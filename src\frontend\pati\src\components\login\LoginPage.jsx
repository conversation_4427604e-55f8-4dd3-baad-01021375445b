import React, { useState } from 'react';
// importando funcoes de estilo do React Native
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  Image, 
  StyleSheet, 
  TouchableWithoutFeedback, 
  Keyboard,
  Alert,
  ActivityIndicator
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAuth } from '../../contexts/AuthContext';

export default function LoginPage({ navigation }) {
  const [focusedInput, setFocusedInput] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');  const { signIn, loading, resetPassword } = useAuth();

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Erro', 'Por favor, preencha todos os campos');
      return;
    }

    if (!isValidEmail(email)) {
      Alert.alert('Erro', 'Por favor, insira um e-mail válido');
      return;
    }    try {      const result = await signIn(email, password);

      if (result.success) {
        // Não precisamos navegar manualmente, o AuthContext cuida da navegação
      } else {
        Alert.alert('Erro de Login', result.error || 'Credenciais inválidas');
      }
    } catch (error) {
      Alert.alert('Erro', 'Ocorreu um erro inesperado. Tente novamente.');
    }
  };

  const handleForgotPassword = async () => {
    if (!email) {
      Alert.alert('Erro', 'Por favor, insira seu e-mail no campo acima');
      return;
    }

    if (!isValidEmail(email)) {
      Alert.alert('Erro', 'Por favor, insira um e-mail válido');
      return;
    }

    try {      const result = await resetPassword(email);

      if (result.success) {
        Alert.alert(
          'E-mail enviado',
          'Verifique sua caixa de entrada para redefinir sua senha.'
        );
      } else {
        Alert.alert('Erro', result.error || 'Erro ao enviar e-mail de recuperação');
      }
    } catch (error) {
      Alert.alert('Erro', 'Ocorreu um erro inesperado. Tente novamente.');
    }
  };

  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={styles.container}>        <View style={styles.logoContainer}>
          <Image style={styles.logo} source={require('../../../assets/images/logopati.png')} />        </View>

        <View style={[styles.formField, styles.emailField]}>
          <TextInput 
            style={[
              styles.inputField, 
              focusedInput === 'email' && styles.inputFieldFocused
            ]} 
            placeholder="E-mail" 
            keyboardType="email-address"
            placeholderTextColor="#000"
            value={email}
            onChangeText={setEmail}
            onFocus={() => setFocusedInput('email')}
            onBlur={() => setFocusedInput(null)}
          />
        </View>        <View style={[styles.formField, styles.passwordField]}>
          <View style={styles.passwordContainer}>
            <TextInput
              style={[
                styles.inputField, 
                styles.passwordInput,
                focusedInput === 'password' && styles.inputFieldFocused
              ]} 
              placeholder="Senha" 
              secureTextEntry={!showPassword}
              placeholderTextColor="#000"
              value={password}
              onChangeText={setPassword}
              onFocus={() => setFocusedInput('password')}
              onBlur={() => setFocusedInput(null)}
            />
            <TouchableOpacity 
              style={styles.eyeIcon} 
              onPress={togglePasswordVisibility}
            >              <Icon 
                name={showPassword ? 'eye-outline' : 'eye-off-outline'} 
                size={24} 
                color="#666" 
              />
            </TouchableOpacity>
          </View>        </View>
        <View style={styles.forgotContainer}>
          <TouchableOpacity onPress={handleForgotPassword}>
            <Text style={styles.forgotPassword}>Esqueci minha senha</Text>
          </TouchableOpacity>        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={[styles.loginBtn, loading && styles.loginBtnDisabled]} 
            onPress={handleLogin}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.loginBtnText}>Login</Text>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.registerContainer}>
          <Text style={styles.registerText}>Não tem uma conta? </Text>
          <TouchableOpacity onPress={() => navigation.navigate('Register')}>
            <Text style={styles.registerLink}>Cadastre-se</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingTop: 40, // Adiciona espaçamento superior para subir o conteúdo
   // Espaçamento superior para evitar sobreposição com a barra de status
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: -120, // Move a logo para cima
  },
  logo: {
    width: 288,
    height: 288,
    resizeMode: 'contain',
  },
  formField: {
    marginBottom: 20,
    width: 288,
  },
  emailField: {
    marginTop: -80, // Puxando o primeiro input para cima
  },
  passwordField: {
    // Removido position absolute
  },  inputField: {
    width: 288,
    height: 50,
    backgroundColor: '#FCF7ED',
    borderRadius: 30,
    borderWidth: 2,
    borderColor: '#EE3480',
    paddingHorizontal: 20,
    fontSize: 14,
    fontWeight: '600',    color: 'black',
  },
  inputFieldFocused: {
    borderColor: '#24A49D',
  },
  forgotContainer: {
    width: 288,
    alignItems: 'flex-start',
    marginBottom: 30,
  },
  forgotPassword: {
    color: '#C81363',
    fontSize: 14,
    fontWeight: '500',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
    marginTop: -10,
  },  loginBtn: {
    width: 118,
    height: 42,
    backgroundColor: '#EE3480',
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loginBtnDisabled: {
    backgroundColor: '#ccc',
  },
  loginBtnText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: '600',
  },
  passwordContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  passwordInput: {
    paddingRight: 50, // Espaço para o ícone do olho
  },  eyeIcon: {
    position: 'absolute',
    right: 15,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  registerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 30,
  },
  registerText: {
    color: '#666',
    fontSize: 14,
  },
  registerLink: {
    color: '#EE3480',
    fontSize: 14,
    fontWeight: '600',
  },
});
