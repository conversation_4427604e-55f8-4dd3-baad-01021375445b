import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import Svg, { Circle, Text as SvgText } from 'react-native-svg';

const Chart = ({ clientsData }) => {
  // Calcular estatísticas dos clientes
  const totalClients = clientsData.length;
  
  if (totalClients === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyChart}>
          <Text style={styles.emptyText}>Nenhum cliente para exibir</Text>
        </View>
      </View>
    );
  }
  const nonCompliantClients = clientsData.filter(client => client.status === 'Inconforme').length;
  const compliantClients = totalClients - nonCompliantClients;
  const nonCompliantPercentage = totalClients > 0 ? Math.round((nonCompliantClients / totalClients) * 100) : 0;
  const compliantPercentage = 100 - nonCompliantPercentage;

  // Configurações do gráfico
  const size = 160;
  const radius = 60;
  const strokeWidth = 20;
  const center = size / 2;
  const circumference = 2 * Math.PI * radius;

  // Calcular o stroke-dasharray para cada segmento
  const nonCompliantStrokeDasharray = `${(nonCompliantPercentage / 100) * circumference} ${circumference}`;
  const compliantStrokeDasharray = `${(compliantPercentage / 100) * circumference} ${circumference}`;
  
  // Offset para posicionar o segundo segmento
  const compliantStrokeDashoffset = -((nonCompliantPercentage / 100) * circumference);

  return (
    <View style={styles.container}>
      <Svg height={size} width={size}>
        {/* Segmento para clientes conformes (azul/verde) */}
        <Circle
          cx={center}
          cy={center}
          r={radius}
          fill="transparent"
          stroke="#24A49D"
          strokeWidth={strokeWidth}
          strokeDasharray={compliantStrokeDasharray}
          strokeDashoffset={compliantStrokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90 ${center} ${center})`}
        />
        
        {/* Segmento para clientes não conformes (rosa) */}
        <Circle
          cx={center}
          cy={center}
          r={radius}
          fill="transparent"
          stroke="#EE3480"
          strokeWidth={strokeWidth}
          strokeDasharray={nonCompliantStrokeDasharray}
          strokeLinecap="round"
          transform={`rotate(-90 ${center} ${center})`}
        />
          {/* Texto central com a porcentagem de não conformes */}
        <SvgText
          x={center}
          y={center - 2}
          textAnchor="middle"
          fontSize="18"
          fontWeight="bold"
          fill="#333"
        >
          {`${nonCompliantPercentage || 0}%`}
        </SvgText>
        <SvgText
          x={center}
          y={center + 14}
          textAnchor="middle"
          fontSize="10"
          fill="#666"
        >
          Inconformes
        </SvgText>
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyChart: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: '#F8F9FA',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#E9ECEF',
    borderStyle: 'dashed',
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});

export default Chart;

