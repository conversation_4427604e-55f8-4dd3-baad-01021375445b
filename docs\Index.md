<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="./assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>

# Nome do Projeto: Pati (Plataforma de Adequação de Tipo de Investidor)

## Nome do Grupo: MMs (Meninas Malvadas)

&emsp; Este documento tem como objetivo elucidar o conteúdo da pasta docs/, explicando a finalidade de cada arquivo e fornecendo um guia para facilitar a navegação.

## Estrutura de Diretórios

Abaixo está a estrutura da pasta `docs/` para facilitar a navegação:

```text
docs/
  ├── assets/                      # Recursos auxiliares (diagramas, logos, etc.)
  │     ...
  │
  ├── standards/                   # Diretrizes para desenvolvimento e repositório
  │     ├── development.md
  │     ├── markdown_writing.md
  │     ├── repo.md
  │     └── git_validators/        # Validadores locais de ações no Git
  │         ├── commit-msg
  │         ├── pre-commit
  │         ├── pre-merge-commit
  │         └── pre-push
  │
  ├── installation_manual.md       # Manual de instalação
  ├── project.md                   # Documentação do projeto (negócios, banco, API, etc.)
  ├── project_management.md        # Monitoramento das sprints e riscos

```

## Gestão do Projeto

- [**project_management.md**](project_management.md): Documentação destinada ao monitoramento das sprints, identificação de riscos e acompanhamento do progresso do projeto.

## Desenvolvimento do Produto

- [**project.md**](project.md): Documentação completa do projeto, abrangendo análise de negócios, diagramas de banco de dados, especificações da API e demais informações essenciais.
- [**installation_manual.md**](installation_manual.md): Manual de instalação detalhado para configurar e executar o projeto corretamente.

## Evolução do Banco de Dados

- [**src/database/index.md**](../src/database/index.md): Documentação consolidada do banco de dados, incluindo dicionário de dados versionado, procedimentos de implantação e rollback.
- **Dicionário de Dados (v1.0)**: Especificação completa das tabelas, campos, tipos e relacionamentos do sistema PATI.
- **Scripts de Migração**: Procedimentos documentados para criação, atualização e rollback do banco de dados.
- **Diagrama ERD**: Representação visual das entidades e relacionamentos (disponível em formato PNG).

## Padronizações e Boas práticas
- [**standards/markdown_writing.md**](./standards/markdown_writing.md): Regras para escrita em Markdown, incluindo padrões para inserção de imagens, referências e formatação geral.
- [**standards/repo.md**](./standards/repo.md): Diretrizes para organização do repositório, incluindo padrões de Pull Requests, nomenclatura de arquivos, estruturação de diretórios, mensagens de commits, entre outros.
- [**standards/development.md**](./standards/development.md): Diretrizes de desenvolvimento para garantir boas práticas e coerência no código.

## Recursos

&emsp; A pasta `assets/` contém arquivos auxiliares como diagramas e logos, úteis para a documentação e compreensão do projeto.