using System.ComponentModel.DataAnnotations;

namespace ApiMlRecommender.DTOs
{
    public class InvestimentoDto
    {
        [Required(ErrorMessage = "Código do investimento é obrigatório")]
        public string Codigo { get; set; } = string.Empty;

        [Required(ErrorMessage = "Nome do investimento é obrigatório")]
        public string Nome { get; set; } = string.Empty;

        [Required(ErrorMessage = "Categoria é obrigatória")]
        public string Categoria { get; set; } = string.Empty;

        public string? Subcategoria { get; set; }

        [Required(ErrorMessage = "Tipo de ativo é obrigatório")]
        public string TipoAtivo { get; set; } = string.Empty;

        [Required(ErrorMessage = "Nível de risco é obrigatório")]
        [Range(1, 5, ErrorMessage = "Nível de risco deve estar entre 1 e 5")]
        public int NivelRisco { get; set; }

        public string Risco => NivelRisco switch
        {
            1 => "Conservador",
            3 => "Moderado",
            4 => "Arrojado",
            5 => "Sofisticado",
            _ => "Desconhecido"
        };

        [Required(ErrorMessage = "Valor atual é obrigatório")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Valor atual deve ser maior que zero")]
        public decimal ValorAtual { get; set; }

        public decimal PercentualCarteira { get; set; }

        public decimal? RentabilidadeEsperada { get; set; }
    }
}