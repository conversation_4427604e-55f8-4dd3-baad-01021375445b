using System;
using System.ComponentModel.DataAnnotations;

namespace Pati.ClassificationService.Domain
{
    /// <summary>
    /// Entidade que representa uma regra de classificação de carteira
    /// </summary>
    public class ClassificationRule
    {
        public int RuleId { get; set; }
        
        [Required]
        [StringLength(30)]
        public string RiskProfile { get; set; } = string.Empty;
        
        [Required]
        [StringLength(200)]
        public string AllowedAssetTypes { get; set; } = string.Empty;
        
        [Range(0, 100)]
        public decimal MaxAllocationPercentage { get; set; }
        
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedAt { get; set; }

        // Construtor padrão
        public ClassificationRule() { }

        public ClassificationRule(string riskProfile, string allowedAssetTypes, decimal maxAllocationPercentage, string description)
        {
            if (string.IsNullOrWhiteSpace(riskProfile))
                throw new ArgumentException("Risk profile is required.", nameof(riskProfile));
            
            if (string.IsNullOrWhiteSpace(allowedAssetTypes))
                throw new ArgumentException("Allowed asset types are required.", nameof(allowedAssetTypes));

            if (maxAllocationPercentage < 0 || maxAllocationPercentage > 100)
                throw new ArgumentException("Max allocation percentage must be between 0 and 100.", nameof(maxAllocationPercentage));

            RiskProfile = riskProfile;
            AllowedAssetTypes = allowedAssetTypes;
            MaxAllocationPercentage = maxAllocationPercentage;
            Description = description ?? string.Empty;
            IsActive = true;
            CreatedAt = DateTime.Now;
        }

        /// <summary>
        /// Verifica se um tipo de ativo é permitido para esta regra
        /// </summary>
        public bool IsAssetTypeAllowed(string assetType)
        {
            if (string.IsNullOrWhiteSpace(assetType) || string.IsNullOrWhiteSpace(AllowedAssetTypes))
                return false;

            var allowedTypes = AllowedAssetTypes.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                              .Select(t => t.Trim().ToLower())
                                              .ToArray();

            return allowedTypes.Contains(assetType.Trim().ToLower());
        }

        /// <summary>
        /// Atualiza a regra
        /// </summary>
        public void Update(string? allowedAssetTypes = null, decimal? maxAllocationPercentage = null, string? description = null, bool? isActive = null)
        {
            if (!string.IsNullOrWhiteSpace(allowedAssetTypes))
                AllowedAssetTypes = allowedAssetTypes;

            if (maxAllocationPercentage.HasValue)
            {
                if (maxAllocationPercentage.Value < 0 || maxAllocationPercentage.Value > 100)
                    throw new ArgumentException("Max allocation percentage must be between 0 and 100.");
                MaxAllocationPercentage = maxAllocationPercentage.Value;
            }

            if (description != null)
                Description = description;

            if (isActive.HasValue)
                IsActive = isActive.Value;

            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// Desativa a regra
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// Ativa a regra
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.Now;
        }

        public override string ToString()
        {
            return $"Rule {RuleId}: {RiskProfile} - {AllowedAssetTypes} (Max: {MaxAllocationPercentage}%)";
        }
    }
}
