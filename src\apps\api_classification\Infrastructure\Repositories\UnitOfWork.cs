using Microsoft.EntityFrameworkCore.Storage;
using Pati.ClassificationService.Application.Repositories;
using Pati.ClassificationService.Infrastructure.Data;
using System.Threading.Tasks;

namespace Pati.ClassificationService.Infrastructure.Repositories
{
    public class UnitOfWork : IUnitOfWork, IDisposable
    {
        private readonly ClassificationDbContext _context;
        private IDbContextTransaction? _transaction;

        private IClientRepository? _clients;
        private IInvestmentRepository? _investments;
        private IAdvisorRepository? _advisors;
        private IClientRecommendationRepository? _clientRecommendations;
        private IClassificationRuleRepository? _classificationRules;

        public UnitOfWork(ClassificationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public IClientRepository Clients
        {
            get { return _clients ??= new ClientRepository(_context); }
        }

        public IInvestmentRepository Investments
        {
            get { return _investments ??= new InvestmentRepository(_context); }
        }

        public IAdvisorRepository Advisors
        {
            get { return _advisors ??= new AdvisorRepository(_context); }
        }

        public IClientRecommendationRepository ClientRecommendations
        {
            get { return _clientRecommendations ??= new ClientRecommendationRepository(_context); }
        }

        public IClassificationRuleRepository ClassificationRules
        {
            get { return _classificationRules ??= new ClassificationRuleRepository(_context); }
        }

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            if (_transaction != null)
            {
                throw new InvalidOperationException("A transaction is already in progress.");
            }

            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction in progress.");
            }

            try
            {
                await _context.SaveChangesAsync();
                await _transaction.CommitAsync();
            }
            catch
            {
                await _transaction.RollbackAsync();
                throw;
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction in progress.");
            }

            try
            {
                await _transaction.RollbackAsync();
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}
