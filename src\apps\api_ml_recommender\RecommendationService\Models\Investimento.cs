namespace RecommendationService.Models
{
    public class Investimento
    {
        public string NomeAtivo { get; set; } = string.Empty;
        public decimal ValorInvestido { get; set; }
        public decimal ValorNormalizado { get; set; }
        public TipoAtivo TipoAtivo { get; set; }
        public int RiscoAtivo { get; set; } 
        public decimal PercentualCarteira { get; set; } 

        // Propriedade calculada para compatibilidade com PerfilRisco
        public PerfilRisco Risco => RiscoAtivo switch
        {
            1 => PerfilRisco.Conservador,
            3 => PerfilRisco.Moderado,
            4 => PerfilRisco.Arrojado,
            5 => PerfilRisco.Sofisticado,
            _ => PerfilRisco.Conservador 
        };
    }

    public enum TipoAtivo
    {
        RendaFixa = 1,      // CDBs, Tesouro, LCIs, etc.
        RendaVariavel = 2   // Ações, Fundos de Ações, etc.
    }
}