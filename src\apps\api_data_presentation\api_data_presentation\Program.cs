using api_data_presentation.Repositories.Interfaces;
using api_data_presentation.Repositories.MongoDB;
using api_data_presentation.Repositories.PostgreSQL;
using api_data_presentation.Services;
using api_data_presentation.Services.Interfaces;
using Microsoft.Extensions.Logging;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddScoped<IAdvisorService, AdvisorService>();

// Configuração do ClientRepository
builder.Services.AddScoped<IClientRepository>(provider =>
{
    var configuration = provider.GetRequiredService<IConfiguration>();
    var logger = provider.GetRequiredService<ILogger<ClientRepository>>();
    
    var postgresConnectionString = configuration.GetConnectionString("PostgreSQL");
    if (string.IsNullOrEmpty(postgresConnectionString))
    {
        postgresConnectionString = "Host=localhost;Database=mean_girls;Username=inteli;Password=**********";
    }
    
    logger.LogInformation("Configurando PostgreSQL com string de conexão: {ConnectionString}", postgresConnectionString);
    
    return new ClientRepository(configuration);
});

// Configuração do RecommendationRepository
builder.Services.AddScoped<IRecommendationRepository>(provider =>
{
    var configuration = provider.GetRequiredService<IConfiguration>();
    var logger = provider.GetRequiredService<ILogger<RecommendationRepository>>();
    
    var mongoConnectionString = configuration.GetConnectionString("MongoDB");
    if (string.IsNullOrEmpty(mongoConnectionString))
    {
        mongoConnectionString = "*******************************************************************************";
    }
    
    var mongoDatabaseName = configuration["MongoDB:DatabaseName"];
    if (string.IsNullOrEmpty(mongoDatabaseName))
    {
        mongoDatabaseName = "mean_girls_mongo";
    }
    
    logger.LogInformation("Configurando MongoDB com string de conexão: {ConnectionString}", mongoConnectionString);
    logger.LogInformation("Nome do banco de dados MongoDB: {DatabaseName}", mongoDatabaseName);
    
    return new RecommendationRepository(mongoConnectionString, mongoDatabaseName, logger);
});

if (!builder.Environment.IsEnvironment("Testing"))
{
    DotNetEnv.Env.Load();

    var postgresConnectionString = Environment.GetEnvironmentVariable("ConnectionStrings__PostgreSQL");
    var mongoConnectionString = Environment.GetEnvironmentVariable("ConnectionStrings__MongoDB");

    if (!string.IsNullOrEmpty(postgresConnectionString))
    {
        builder.Configuration["ConnectionStrings:PostgreSQL"] = postgresConnectionString;
    }

    if (!string.IsNullOrEmpty(mongoConnectionString))
    {
        builder.Configuration["ConnectionStrings:MongoDB"] = mongoConnectionString;
    }
}

var app = builder.Build();

if (!app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseAuthorization();
app.MapControllers();

app.Run();

// Torna a classe Program pública para os testes de integração
public partial class Program { }
