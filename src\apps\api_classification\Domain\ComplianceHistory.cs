using System;
using System.ComponentModel.DataAnnotations;

namespace Pati.ClassificationService.Domain
{
    /// <summary>
    /// Entidade para histórico de mudanças de compliance dos clientes
    /// </summary>
    public class ComplianceHistory
    {
        /// <summary>
        /// Identificador único do histórico
        /// </summary>
        public int HistoryId { get; set; }

        /// <summary>
        /// Identificador do cliente
        /// </summary>
        [Required]
        public int ClientId { get; set; }

        /// <summary>
        /// Status anterior de compliance
        /// </summary>
        [Required]
        public bool PreviousStatus { get; set; }

        /// <summary>
        /// Novo status de compliance
        /// </summary>
        [Required]
        public bool NewStatus { get; set; }

        /// <summary>
        /// Data da mudança
        /// </summary>
        [Required]
        public DateTime ChangeDate { get; set; }

        // Campos removidos: Reason, PreviousRiskProfile, NewRiskProfile
        // Estes campos não existem no esquema real do banco
        // O banco só tem: history_id, client_id, previous_status, new_status, change_date

        /// <summary>
        /// Construtor padrão
        /// </summary>
        public ComplianceHistory()
        {
            ChangeDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Construtor com parâmetros - apenas campos que existem no banco
        /// </summary>
        public ComplianceHistory(int clientId, bool previousStatus, bool newStatus)
        {
            ClientId = clientId;
            PreviousStatus = previousStatus;
            NewStatus = newStatus;
            ChangeDate = DateTime.UtcNow;
        }
    }
}
