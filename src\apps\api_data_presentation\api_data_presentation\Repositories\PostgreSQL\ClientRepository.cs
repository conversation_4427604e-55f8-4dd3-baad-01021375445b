using api_data_presentation.Models.Entities;
using api_data_presentation.Models.QueryParameters;
using api_data_presentation.Repositories.Interfaces;
using Npgsql;
using Microsoft.Extensions.Configuration;

namespace api_data_presentation.Repositories.PostgreSQL
{
    public class ClientRepository : IClientRepository
    {
        private readonly string _connectionString;

        public ClientRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("PostgreSQL");
            if (string.IsNullOrEmpty(_connectionString))
            {
                _connectionString = "Host=localhost;Database=mean_girls;Username=inteli;Password=**********";
            }
        }

        public async Task<(int Conform, int Inconform)> GetComplianceStatsByAdvisorIdAsync(int advisorId)
        {
            const string query = @"
                SELECT
                    SUM(CASE WHEN compliance = true THEN 1 ELSE 0 END) AS conform,
                    SUM(CASE WHEN compliance = false THEN 1 ELSE 0 END) AS inconform
                FROM client
                WHERE advisor_id = @AdvisorId;
            ";

            await using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();
            using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddWithValue("@AdvisorId", advisorId);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return (
                    Conform: reader.GetInt32(0),
                    Inconform: reader.GetInt32(1)
                );
            }

            return (0, 0);
        }

        public async Task<List<(Client Client, decimal TotalInvested)>> GetClientsByAdvisorIdAsync(int advisorId, ClientListQueryParams queryParams)
        {
            var query = @"
                SELECT 
                    c.client_id AS ClientId,
                    c.name AS Name,
                    c.phone_number AS PhoneNumber,
                    c.email AS Email,
                    c.risk_profile_wallet AS RiskProfileWallet,
                    c.risk_profile_form AS RiskProfileForm,
                    c.compliance AS Compliance,
                    c.advisor_id AS AdvisorId,
                    c.created_at AS CreatedAt,
                    c.updated_at AS UpdatedAt,
                    COALESCE(SUM(ci.invested_amount), 0) AS TotalInvested
                FROM client c
                LEFT JOIN client_investment ci ON c.client_id = ci.client_id
                WHERE c.advisor_id = @AdvisorId
                AND (@RiskProfileWallet IS NULL OR c.risk_profile_wallet = @RiskProfileWallet)
                AND (@Compliance IS NULL OR c.compliance = @Compliance)
                AND (@RiskProfileForm IS NULL OR c.risk_profile_form = @RiskProfileForm)
                GROUP BY c.client_id
                ORDER BY {0}
                OFFSET @Offset ROWS
                LIMIT @Limit";

            var formattedQuery = string.Format(query, queryParams.GetOrderByClause());

            var clients = new List<(Client Client, decimal TotalInvested)>();

            await using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();
            using var command = new NpgsqlCommand(formattedQuery, connection);
            command.Parameters.AddWithValue("@AdvisorId", advisorId);
            command.Parameters.Add("@RiskProfileWallet", NpgsqlTypes.NpgsqlDbType.Varchar).Value = (object?)queryParams.RiskProfileWallet ?? DBNull.Value;
            command.Parameters.Add("@Compliance", NpgsqlTypes.NpgsqlDbType.Boolean).Value = (object?)queryParams.Compliance ?? DBNull.Value;
            command.Parameters.Add("@RiskProfileForm", NpgsqlTypes.NpgsqlDbType.Varchar).Value = (object?)queryParams.RiskProfileForm ?? DBNull.Value;
            command.Parameters.Add("@Offset", NpgsqlTypes.NpgsqlDbType.Integer).Value = queryParams.Offset ?? 0;
            command.Parameters.Add("@Limit", NpgsqlTypes.NpgsqlDbType.Integer).Value = queryParams.Limit ?? 10;

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var client = new Client
                {
                    ClientId = reader.GetInt32(reader.GetOrdinal("ClientId")),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    PhoneNumber = reader.IsDBNull(reader.GetOrdinal("PhoneNumber")) ? null : reader.GetString(reader.GetOrdinal("PhoneNumber")),
                    Email = reader.GetString(reader.GetOrdinal("Email")),
                    RiskProfileWallet = reader.GetString(reader.GetOrdinal("RiskProfileWallet")),
                    RiskProfileForm = reader.GetString(reader.GetOrdinal("RiskProfileForm")),
                    Compliance = reader.GetBoolean(reader.GetOrdinal("Compliance")),
                    AdvisorId = reader.GetInt32(reader.GetOrdinal("AdvisorId")),
                    CreatedAt = reader.GetDateTime(reader.GetOrdinal("CreatedAt")),
                    UpdatedAt = reader.GetDateTime(reader.GetOrdinal("UpdatedAt"))
                };

                var totalInvested = reader.GetDecimal(reader.GetOrdinal("TotalInvested"));

                clients.Add((client, totalInvested));
            }

            return clients;
        }

        public async Task<Advisor?> GetAdvisorByUidAsync(string uid)
        {
            const string query = @"
                SELECT advisor_id, uid, created_at, updated_at
                FROM advisor
                WHERE uid = @Uid
                LIMIT 1;
            ";

            await using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddWithValue("@Uid", uid);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new Advisor
                {
                    AdvisorId = reader.GetInt32(reader.GetOrdinal("advisor_id")),
                    Uid = reader.GetString(reader.GetOrdinal("uid")),
                    CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                    UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                };
            }

            return null;
        }

        public async Task<int> CreateAdvisorAsync(string uid)
        {
            const string query = @"
                INSERT INTO advisor (uid, created_at, updated_at)
                VALUES (@Uid, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                RETURNING advisor_id;
            ";

            await using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddWithValue("@Uid", uid);

            var advisorId = await command.ExecuteScalarAsync();
            return Convert.ToInt32(advisorId);
        }

        public async Task<List<ClientInvestment>> GetClientInvestmentsAsync(int clientId, InvestmentQueryParams queryParams)
        {
            var query = @"
                SELECT ci.client_id, ci.investment_id, ci.investment_date, ci.invested_amount,
                       i.name AS investment_name, i.type AS investment_type, i.risk AS investment_risk
                FROM client_investment ci
                INNER JOIN investment i ON ci.investment_id = i.investment_id
                WHERE ci.client_id = @ClientId";

            if (!string.IsNullOrEmpty(queryParams.InvestmentType))
                query += " AND i.type = @InvestmentType";

            if (!string.IsNullOrEmpty(queryParams.InvestmentRisk))
                query += " AND i.risk = @InvestmentRisk";

            query += $" ORDER BY {queryParams.GetOrderByClause()}";

            if (queryParams.Offset.HasValue)
                query += " OFFSET @Offset";

            if (queryParams.Limit.HasValue)
                query += " LIMIT @Limit";

            var investments = new List<ClientInvestment>();

            await using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddWithValue("@ClientId", clientId);

            if (!string.IsNullOrEmpty(queryParams.InvestmentType))
                command.Parameters.AddWithValue("@InvestmentType", queryParams.InvestmentType);
            else
                command.Parameters.AddWithValue("@InvestmentType", DBNull.Value);

            if (!string.IsNullOrEmpty(queryParams.InvestmentRisk))
                command.Parameters.AddWithValue("@InvestmentRisk", queryParams.InvestmentRisk);
            else
                command.Parameters.AddWithValue("@InvestmentRisk", DBNull.Value);

            command.Parameters.AddWithValue("@Offset", queryParams.Offset ?? 0);
            command.Parameters.AddWithValue("@Limit", queryParams.Limit ?? 10);

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var clientInvestment = new ClientInvestment
                {
                    ClientId = reader.GetInt32(reader.GetOrdinal("client_id")),
                    InvestmentId = reader.GetInt32(reader.GetOrdinal("investment_id")),
                    InvestmentDate = reader.GetDateTime(reader.GetOrdinal("investment_date")),
                    InvestedAmount = reader.GetDecimal(reader.GetOrdinal("invested_amount")),
                    Investment = new Investment
                    {
                        Name = reader.GetString(reader.GetOrdinal("investment_name")),
                        Type = reader.GetString(reader.GetOrdinal("investment_type")),
                        Risk = reader.IsDBNull(reader.GetOrdinal("investment_risk")) ? null : reader.GetString(reader.GetOrdinal("investment_risk"))
                    }
                };

                investments.Add(clientInvestment);
            }

            return investments;
        }

        public async Task<Client?> GetClientByIdAsync(int clientId)
        {
            const string query = "SELECT * FROM CLIENT WHERE client_id = @ClientId";

            await using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddWithValue("@ClientId", clientId);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new Client
                {
                    ClientId = reader.GetInt32(reader.GetOrdinal("client_id")),
                    Name = reader.GetString(reader.GetOrdinal("name")),
                    PhoneNumber = reader.IsDBNull(reader.GetOrdinal("phone_number")) ? null : reader.GetString(reader.GetOrdinal("phone_number")),
                    Email = reader.GetString(reader.GetOrdinal("email")),
                    RiskProfileWallet = reader.GetString(reader.GetOrdinal("risk_profile_wallet")),
                    RiskProfileForm = reader.GetString(reader.GetOrdinal("risk_profile_form")),
                    Compliance = reader.GetBoolean(reader.GetOrdinal("compliance")),
                    AdvisorId = reader.GetInt32(reader.GetOrdinal("advisor_id")),
                    CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                    UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                };
            }

            return null;
        }

        public async Task<Investment?> GetInvestmentByIdAsync(int investmentId)
        {
            const string query = "SELECT * FROM INVESTMENT WHERE investment_id = @InvestmentId";

            await using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddWithValue("@InvestmentId", investmentId);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new Investment
                {
                    InvestmentId = reader.GetInt32(reader.GetOrdinal("investment_id")),
                    Name = reader.GetString(reader.GetOrdinal("name")),
                    Type = reader.GetString(reader.GetOrdinal("type")),
                    Risk = reader.IsDBNull(reader.GetOrdinal("risk")) ? null : reader.GetString(reader.GetOrdinal("risk"))
                };
            }

            return null;
        }
    }
}