{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Urls": "http://localhost:8080", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=mean_girls;Username=inteli;Password=**********;"}, "ServiceConfiguration": {"BaseUrl": "http://localhost:8080", "ServiceName": "Pati.ClassificationService", "Version": "1.0.0"}, "SVDModel": {"ModelPath": "/app/models/modelo_svd_treinado.pkl", "PythonServiceUrl": "http://localhost:5000", "MinSuitabilityThreshold": 0.4, "DefaultRecommendationCount": 5, "EnableModelCaching": true, "PythonServiceTimeoutSeconds": 10}, "RecommendationService": {"BaseUrl": "http://localhost:5001", "TimeoutSeconds": 30}, "ClassificationJob": {"Enabled": true, "IntervalMinutes": 1440, "RunOnStartup": false, "TimeoutMinutes": 60, "DailyExecutionTime": "02:00"}, "DataFiles": {"InconformidadesPath": "src/database/inconformidades.csv", "TipoAtivosPath": "src/database/Tipo_ativos_com_risco.csv"}}