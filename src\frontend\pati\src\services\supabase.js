import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';

// Configurações do Supabase
// As variáveis de ambiente devem ser definidas no arquivo .env
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://your-project-url.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

if (supabaseUrl === 'https://your-project-url.supabase.co' || supabaseAnonKey === 'your-anon-key') {
  console.warn('⚠️  Configure as variáveis de ambiente do Supabase no arquivo .env');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
