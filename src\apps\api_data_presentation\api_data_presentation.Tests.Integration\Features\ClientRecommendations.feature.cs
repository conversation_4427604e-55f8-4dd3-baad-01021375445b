﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by SpecFlow (https://www.specflow.org/).
//      SpecFlow Version:3.9.0.0
//      SpecFlow Generator Version:3.9.0.0
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
namespace api_data_presentation.Tests.Integration.Features
{
    using TechTalk.SpecFlow;
    using System;
    using System.Linq;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("TechTalk.SpecFlow", "3.9.0.0")]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public partial class ObterRecomendaEsDeInvestimentosParaClienteFeature : object, Xunit.IClassFixture<ObterRecomendaEsDeInvestimentosParaClienteFeature.FixtureData>, System.IDisposable
    {
        
        private static TechTalk.SpecFlow.ITestRunner testRunner;
        
        private static string[] featureTags = ((string[])(null));
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "ClientRecommendations.feature"
#line hidden
        
        public ObterRecomendaEsDeInvestimentosParaClienteFeature(ObterRecomendaEsDeInvestimentosParaClienteFeature.FixtureData fixtureData, api_data_presentation_Tests_Integration_XUnitAssemblyFixture assemblyFixture, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
            this.TestInitialize();
        }
        
        public static void FeatureSetup()
        {
            testRunner = TechTalk.SpecFlow.TestRunnerManager.GetTestRunner();
            TechTalk.SpecFlow.FeatureInfo featureInfo = new TechTalk.SpecFlow.FeatureInfo(new System.Globalization.CultureInfo("pt-BR"), "Features", "Obter Recomenda��es de Investimentos para Cliente", @"    Como um assessor de investimentos
    Eu quero obter recomenda��es personalizadas para um cliente espec�fico
    Para que eu possa realinhar sua carteira de acordo com seu perfil de risco

    Cen�rio: Buscar recomenda��es para cliente com perfil desalinhado
        Dado que existe um cliente cadastrado com ID 123
        E o cliente possui perfil de risco ""Moderado"" no formul�rio
        E o cliente possui perfil de risco ""Conservador"" calculado pela carteira
        E existem recomenda��es de investimentos para este cliente no MongoDB
        Quando eu fa�o uma requisi��o GET para ""/advisor/clients/123/recommendations""
        Ent�o a resposta deve ter status 200
        E a resposta deve conter os dados b�sicos do cliente
        E a resposta deve conter uma lista de investimentos recomendados
        E cada investimento recomendado deve ter um score de adequa��o ", ProgrammingLanguage.CSharp, featureTags);
            testRunner.OnFeatureStart(featureInfo);
        }
        
        public static void FeatureTearDown()
        {
            testRunner.OnFeatureEnd();
            testRunner = null;
        }
        
        public void TestInitialize()
        {
        }
        
        public void TestTearDown()
        {
            testRunner.OnScenarioEnd();
        }
        
        public void ScenarioInitialize(TechTalk.SpecFlow.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public void ScenarioStart()
        {
            testRunner.OnScenarioStart();
        }
        
        public void ScenarioCleanup()
        {
            testRunner.CollectScenarioErrors();
        }
        
        void System.IDisposable.Dispose()
        {
            this.TestTearDown();
        }
        
        [System.CodeDom.Compiler.GeneratedCodeAttribute("TechTalk.SpecFlow", "3.9.0.0")]
        [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : System.IDisposable
        {
            
            public FixtureData()
            {
                ObterRecomendaEsDeInvestimentosParaClienteFeature.FeatureSetup();
            }
            
            void System.IDisposable.Dispose()
            {
                ObterRecomendaEsDeInvestimentosParaClienteFeature.FeatureTearDown();
            }
        }
    }
}
#pragma warning restore
#endregion
