using Pati.ClassificationService.DTOs;

namespace Pati.ClassificationService.Application.Services
{
    /// <summary>
    /// Interface para comunicação com o serviço de recomendações
    /// </summary>
    public interface IRecommendationServiceClient
    {
        /// <summary>
        /// Envia análise de carteira para o serviço de recomendações
        /// </summary>
        /// <param name="portfolioOutput">Resultado da análise de carteira</param>
        /// <returns>True se enviado com sucesso</returns>
        Task<bool> SendPortfolioAnalysisAsync(PortfolioOutputDto portfolioOutput);

        /// <summary>
        /// Verifica se o serviço de recomendações está disponível
        /// </summary>
        /// <returns>True se o serviço está disponível</returns>
        Task<bool> IsServiceAvailableAsync();
    }
}
