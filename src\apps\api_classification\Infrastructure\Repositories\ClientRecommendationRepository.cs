using Microsoft.EntityFrameworkCore;
using Pati.ClassificationService.Application.Repositories;
using Pati.ClassificationService.Domain;
using Pati.ClassificationService.Infrastructure.Data;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pati.ClassificationService.Infrastructure.Repositories
{
    public class ClientRecommendationRepository : IClientRecommendationRepository
    {
        private readonly ClassificationDbContext _context;

        public ClientRecommendationRepository(ClassificationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<ClientRecommendation?> GetByIdAsync(int recommendationId)
        {
            return await _context.ClientRecommendations
                .FirstOrDefaultAsync(r => r.RecommendationId == recommendationId);
        }

        public async Task<IEnumerable<ClientRecommendation>> GetByClientIdAsync(int clientId)
        {
            return await _context.ClientRecommendations
                .Where(r => r.ClientId == clientId)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }

        public async Task AddAsync(ClientRecommendation recommendation)
        {
            if (recommendation == null)
                throw new ArgumentNullException(nameof(recommendation));

            await _context.ClientRecommendations.AddAsync(recommendation);
        }

        public async Task UpdateAsync(ClientRecommendation recommendation)
        {
            if (recommendation == null)
                throw new ArgumentNullException(nameof(recommendation));

            _context.ClientRecommendations.Update(recommendation);
            await Task.CompletedTask;
        }

        public async Task DeleteAsync(int recommendationId)
        {
            var recommendation = await _context.ClientRecommendations.FindAsync(recommendationId);
            if (recommendation != null)
            {
                _context.ClientRecommendations.Remove(recommendation);
            }
        }
    }
}
