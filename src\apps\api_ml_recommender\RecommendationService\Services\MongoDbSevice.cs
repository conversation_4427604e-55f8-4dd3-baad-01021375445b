using MongoDB.Bson;
using MongoDB.Driver;
using Microsoft.Extensions.Configuration;
using ApiMlRecommender.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ApiMlRecommender.Services
{
    public class MongoDbService
    {
        private readonly IMongoCollection<RecomendacaoInvestimento> _recomendacoes;

        public MongoDbService(IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("MongoDb");
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new ArgumentNullException("MongoDb connection string not found.");
            }
            var client = new MongoClient(connectionString);
            var databaseName = configuration.GetSection("MongoDbSettings:DatabaseName").Value;
            var recomendacoesCollectionName = configuration.GetSection("MongoDbSettings:RecomendacoesCollectionName").Value;
            var database = client.GetDatabase(databaseName);
            _recomendacoes = database.GetCollection<RecomendacaoInvestimento>(recomendacoesCollectionName);
        }

        public async Task SaveRecomendacaoAsync(RecomendacaoInvestimento recomendacao)
        {
            recomendacao.RecommendationDate = DateTime.UtcNow;
            await _recomendacoes.InsertOneAsync(recomendacao);
        }

        public async Task<RecomendacaoInvestimento?> GetRecomendacaoClienteAsync(string clientId)
        {
            var filter = Builders<RecomendacaoInvestimento>.Filter.Eq(r => r.ClientId, clientId);
            return await _recomendacoes.Find(filter).SortByDescending(r => r.RecommendationDate).FirstOrDefaultAsync();
        }

        public async Task<bool> CheckConnectionAsync()
        {
            try
            {
                await _recomendacoes.Database.RunCommandAsync((Command<BsonDocument>)"{ping:1}");
                return true;
            }
            catch (Exception ex)
            {   
                Console.WriteLine($"MongoDB connection error: {ex.Message}");
                return false;
            }
        }
    }

    public class RecomendacaoInvestimento
    {
        public string ClientId { get; set; } = string.Empty;
        public List<InvestmentRecommendation> Investments { get; set; } = new List<InvestmentRecommendation>();
        public DateTime RecommendationDate { get; set; }
    }

    public class InvestmentRecommendation
    {
        public string InvestmentId { get; set; } = string.Empty;
        public double Score { get; set; }
    }
}