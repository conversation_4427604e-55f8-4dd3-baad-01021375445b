using System;
using System.ComponentModel.DataAnnotations;

namespace Pati.ClassificationService.Domain
{
    /// <summary>
    /// Entidade que representa uma inconformidade identificada na carteira
    /// </summary>
    public class Inconsistency
    {
        public string AssetId { get; private set; } = string.Empty;
        public string AssetName { get; private set; } = string.Empty;
        public string Name { get; private set; } = string.Empty;
        public string Description { get; private set; } = string.Empty;
        public string Severity { get; private set; } = string.Empty;
        public DateTime IdentifiedAt { get; private set; }

        // Construtor padrão para Entity Framework
        protected Inconsistency() { }

        public Inconsistency(string assetId, string name, string description, string severity)
        {
            if (string.IsNullOrWhiteSpace(assetId))
                throw new ArgumentException("Asset ID is required.", nameof(assetId));
            
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Asset name is required.", nameof(name));
            
            if (string.IsNullOrWhiteSpace(description))
                throw new ArgumentException("Description is required.", nameof(description));
            
            if (string.IsNullOrWhiteSpace(severity))
                throw new ArgumentException("Severity is required.", nameof(severity));

            var validSeverities = new[] { "low", "medium", "high" };
            if (!validSeverities.Contains(severity.ToLower()))
                throw new ArgumentException("Invalid severity level.", nameof(severity));

            AssetId = assetId;
            AssetName = name;
            Name = name;
            Description = description;
            Severity = severity.ToLower();
            IdentifiedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Verifica se a inconformidade é de alta severidade
        /// </summary>
        public bool IsHighSeverity()
        {
            return Severity == "high";
        }

        /// <summary>
        /// Verifica se a inconformidade é de média severidade
        /// </summary>
        public bool IsMediumSeverity()
        {
            return Severity == "medium";
        }

        /// <summary>
        /// Verifica se a inconformidade é de baixa severidade
        /// </summary>
        public bool IsLowSeverity()
        {
            return Severity == "low";
        }

        /// <summary>
        /// Obtém a prioridade numérica da severidade (para ordenação)
        /// </summary>
        public int GetSeverityPriority()
        {
            return Severity switch
            {
                "high" => 3,
                "medium" => 2,
                "low" => 1,
                _ => 0
            };
        }

        /// <summary>
        /// Cria uma inconformidade para ativo incompatível com perfil da carteira
        /// </summary>
        public static Inconsistency CreateProfileIncompatibility(string assetId, string assetName, 
            string assetProfile, string portfolioProfile, string incomeType)
        {
            var description = $"Ativo de {incomeType} com perfil {assetProfile} incompatível com perfil {portfolioProfile}";
            var severity = CalculateProfileIncompatibilitySeverity(portfolioProfile, assetProfile);
            
            return new Inconsistency(assetId, assetName, description, severity);
        }

        /// <summary>
        /// Cria uma inconformidade para proporção inadequada de renda
        /// </summary>
        public static Inconsistency CreateAllocationInconsistency(string incomeType, 
            decimal currentProportion, decimal idealProportion, string portfolioProfile)
        {
            var description = $"Proporção de {incomeType} ({currentProportion:P1}) fora do ideal para perfil {portfolioProfile} ({idealProportion:P1})";
            var difference = Math.Abs(currentProportion - idealProportion);
            var severity = difference > 0.3m ? "high" : "medium";
            
            return new Inconsistency("PORTFOLIO_ALLOCATION", "Alocação da Carteira", description, severity);
        }

        /// <summary>
        /// Calcula a severidade baseado na incompatibilidade entre perfis
        /// </summary>
        private static string CalculateProfileIncompatibilitySeverity(string portfolioProfile, string assetProfile)
        {
            var profileRanking = new Dictionary<string, int>
            {
                { "Conservador", 1 },
                { "Moderado", 2 },
                { "Arrojado", 3 },
                { "Sofisticado", 4 }
            };

            if (!profileRanking.ContainsKey(portfolioProfile) || !profileRanking.ContainsKey(assetProfile))
                return "medium";

            var portfolioRank = profileRanking[portfolioProfile];
            var assetRank = profileRanking[assetProfile];
            var difference = Math.Abs(portfolioRank - assetRank);

            return difference switch
            {
                0 => "low",
                1 => "medium",
                >= 2 => "high",
                _ => "medium"
            };
        }

        public override string ToString()
        {
            return $"[{Severity.ToUpper()}] {AssetId} - {Name}: {Description}";
        }

        public override bool Equals(object? obj)
        {
            if (obj is Inconsistency other)
                return AssetId == other.AssetId && Description == other.Description;
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(AssetId, Description);
        }
    }
}
