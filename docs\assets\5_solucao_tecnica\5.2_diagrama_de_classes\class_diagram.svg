<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="811px" preserveAspectRatio="none" style="width:1690px;height:811px;background:#FFFFFF;" version="1.1" viewBox="0 0 1690 811" width="1690px" zoomAndPan="magnify"><defs/><g><!--MD5=[ed6624c6556d5e7b1bd23032162d20b7]
class Client--><g id="elem_Client"><rect codeLine="1" fill="#F1F1F1" height="210.9688" id="Client" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="251" x="474" y="371"/><ellipse cx="575.75" cy="387" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M578.7188,392.6406 Q578.1406,392.9375 577.5,393.0781 Q576.8594,393.2344 576.1563,393.2344 Q573.6563,393.2344 572.3281,391.5938 Q571.0156,389.9375 571.0156,386.8125 Q571.0156,383.6875 572.3281,382.0313 Q573.6563,380.375 576.1563,380.375 Q576.8594,380.375 577.5,380.5313 Q578.1563,380.6875 578.7188,380.9844 L578.7188,383.7031 Q578.0938,383.125 577.5,382.8594 Q576.9063,382.5781 576.2813,382.5781 Q574.9375,382.5781 574.25,383.6563 Q573.5625,384.7188 573.5625,386.8125 Q573.5625,388.9063 574.25,389.9844 Q574.9375,391.0469 576.2813,391.0469 Q576.9063,391.0469 577.5,390.7813 Q578.0938,390.5 578.7188,389.9219 L578.7188,392.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="39" x="596.25" y="391.8467">Client</text><line style="stroke:#181818;stroke-width:0.5;" x1="475" x2="724" y1="403" y2="403"/><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="482" y="413.6484"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="88" x="494" y="419.9951">name: String</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="482" y="429.9453"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="155" x="494" y="436.292">phone_number: String</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="482" y="446.2422"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="85" x="494" y="452.5889">email: String</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="482" y="462.5391"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="122" x="494" y="468.8857">risk_profile: String</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="482" y="478.8359"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="130" x="494" y="485.1826">last_meeting: Date</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="482" y="495.1328"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="179" x="494" y="501.4795">non_compliance: Boolean</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="482" y="511.4297"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="105" x="494" y="517.7764">account: String</text><line style="stroke:#181818;stroke-width:0.5;" x1="475" x2="724" y1="525.0781" y2="525.0781"/><ellipse cx="485" cy="538.7266" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="225" x="494" y="542.0732">getCalculatedRiskProfile(): String</text><ellipse cx="485" cy="555.0234" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="150" x="494" y="558.3701">getInvestments(): List</text><ellipse cx="485" cy="571.3203" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="156" x="494" y="574.667">isCompliant(): Boolean</text></g><!--MD5=[544917d1b71264f45ca27fbbf077edcc]
class Advisor--><g id="elem_Advisor"><rect codeLine="14" fill="#F1F1F1" height="145.7813" id="Advisor" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="235" x="280" y="659"/><ellipse cx="368.25" cy="675" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M371.2188,680.6406 Q370.6406,680.9375 370,681.0781 Q369.3594,681.2344 368.6563,681.2344 Q366.1563,681.2344 364.8281,679.5938 Q363.5156,677.9375 363.5156,674.8125 Q363.5156,671.6875 364.8281,670.0313 Q366.1563,668.375 368.6563,668.375 Q369.3594,668.375 370,668.5313 Q370.6563,668.6875 371.2188,668.9844 L371.2188,671.7031 Q370.5938,671.125 370,670.8594 Q369.4063,670.5781 368.7813,670.5781 Q367.4375,670.5781 366.75,671.6563 Q366.0625,672.7188 366.0625,674.8125 Q366.0625,676.9063 366.75,677.9844 Q367.4375,679.0469 368.7813,679.0469 Q369.4063,679.0469 370,678.7813 Q370.5938,678.5 371.2188,677.9219 L371.2188,680.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="50" x="388.75" y="679.8467">Advisor</text><line style="stroke:#181818;stroke-width:0.5;" x1="281" x2="514" y1="691" y2="691"/><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="288" y="701.6484"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="88" x="300" y="707.9951">name: String</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="288" y="717.9453"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="85" x="300" y="724.292">email: String</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="288" y="734.2422"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="155" x="300" y="740.5889">phone_number: String</text><line style="stroke:#181818;stroke-width:0.5;" x1="281" x2="514" y1="747.8906" y2="747.8906"/><ellipse cx="291" cy="761.5391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="127" x="300" y="764.8857">getClientList(): List</text><ellipse cx="291" cy="777.8359" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="209" x="300" y="781.1826">getNonCompliantClients(): List</text><ellipse cx="291" cy="794.1328" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="195" x="300" y="797.4795">getDashboardData(): Object</text></g><!--MD5=[1032d4f2870c9a77590bd215a4cd5854]
class Investment--><g id="elem_Investment"><rect codeLine="23" fill="#F1F1F1" height="129.4844" id="Investment" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="354" x="760.5" y="412"/><ellipse cx="894.75" cy="428" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M897.7188,433.6406 Q897.1406,433.9375 896.5,434.0781 Q895.8594,434.2344 895.1563,434.2344 Q892.6563,434.2344 891.3281,432.5938 Q890.0156,430.9375 890.0156,427.8125 Q890.0156,424.6875 891.3281,423.0313 Q892.6563,421.375 895.1563,421.375 Q895.8594,421.375 896.5,421.5313 Q897.1563,421.6875 897.7188,421.9844 L897.7188,424.7031 Q897.0938,424.125 896.5,423.8594 Q895.9063,423.5781 895.2813,423.5781 Q893.9375,423.5781 893.25,424.6563 Q892.5625,425.7188 892.5625,427.8125 Q892.5625,429.9063 893.25,430.9844 Q893.9375,432.0469 895.2813,432.0469 Q895.9063,432.0469 896.5,431.7813 Q897.0938,431.5 897.7188,430.9219 L897.7188,433.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="77" x="915.25" y="432.8467">Investment</text><line style="stroke:#181818;stroke-width:0.5;" x1="761.5" x2="1113.5" y1="444" y2="444"/><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="768.5" y="454.6484"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="88" x="780.5" y="460.9951">name: String</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="768.5" y="470.9453"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="73" x="780.5" y="477.292">risk: String</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="768.5" y="487.2422"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="79" x="780.5" y="493.5889">type: String</text><line style="stroke:#181818;stroke-width:0.5;" x1="761.5" x2="1113.5" y1="500.8906" y2="500.8906"/><ellipse cx="771.5" cy="514.5391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="146" x="780.5" y="517.8857">getRiskLevel(): String</text><ellipse cx="771.5" cy="530.8359" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="328" x="780.5" y="534.1826">isCompatibleWithProfile(profile: String): Boolean</text></g><!--MD5=[2b76b8a65ad0546052e5596cedc6ab71]
class ClientInvestment--><g id="elem_ClientInvestment"><rect codeLine="31" fill="#F1F1F1" height="129.4844" id="ClientInvestment" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="233" x="904" y="667.5"/><ellipse cx="958.25" cy="683.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M961.2188,689.1406 Q960.6406,689.4375 960,689.5781 Q959.3594,689.7344 958.6563,689.7344 Q956.1563,689.7344 954.8281,688.0938 Q953.5156,686.4375 953.5156,683.3125 Q953.5156,680.1875 954.8281,678.5313 Q956.1563,676.875 958.6563,676.875 Q959.3594,676.875 960,677.0313 Q960.6563,677.1875 961.2188,677.4844 L961.2188,680.2031 Q960.5938,679.625 960,679.3594 Q959.4063,679.0781 958.7813,679.0781 Q957.4375,679.0781 956.75,680.1563 Q956.0625,681.2188 956.0625,683.3125 Q956.0625,685.4063 956.75,686.4844 Q957.4375,687.5469 958.7813,687.5469 Q959.4063,687.5469 960,687.2813 Q960.5938,687 961.2188,686.4219 L961.2188,689.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="116" x="978.75" y="688.3467">ClientInvestment</text><line style="stroke:#181818;stroke-width:0.5;" x1="905" x2="1136" y1="699.5" y2="699.5"/><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="912" y="710.1484"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="117" x="924" y="716.4951">amount: Decimal</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="912" y="726.4453"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="113" x="924" y="732.792">quantity: Integer</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="912" y="742.7422"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="145" x="924" y="749.0889">purchase_date: Date</text><line style="stroke:#181818;stroke-width:0.5;" x1="905" x2="1136" y1="756.3906" y2="756.3906"/><ellipse cx="915" cy="770.0391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="207" x="924" y="773.3857">calculateTotalValue(): Decimal</text><ellipse cx="915" cy="786.3359" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="196" x="924" y="789.6826">getRiskContribution(): String</text></g><!--MD5=[037d6bf9e17a7a3f2ae3713e71c6385e]
class ClientRecommendation--><g id="elem_ClientRecommendation"><rect codeLine="39" fill="#F1F1F1" height="113.1875" id="ClientRecommendation" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="249" x="585" y="675.5"/><ellipse cx="624.75" cy="691.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M627.7188,697.1406 Q627.1406,697.4375 626.5,697.5781 Q625.8594,697.7344 625.1563,697.7344 Q622.6563,697.7344 621.3281,696.0938 Q620.0156,694.4375 620.0156,691.3125 Q620.0156,688.1875 621.3281,686.5313 Q622.6563,684.875 625.1563,684.875 Q625.8594,684.875 626.5,685.0313 Q627.1563,685.1875 627.7188,685.4844 L627.7188,688.2031 Q627.0938,687.625 626.5,687.3594 Q625.9063,687.0781 625.2813,687.0781 Q623.9375,687.0781 623.25,688.1563 Q622.5625,689.2188 622.5625,691.3125 Q622.5625,693.4063 623.25,694.4844 Q623.9375,695.5469 625.2813,695.5469 Q625.9063,695.5469 626.5,695.2813 Q627.0938,695 627.7188,694.4219 L627.7188,697.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="162" x="644.25" y="696.3467">ClientRecommendation</text><line style="stroke:#181818;stroke-width:0.5;" x1="586" x2="833" y1="707.5" y2="707.5"/><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="593" y="718.1484"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="223" x="605" y="724.4951">recommendation_score: Double</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="593" y="734.4453"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="152" x="605" y="740.792">date_generated: Date</text><rect fill="none" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="593" y="750.7422"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="131" x="605" y="757.0889">accepted: Boolean</text><line style="stroke:#181818;stroke-width:0.5;" x1="586" x2="833" y1="764.3906" y2="764.3906"/><ellipse cx="596" cy="778.0391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="159" x="605" y="781.3857">getJustification(): String</text></g><!--MD5=[ecb9f6622f029f4e18dded3a7a70baaf]
class ComplianceService--><g id="elem_ComplianceService"><rect codeLine="46" fill="#F1F1F1" height="113.1875" id="ComplianceService" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="352" x="831.5" y="181"/><ellipse cx="937.75" cy="197" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M940.7188,202.6406 Q940.1406,202.9375 939.5,203.0781 Q938.8594,203.2344 938.1563,203.2344 Q935.6563,203.2344 934.3281,201.5938 Q933.0156,199.9375 933.0156,196.8125 Q933.0156,193.6875 934.3281,192.0313 Q935.6563,190.375 938.1563,190.375 Q938.8594,190.375 939.5,190.5313 Q940.1563,190.6875 940.7188,190.9844 L940.7188,193.7031 Q940.0938,193.125 939.5,192.8594 Q938.9063,192.5781 938.2813,192.5781 Q936.9375,192.5781 936.25,193.6563 Q935.5625,194.7188 935.5625,196.8125 Q935.5625,198.9063 936.25,199.9844 Q936.9375,201.0469 938.2813,201.0469 Q938.9063,201.0469 939.5,200.7813 Q940.0938,200.5 940.7188,199.9219 L940.7188,202.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="131" x="958.25" y="201.8467">ComplianceService</text><line style="stroke:#181818;stroke-width:0.5;" x1="832.5" x2="1182.5" y1="213" y2="213"/><line style="stroke:#181818;stroke-width:0.5;" x1="832.5" x2="1182.5" y1="221" y2="221"/><ellipse cx="842.5" cy="234.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="284" x="851.5" y="237.9951">checkCompliance(client: Client): Boolean</text><ellipse cx="842.5" cy="250.9453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="292" x="851.5" y="254.292">getComplianceStatus(client: Client): String</text><ellipse cx="842.5" cy="267.2422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="326" x="851.5" y="270.5889">getComplianceMetrics(advisor: Advisor): Object</text><ellipse cx="842.5" cy="283.5391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="276" x="851.5" y="286.8857">getComplianceIssues(client: Client): List</text></g><!--MD5=[32d1081cb8cbf7ee51e26a3f2e2a59fe]
class RecommendationService--><g id="elem_RecommendationService"><rect codeLine="53" fill="#F1F1F1" height="80.5938" id="RecommendationService" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="347" x="450" y="197"/><ellipse cx="532.75" cy="213" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M535.7188,218.6406 Q535.1406,218.9375 534.5,219.0781 Q533.8594,219.2344 533.1563,219.2344 Q530.6563,219.2344 529.3281,217.5938 Q528.0156,215.9375 528.0156,212.8125 Q528.0156,209.6875 529.3281,208.0313 Q530.6563,206.375 533.1563,206.375 Q533.8594,206.375 534.5,206.5313 Q535.1563,206.6875 535.7188,206.9844 L535.7188,209.7031 Q535.0938,209.125 534.5,208.8594 Q533.9063,208.5781 533.2813,208.5781 Q531.9375,208.5781 531.25,209.6563 Q530.5625,210.7188 530.5625,212.8125 Q530.5625,214.9063 531.25,215.9844 Q531.9375,217.0469 533.2813,217.0469 Q533.9063,217.0469 534.5,216.7813 Q535.0938,216.5 535.7188,215.9219 L535.7188,218.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="173" x="553.25" y="217.8467">RecommendationService</text><line style="stroke:#181818;stroke-width:0.5;" x1="451" x2="796" y1="229" y2="229"/><line style="stroke:#181818;stroke-width:0.5;" x1="451" x2="796" y1="237" y2="237"/><ellipse cx="461" cy="250.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="321" x="470" y="253.9951">generateRecommendations(client: Client): List</text><ellipse cx="461" cy="266.9453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="251" x="470" y="270.292">scoreInvestments(client: Client): List</text></g><!--MD5=[05e66cf12181eddc8120ccbc0de1909c]
class PortfolioService--><g id="elem_PortfolioService"><rect codeLine="58" fill="#F1F1F1" height="96.8906" id="PortfolioService" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="533" x="1150" y="428"/><ellipse cx="1359.25" cy="444" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M1362.2188,449.6406 Q1361.6406,449.9375 1361,450.0781 Q1360.3594,450.2344 1359.6563,450.2344 Q1357.1563,450.2344 1355.8281,448.5938 Q1354.5156,446.9375 1354.5156,443.8125 Q1354.5156,440.6875 1355.8281,439.0313 Q1357.1563,437.375 1359.6563,437.375 Q1360.3594,437.375 1361,437.5313 Q1361.6563,437.6875 1362.2188,437.9844 L1362.2188,440.7031 Q1361.5938,440.125 1361,439.8594 Q1360.4063,439.5781 1359.7813,439.5781 Q1358.4375,439.5781 1357.75,440.6563 Q1357.0625,441.7188 1357.0625,443.8125 Q1357.0625,445.9063 1357.75,446.9844 Q1358.4375,448.0469 1359.7813,448.0469 Q1360.4063,448.0469 1361,447.7813 Q1361.5938,447.5 1362.2188,446.9219 L1362.2188,449.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="106" x="1379.75" y="448.8467">PortfolioService</text><line style="stroke:#181818;stroke-width:0.5;" x1="1151" x2="1682" y1="460" y2="460"/><line style="stroke:#181818;stroke-width:0.5;" x1="1151" x2="1682" y1="468" y2="468"/><ellipse cx="1161" cy="481.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="322" x="1170" y="484.9951">calculatePortfolioRisk(investments: List): String</text><ellipse cx="1161" cy="497.9453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="507" x="1170" y="501.292">compareProfiles(calculatedProfile: String, declaredProfile: String): Boolean</text><ellipse cx="1161" cy="514.2422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="309" x="1170" y="517.5889">getAssetAllocation(investments: List): Object</text></g><!--MD5=[b4f586f7c628b5a371c8c0ac9af5f898]
class AuthService--><g id="elem_AuthService"><rect codeLine="64" fill="#F1F1F1" height="96.8906" id="AuthService" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="337" x="7" y="428"/><ellipse cx="130.25" cy="444" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M133.2188,449.6406 Q132.6406,449.9375 132,450.0781 Q131.3594,450.2344 130.6563,450.2344 Q128.1563,450.2344 126.8281,448.5938 Q125.5156,446.9375 125.5156,443.8125 Q125.5156,440.6875 126.8281,439.0313 Q128.1563,437.375 130.6563,437.375 Q131.3594,437.375 132,437.5313 Q132.6563,437.6875 133.2188,437.9844 L133.2188,440.7031 Q132.5938,440.125 132,439.8594 Q131.4063,439.5781 130.7813,439.5781 Q129.4375,439.5781 128.75,440.6563 Q128.0625,441.7188 128.0625,443.8125 Q128.0625,445.9063 128.75,446.9844 Q129.4375,448.0469 130.7813,448.0469 Q131.4063,448.0469 132,447.7813 Q132.5938,447.5 133.2188,446.9219 L133.2188,449.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="82" x="150.75" y="448.8467">AuthService</text><line style="stroke:#181818;stroke-width:0.5;" x1="8" x2="343" y1="460" y2="460"/><line style="stroke:#181818;stroke-width:0.5;" x1="8" x2="343" y1="468" y2="468"/><ellipse cx="18" cy="481.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="311" x="27" y="484.9951">login(email: String, password: String): Advisor</text><ellipse cx="18" cy="497.9453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="91" x="27" y="501.292">logout(): void</text><ellipse cx="18" cy="514.2422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="183" x="27" y="517.5889">validateSession(): Boolean</text></g><!--MD5=[569c5868375e12a0903c259e7bee9d1e]
class DashboardService--><g id="elem_DashboardService"><rect codeLine="70" fill="#F1F1F1" height="96.8906" id="DashboardService" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="346" x="450.5" y="7"/><ellipse cx="556.25" cy="23" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M559.2188,28.6406 Q558.6406,28.9375 558,29.0781 Q557.3594,29.2344 556.6563,29.2344 Q554.1563,29.2344 552.8281,27.5938 Q551.5156,25.9375 551.5156,22.8125 Q551.5156,19.6875 552.8281,18.0313 Q554.1563,16.375 556.6563,16.375 Q557.3594,16.375 558,16.5313 Q558.6563,16.6875 559.2188,16.9844 L559.2188,19.7031 Q558.5938,19.125 558,18.8594 Q557.4063,18.5781 556.7813,18.5781 Q555.4375,18.5781 554.75,19.6563 Q554.0625,20.7188 554.0625,22.8125 Q554.0625,24.9063 554.75,25.9844 Q555.4375,27.0469 556.7813,27.0469 Q557.4063,27.0469 558,26.7813 Q558.5938,26.5 559.2188,25.9219 L559.2188,28.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="126" x="576.75" y="27.8467">DashboardService</text><line style="stroke:#181818;stroke-width:0.5;" x1="451.5" x2="795.5" y1="39" y2="39"/><line style="stroke:#181818;stroke-width:0.5;" x1="451.5" x2="795.5" y1="47" y2="47"/><ellipse cx="461.5" cy="60.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="289" x="470.5" y="63.9951">getOverviewData(advisor: Advisor): Object</text><ellipse cx="461.5" cy="76.9453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="258" x="470.5" y="80.292">getClientDetails(client: Client): Object</text><ellipse cx="461.5" cy="93.2422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="320" x="470.5" y="96.5889">getClientRecommendations(client: Client): List</text></g><!--MD5=[c3b41086add5ce7c0c3acc60aa113f5e]
link Client to Advisor--><g id="link_Client_Advisor"><path codeLine="76" d="M516.17,582.07 C496.76,606.44 476.44,631.93 458.22,654.8 " fill="none" id="Client-to-Advisor" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="455.07,658.75,463.8036,654.1976,458.183,654.8373,457.5433,649.2167,455.07,658.75" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="69" x="487.5" y="625.0669">1 atende 1</text></g><!--MD5=[c7fde211d6ae8eacbbbc949aafae9a94]
link Client to ClientInvestment--><g id="link_Client_ClientInvestment"><path codeLine="77" d="M666.69,582.16 C682.16,599.96 699.91,616.66 719.5,629 C770.4,661.07 794.26,640.47 851.5,659 C867.04,664.03 883.12,669.93 898.91,676.15 " fill="none" id="Client-to-ClientInvestment" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="903.8,678.09,896.9106,671.0518,899.1527,676.2454,893.9592,678.4875,903.8,678.09" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="73" x="720.5" y="625.0669">1 possui 0..</text></g><!--MD5=[7d2885c0da9ea557268850f11ab6776c]
link Investment to ClientInvestment--><g id="link_Investment_ClientInvestment"><path codeLine="78" d="M958.34,541.14 C970.42,578.06 985.65,624.56 998.01,662.3 " fill="none" id="Investment-to-ClientInvestment" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="999.65,667.33,1000.643,657.5313,998.0903,662.5795,993.0422,660.0268,999.65,667.33" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="89" x="986.5" y="625.0669">1 inclui em 0..</text></g><!--MD5=[f782c9c51a376ac2d52b4dd2e9f86901]
link Client to ClientRecommendation--><g id="link_Client_ClientRecommendation"><path codeLine="79" d="M585.61,582.17 C586.79,598.33 590.04,614.46 596.5,629 C603.46,644.65 614.13,658.91 626.19,671.48 " fill="none" id="Client-to-ClientRecommendation" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="629.98,675.33,626.5002,666.1164,626.466,671.7731,620.8092,671.7388,629.98,675.33" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="76" x="597.5" y="625.0669">1 recebe 0..</text></g><!--MD5=[e029d4b8175fd2c959655e5e5b56de26]
link Investment to ClientRecommendation--><g id="link_Investment_ClientRecommendation"><path codeLine="80" d="M880.26,541.14 C844.35,581.07 798.36,632.21 763.15,671.35 " fill="none" id="Investment-to-ClientRecommendation" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="759.78,675.1,768.7739,671.0862,763.1248,671.3835,762.8275,665.7345,759.78,675.1" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="146" x="810.5" y="625.0669">1 recomendado em 0..</text></g><!--MD5=[6107c9eaffc74278cd17b742cdee531f]
link ComplianceService to Client--><g id="link_ComplianceService_Client"><path codeLine="82" d="M886.67,294.01 C840.93,316.35 788.96,343.35 743.5,371 C738.85,373.83 734.16,376.76 729.46,379.77 " fill="none" id="ComplianceService-to-Client" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="725.14,382.55,734.8723,381.0396,729.3436,379.8426,730.5405,374.3138,725.14,382.55" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="77" x="821.5" y="337.0669">1 analisa 0..</text></g><!--MD5=[c1e904ea2f61d7207c35e6ca055b2f3e]
link ComplianceService to PortfolioService--><g id="link_ComplianceService_PortfolioService"><path codeLine="83" d="M1103.39,294.07 C1172.01,333.83 1263.61,386.91 1329.99,425.37 " fill="none" id="ComplianceService-to-PortfolioService" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="1334.33,427.89,1328.537,419.925,1330.0003,425.3893,1324.5359,426.8526,1334.33,427.89" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="47" x="1175.5" y="337.0669">1 usa 1</text></g><!--MD5=[eb34c1819d76aac9ec575c44f107a186]
link RecommendationService to Client--><g id="link_RecommendationService_Client"><path codeLine="84" d="M619.48,278.17 C617.04,302.31 613.78,334.44 610.61,365.75 " fill="none" id="RecommendationService-to-Client" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="610.11,370.77,615.0149,362.2294,610.6245,365.7965,607.0573,361.4062,610.11,370.77" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="138" x="614.5" y="337.0669">1 recomenda para 0..</text></g><!--MD5=[783cddea75fe0ff6298e6d2dd249f38f]
link PortfolioService to ClientInvestment--><g id="link_PortfolioService_ClientInvestment"><path codeLine="85" d="M1342.02,525.18 C1280.52,564.55 1192.56,620.86 1124.18,664.63 " fill="none" id="PortfolioService-to-ClientInvestment" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="1119.71,667.49,1129.4459,666.0029,1123.9201,664.7927,1125.1303,659.2668,1119.71,667.49" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="77" x="1195.5" y="625.0669">1 analisa 0..</text></g><!--MD5=[7c5cd6c46e028680181e2814b7356d3e]
link DashboardService to Advisor--><g id="link_DashboardService_Advisor"><path codeLine="86" d="M513.88,104.06 C482.32,123.28 451.36,148.7 432.5,181 C345.65,329.8 363.66,541.4 381.99,653.82 " fill="none" id="DashboardService-to-Advisor" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="382.81,658.77,385.2894,649.2383,381.995,653.8369,377.3964,650.5424,382.81,658.77" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="124" x="381.5" y="337.0669">1 utiliza dados de 1</text></g><!--MD5=[4f38252ad01e2fa24735c1e3e1c29697]
link DashboardService to ComplianceService--><g id="link_DashboardService_ComplianceService"><path codeLine="87" d="M724.89,104.03 C773.75,126.93 832.76,154.59 884.19,178.7 " fill="none" id="DashboardService-to-ComplianceService" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="888.83,180.87,882.3762,173.4304,884.302,178.7493,878.9831,180.6751,888.83,180.87" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="47" x="816.5" y="147.0669">1 usa 1</text></g><!--MD5=[69f46f089b6f7177fd005a6a8bf7e338]
link DashboardService to RecommendationService--><g id="link_DashboardService_RecommendationService"><path codeLine="88" d="M623.5,104.26 C623.5,131.31 623.5,164.94 623.5,191.52 " fill="none" id="DashboardService-to-RecommendationService" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="623.5,196.73,627.5,187.73,623.5,191.73,619.5,187.73,623.5,196.73" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="47" x="624.5" y="147.0669">1 usa 1</text></g><!--MD5=[ada274ae7172f6c94d7cff543c68a8cd]
link AuthService to Advisor--><g id="link_AuthService_Advisor"><path codeLine="89" d="M202.99,525.31 C221.97,556.4 248.87,596.97 277.5,629 C285.47,637.92 294.22,646.77 303.26,655.31 " fill="none" id="AuthService-to-Advisor" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="#181818" points="307.14,658.95,303.3087,649.8769,303.4919,655.5308,297.838,655.7139,307.14,658.95" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="92" x="278.5" y="625.0669">1 autentica 0..</text></g><!--MD5=[daa4835e158a1d04e08c0a17efb84f9d]
@startuml
class Client {
- name: String
- phone_number: String
- email: String
- risk_profile: String
- last_meeting: Date
- non_compliance: Boolean
- account: String
+ getCalculatedRiskProfile(): String
+ getInvestments(): List
+ isCompliant(): Boolean
}

class Advisor {
- name: String
- email: String
- phone_number: String
+ getClientList(): List
+ getNonCompliantClients(): List
+ getDashboardData(): Object
}

class Investment {
- name: String
- risk: String
- type: String
+ getRiskLevel(): String
+ isCompatibleWithProfile(profile: String): Boolean
}

class ClientInvestment {
- amount: Decimal
- quantity: Integer
- purchase_date: Date
+ calculateTotalValue(): Decimal
+ getRiskContribution(): String
}

class ClientRecommendation {
- recommendation_score: Double
- date_generated: Date
- accepted: Boolean
+ getJustification(): String
}

class ComplianceService {
+ checkCompliance(client: Client): Boolean
+ getComplianceStatus(client: Client): String
+ getComplianceMetrics(advisor: Advisor): Object
+ getComplianceIssues(client: Client): List
}

class RecommendationService {
+ generateRecommendations(client: Client): List
+ scoreInvestments(client: Client): List
}

class PortfolioService {
+ calculatePortfolioRisk(investments: List): String
+ compareProfiles(calculatedProfile: String, declaredProfile: String): Boolean
+ getAssetAllocation(investments: List): Object
}

class AuthService {
+ login(email: String, password: String): Advisor
+ logout(): void
+ validateSession(): Boolean
}

class DashboardService {
+ getOverviewData(advisor: Advisor): Object
+ getClientDetails(client: Client): Object
+ getClientRecommendations(client: Client): List
}

Client - -> Advisor : "1 atende 1"
Client - -> ClientInvestment : "1 possui 0.."
Investment - -> ClientInvestment : "1 inclui em 0.."
Client - -> ClientRecommendation : "1 recebe 0.."
Investment - -> ClientRecommendation : "1 recomendado em 0.."

ComplianceService - -> Client : "1 analisa 0.."
ComplianceService - -> PortfolioService : "1 usa 1"
RecommendationService - -> Client : "1 recomenda para 0.."
PortfolioService - -> ClientInvestment : "1 analisa 0.."
DashboardService - -> Advisor : "1 utiliza dados de 1"
DashboardService - -> ComplianceService : "1 usa 1"
DashboardService - -> RecommendationService : "1 usa 1"
AuthService - -> Advisor : "1 autentica 0.."
@enduml

PlantUML version 1.2022.7(Mon Aug 22 17:01:30 UTC 2022)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Default Encoding: UTF-8
Language: en
Country: null
--></g></svg>