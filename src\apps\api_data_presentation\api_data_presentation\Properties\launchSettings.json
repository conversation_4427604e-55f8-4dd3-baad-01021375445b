{"profiles": {"http": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5072"}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "environmentVariables": {"ASPNETCORE_HTTP_PORTS": "8080"}, "publishAllPorts": true, "useSSL": false}}, "$schema": "https://json.schemastore.org/launchsettings.json"}