using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Pati.ClassificationService.Application.Services;

namespace Pati.ClassificationService.Infrastructure.Jobs
{
    /// <summary>
    /// Job simples usando Timer para classificação automática
    /// Alternativa mais simples ao Quartz.NET
    /// </summary>
    public class SimpleClassificationJob : BackgroundService
    {
        private readonly ILogger<SimpleClassificationJob> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly ClassificationJobOptions _options;
        private Timer? _timer;

        public SimpleClassificationJob(
            ILogger<SimpleClassificationJob> logger,
            IServiceProvider serviceProvider,
            IOptions<ClassificationJobOptions> options)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("SimpleClassificationJob ExecuteAsync called - Enabled: {Enabled}, Interval: {Minutes}min, RunOnStartup: {RunOnStartup}",
                _options.Enabled, _options.IntervalMinutes, _options.RunOnStartup);

            if (!_options.Enabled)
            {
                _logger.LogInformation("Classification job is disabled in configuration");
                return;
            }

            _logger.LogInformation("🕒 Starting Simple Classification Job Service");

            if (_options.IntervalMinutes >= 1440)
            {
                var hours = _options.IntervalMinutes / 60;
                _logger.LogInformation("🔄 Classification job will run every {Hours} hours (Daily at {DailyTime}, RunOnStartup: {RunOnStartup})",
                    hours, _options.DailyExecutionTime ?? "not specified", _options.RunOnStartup);
            }
            else
            {
                _logger.LogInformation("🔄 Classification job will run every {Minutes} minutes (RunOnStartup: {RunOnStartup})",
                    _options.IntervalMinutes, _options.RunOnStartup);
            }

            // Calcular intervalo
            var interval = TimeSpan.FromMinutes(_options.IntervalMinutes);
            var initialDelay = _options.RunOnStartup ? TimeSpan.Zero : interval;

            // Configurar timer
            _timer = new Timer(
                callback: async _ => await ExecuteJobAsync(),
                state: null,
                dueTime: initialDelay,
                period: interval);

            // Manter o serviço rodando
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }

        private async Task ExecuteJobAsync()
        {
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("🚀 Starting scheduled classification job at {StartTime}", startTime);

                // Criar scope para resolver serviços
                using var scope = _serviceProvider.CreateScope();
                var classificationService = scope.ServiceProvider.GetRequiredService<IClassificationService>();

                // Executar classificação de todas as carteiras
                await classificationService.ClassifyAllPortfoliosAsync();

                var duration = DateTime.UtcNow - startTime;
                _logger.LogInformation("✅ Scheduled classification job completed successfully in {Duration}ms",
                    duration.TotalMilliseconds);
            }
            catch (Exception ex)
            {
                var duration = DateTime.UtcNow - startTime;
                _logger.LogError(ex, "Scheduled classification job failed after {Duration}ms", 
                    duration.TotalMilliseconds);
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping Simple Classification Job Service");

            _timer?.Change(Timeout.Infinite, 0);
            _timer?.Dispose();

            await base.StopAsync(cancellationToken);
            
            _logger.LogInformation("Simple Classification Job Service stopped");
        }

        public override void Dispose()
        {
            _timer?.Dispose();
            base.Dispose();
        }
    }
}
