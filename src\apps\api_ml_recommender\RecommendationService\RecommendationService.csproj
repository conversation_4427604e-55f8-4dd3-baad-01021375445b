<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.16" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.5" />
    <PackageReference Include="Microsoft.ML" Version="4.0.2" />
    <PackageReference Include="Microsoft.ML.Recommender" Version="0.22.2" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.0" />
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
    <PackageReference Include="Npgsql" Version="7.0.5" />
  </ItemGroup>

</Project>
