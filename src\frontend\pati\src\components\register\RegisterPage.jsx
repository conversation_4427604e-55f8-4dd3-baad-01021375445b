import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StyleSheet,
  TouchableWithoutFeedback,
  Keyboard,
  Alert,
  ActivityIndicator,
  ScrollView
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAuth } from '../../contexts/AuthContext';

export default function RegisterPage({ navigation }) {
  const [focusedInput, setFocusedInput] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const { signUp, loading } = useAuth();

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleRegister = async () => {
    const { name, email, password, confirmPassword } = formData;

    // Validações
    if (!name || !email || !password || !confirmPassword) {
      Alert.alert('Erro', 'Por favor, preencha todos os campos');
      return;
    }

    if (!isValidEmail(email)) {
      Alert.alert('Erro', 'Por favor, insira um e-mail válido');
      return;
    }

    if (password.length < 6) {
      Alert.alert('Erro', 'A senha deve ter pelo menos 6 caracteres');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Erro', 'As senhas não coincidem');
      return;
    }

    try {
      const result = await signUp(email, password, { name });
      
      if (result.success) {        Alert.alert(
          'Sucesso', 
          'Conta criada com sucesso! Verifique seu e-mail para confirmar a conta.',
          [{ text: 'OK', onPress: () => navigation.replace('Login') }]
        );
      } else {
        Alert.alert('Erro no Cadastro', result.error || 'Erro ao criar conta');
      }
    } catch (error) {
      Alert.alert('Erro', 'Ocorreu um erro inesperado. Tente novamente.');
    }
  };

  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <ScrollView contentContainerStyle={styles.container}>
        <View style={styles.logoContainer}>
          <Image style={styles.logo} source={require('../../../assets/images/logopati.png')} />
        </View>

        <Text style={styles.title}>Criar Conta</Text>

        <View style={styles.formField}>
          <TextInput 
            style={[
              styles.inputField, 
              focusedInput === 'name' && styles.inputFieldFocused
            ]} 
            placeholder="Nome completo" 
            placeholderTextColor="#000"
            value={formData.name}
            onChangeText={(value) => handleInputChange('name', value)}
            onFocus={() => setFocusedInput('name')}
            onBlur={() => setFocusedInput(null)}
          />
        </View>

        <View style={styles.formField}>
          <TextInput 
            style={[
              styles.inputField, 
              focusedInput === 'email' && styles.inputFieldFocused
            ]} 
            placeholder="E-mail" 
            keyboardType="email-address"
            placeholderTextColor="#000"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            onFocus={() => setFocusedInput('email')}
            onBlur={() => setFocusedInput(null)}
          />
        </View>

        <View style={styles.formField}>
          <View style={styles.passwordContainer}>
            <TextInput 
              style={[
                styles.inputField, 
                styles.passwordInput,
                focusedInput === 'password' && styles.inputFieldFocused
              ]} 
              placeholder="Senha" 
              secureTextEntry={!showPassword}
              placeholderTextColor="#000"
              value={formData.password}
              onChangeText={(value) => handleInputChange('password', value)}
              onFocus={() => setFocusedInput('password')}
              onBlur={() => setFocusedInput(null)}
            />
            <TouchableOpacity 
              style={styles.eyeIcon} 
              onPress={togglePasswordVisibility}
            >
              <Icon 
                name={showPassword ? 'eye-outline' : 'eye-off-outline'} 
                size={24} 
                color="#666" 
              />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.formField}>
          <View style={styles.passwordContainer}>
            <TextInput 
              style={[
                styles.inputField, 
                styles.passwordInput,
                focusedInput === 'confirmPassword' && styles.inputFieldFocused
              ]} 
              placeholder="Confirmar senha" 
              secureTextEntry={!showConfirmPassword}
              placeholderTextColor="#000"
              value={formData.confirmPassword}
              onChangeText={(value) => handleInputChange('confirmPassword', value)}
              onFocus={() => setFocusedInput('confirmPassword')}
              onBlur={() => setFocusedInput(null)}
            />
            <TouchableOpacity 
              style={styles.eyeIcon} 
              onPress={toggleConfirmPasswordVisibility}
            >
              <Icon 
                name={showConfirmPassword ? 'eye-outline' : 'eye-off-outline'} 
                size={24} 
                color="#666" 
              />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={[styles.registerBtn, loading && styles.registerBtnDisabled]} 
            onPress={handleRegister}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.registerBtnText}>Cadastrar</Text>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.loginContainer}>
          <Text style={styles.loginText}>Já tem uma conta? </Text>
          <TouchableOpacity onPress={() => navigation.navigate('Login')}>
            <Text style={styles.loginLink}>Faça login</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  logo: {
    width: 200,
    height: 200,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#EE3480',
    marginBottom: 30,
    textAlign: 'center',
  },
  formField: {
    marginBottom: 20,
    width: 288,
  },  inputField: {
    width: 288,
    height: 50,
    backgroundColor: '#FCF7ED',
    borderRadius: 30,
    borderWidth: 2,
    borderColor: '#EE3480',
    paddingHorizontal: 20,
    fontSize: 12,
    fontWeight: '600',
    color: 'black',
  },
  inputFieldFocused: {
    borderColor: '#24A49D',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  registerBtn: {
    width: 150,
    height: 42,
    backgroundColor: '#EE3480',
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  registerBtnDisabled: {
    backgroundColor: '#ccc',
  },
  registerBtnText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  passwordContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  passwordInput: {
    paddingRight: 50,
  },
  eyeIcon: {
    position: 'absolute',
    right: 15,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
  },
  loginText: {
    color: '#666',
    fontSize: 14,
  },
  loginLink: {
    color: '#EE3480',
    fontSize: 14,
    fontWeight: '600',
  },
});
