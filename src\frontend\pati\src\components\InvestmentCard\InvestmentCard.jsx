import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const InvestmentCard = ({ name, gestora, classe }) => {
  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.name}>{name}</Text>
      </View>
      <View style={styles.cardBody}>
        <Text style={styles.detailText}>Gestora: {gestora}</Text>
        <Text style={styles.detailText}>Classe: {classe}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FCF7ED',
    borderRadius: 30,
    borderWidth: 2,
    borderColor: '#EE3480',
    padding: 16,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    marginBottom: 8,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  cardBody: {
    // No specific styles needed for the body container itself
  },
  detailText: {
    fontSize: 12,
    color: '#00000',
    marginBottom: 3,
  },
});

export default InvestmentCard;