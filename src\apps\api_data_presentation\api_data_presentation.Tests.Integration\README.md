# Documentação dos Testes de Integração

## Executando os Testes

### Pré-requisitos
- Docker e Docker Compose instalados
- .NET 9.0 SDK

### Passos para Execução

1. Inicie os bancos de dados necessários (em `./src/apps/apí_data_presentation`):
```bash
docker-compose up -d db mongo
```

> **Nota**: Não é necessário subir o serviço da API manualmente, pois o próprio teste instancia o serviço usando `WebApplicationFactory`.

2. Execute os testes:
```bash
dotnet test
```

## O que este Teste Valida

Este teste valida o endpoint mais fundamental da aplicação: `GET /advisor/clients/{clientId}/recommendations`. Este endpoint é central para nossa regra de negócio pois integra:
- Dados cadastrais do cliente
- Análise de perfil de risco
- Sistema de recomendação de investimentos

### Estrutura do Teste (Given-When-Then)

O teste segue o padrão BDD (Behavior Driven Development) com a estrutura Given-When-Then:

**Given (Dado que)**
- Existe um cliente cadastrado com ID específico
- O cliente possui perfis de risco desalinhados (Formulário: Moderado, Carteira: Conservador)
- Existem investimentos cadastrados no sistema

**When (Quando)**
- O endpoint de recomendações é consultado para este cliente

**Then (Então)**
- O sistema retorna status 200 OK
- Os dados do cliente estão corretos
- Os perfis de risco são apresentados
- Uma lista de investimentos recomendados é fornecida com scores de adequação

### Validações Específicas

1. Status e Dados Básicos:
   - Status 200 OK
   - Dados do cliente (ID, email, telefone)

2. Perfis de Risco:
   - Perfil do formulário
   - Perfil da carteira atual

3. Recomendações:
   - Lista não vazia de investimentos
   - Cada investimento contém:
     - ID do investimento
     - Nome
     - Tipo
     - Risco
     - Score de adequação (0 a 1)

## Casos de Uso e Requisitos Funcionais
A seguir estão os casos de uso e requisitos funcionais relacionados ao teste feito.

### Casos de Uso Principais
- **UC06 - Gerar Recomendação de Ativos**
  - Validação de recomendações baseadas no perfil
  - Cálculo de scores de adequação

- **UC04 - Visualizar Detalhes do Cliente**
  - Validação de dados cadastrais
  - Exibição de perfis de risco

### Requisitos Funcionais
- **RF08 - Gerar recomendações para realinhamento**
  - Recomendações para perfis desalinhados
  - Cálculo correto de scores

- **RF07 - Visualizar perfis do cliente**
  - Exibição de perfis (formulário e carteira)
  - Identificação de desalinhamento

## Arquitetura do Teste

### 1. Estrutura
- Padrão AAA (Arrange, Act, Assert)
- Framework: xUnit + FluentAssertions
- WebApplicationFactory para simulação da API
- Integração com PostgreSQL e MongoDB

### 2. Preparação (Arrange)
- **PostgreSQL:**
  - Cliente com perfis desalinhados
  - Cadastro de investimentos
- **MongoDB:**
  - Recomendações pré-calculadas
  - Scores de adequação

### 3. Execução (Act)
- Requisição HTTP GET
- Deserialização da resposta

### 4. Verificações (Assert)
- Status HTTP
- Campos da resposta
- Estrutura das recomendações
- Validação de scores

## Garantias de Qualidade

1. **Integridade de Dados**
   - Bancos isolados para teste
   - Limpeza pós-execução
   - Consistência entre bases

2. **Validação Completa**
   - Verificação de campos
   - Validação de tipos e ranges
   - Coerência das recomendações

3. **Cenário Real**
   - Perfil desalinhado
   - Dados representativos
   - Integração end-to-end

4. **Isolamento**
   - Ambiente controlado
   - Bancos dedicados
   - Não interferência

## Documentação Gherkin

O arquivo `.feature` documenta o comportamento em linguagem natural, servindo como:
- Documentação viva do sistema
- Referência para stakeholders
- Especificação executável

O cenário representa um caso real onde um assessor precisa obter recomendações para um cliente com perfil desalinhado.

## Resultados dos Testes

Os testes são executados com sucesso, apresentando apenas 4 avisos relacionados a:
- Possíveis referências nulas (CS8601)
- Campos não anuláveis que precisam ser inicializados (CS8618)

Tempo médio de execução: ~1 segundo

### Exemplo de Resposta
```json
{
  "clientId": 123,
  "email": "<EMAIL>",
  "phone": "11999999999",
  "riskProfileForm": "Moderado",
  "riskProfileWallet": "Conservador",
  "recommendedInvestments": [
    {
      "investmentId": 456,
      "name": "Tesouro IPCA+ 2026",
      "type": "Renda Fixa",
      "risk": "Moderado",
      "score": 0.85
    },
    {
      "investmentId": 457,
      "name": "Fundo Multimercado XYZ",
      "type": "Fundo",
      "risk": "Moderado",
      "score": 0.75
    }
  ]
}
```