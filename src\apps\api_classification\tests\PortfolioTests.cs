using Pati.ClassificationService.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace Pati.ClassificationService.Tests
{
    public class PortfolioTests
    {
        [Fact]
        public void Portfolio_Constructor_ValidData_CreatesSuccessfully()
        {
            // Arrange
            var beforeCreation = DateTime.UtcNow;
            var assets = new List<Asset>
            {
                new Asset("asset1", "Test Asset", "Test Type", "Renda Fixa", "Conservador", 100, 1000)
            };

            // Act
            var portfolio = new Portfolio(1, "test-portfolio", "Moderado", assets);
            var afterCreation = DateTime.UtcNow;

            // Assert
            Assert.Equal(1, portfolio.AccountId);
            Assert.Equal("test-portfolio", portfolio.PortfolioId);
            Assert.Equal("Moderado", portfolio.PortfolioProfile);
            Assert.Single(portfolio.Assets);
            Assert.True(portfolio.CreatedAt >= beforeCreation && portfolio.CreatedAt <= afterCreation);
            Assert.Null(portfolio.UpdatedAt); // UpdatedAt should be null initially
        }

        [Theory]
        [InlineData("Conservador", "Conservador", true)]
        [InlineData("Conservador", "Moderado", false)]
        [InlineData("Conservador", "Arrojado", false)]
        [InlineData("Conservador", "Sofisticado", false)]
        [InlineData("Moderado", "Conservador", true)]
        [InlineData("Moderado", "Moderado", true)]
        [InlineData("Moderado", "Arrojado", false)]
        [InlineData("Moderado", "Sofisticado", false)]
        [InlineData("Arrojado", "Conservador", true)]
        [InlineData("Arrojado", "Moderado", true)]
        [InlineData("Arrojado", "Arrojado", true)]
        [InlineData("Arrojado", "Sofisticado", false)]
        [InlineData("Sofisticado", "Conservador", true)]
        [InlineData("Sofisticado", "Moderado", true)]
        [InlineData("Sofisticado", "Sofisticado", true)]
        [InlineData("Sofisticado", "Arrojado", false)]
        public void IsRiskAllowedForProfile_VariousProfiles_ReturnsExpectedResult(
            string profile, string risk, bool expected)
        {
            // Act
            var result = Portfolio.IsRiskAllowedForProfile(profile, risk);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void IdentifyInconsistencies_ConservadorWithArrojadoAsset_ReturnsInconsistency()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Risky Asset", "Test Type", "Renda Variável", "Arrojado", 100, 1000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Conservador", assets);

            // Act
            var inconsistencies = portfolio.IdentifyInconsistencies();

            // Assert
            Assert.NotEmpty(inconsistencies);
            var inconsistency = inconsistencies.First();
            Assert.Equal("asset1", inconsistency.AssetId);
            Assert.Equal("Risky Asset", inconsistency.Name);
            Assert.Contains("incompatível", inconsistency.Description);
            Assert.Equal("high", inconsistency.Severity);
        }

        [Fact]
        public void IdentifyInconsistencies_ModeradoWithCompatibleAssets_ReturnsNoInconsistencies()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Conservative Asset", "Test Type", "Renda Fixa", "Conservador", 100, 1000),
                new Asset("asset2", "Moderate Asset", "Test Type", "Renda Variável", "Moderado", 50, 500)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Moderado", assets);

            // Act
            var inconsistencies = portfolio.IdentifyInconsistencies();

            // Assert
            Assert.Empty(inconsistencies);
        }

        [Fact]
        public void CalculateIncomeProportions_OnlyFixedIncome_Returns100PercentFixed()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Fixed Asset 1", "Type1", "Renda Fixa", "Conservador", 100, 5000),
                new Asset("asset2", "Fixed Asset 2", "Type2", "Renda Fixa", "Conservador", 50, 3000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Conservador", assets);

            // Act
            var proportions = portfolio.CalculateIncomeProportions();

            // Assert
            Assert.Equal(1.0m, proportions.FixedIncome);
            Assert.Equal(0.0m, proportions.VariableIncome);
        }

        [Fact]
        public void CalculateIncomeProportions_OnlyVariableIncome_Returns100PercentVariable()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Variable Asset 1", "Type1", "Renda Variável", "Arrojado", 100, 4000),
                new Asset("asset2", "Variable Asset 2", "Type2", "Renda Variável", "Arrojado", 50, 6000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Arrojado", assets);

            // Act
            var proportions = portfolio.CalculateIncomeProportions();

            // Assert
            Assert.Equal(0.0m, proportions.FixedIncome);
            Assert.Equal(1.0m, proportions.VariableIncome);
        }

        [Fact]
        public void CalculateIncomeProportions_MixedAssets_ReturnsCorrectProportions()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Fixed Asset", "Type1", "Renda Fixa", "Conservador", 100, 7000),
                new Asset("asset2", "Variable Asset", "Type2", "Renda Variável", "Moderado", 50, 3000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Moderado", assets);

            // Act
            var proportions = portfolio.CalculateIncomeProportions();

            // Assert
            Assert.Equal(0.7m, proportions.FixedIncome);
            Assert.Equal(0.3m, proportions.VariableIncome);
        }

        [Fact]
        public void CalculateIncomeProportions_EmptyAssets_ThrowsArgumentException()
        {
            // Arrange
            var assets = new List<Asset>();

            // Act & Assert
            Assert.Throws<ArgumentException>(() => new Portfolio(1, "test-portfolio", "Moderado", assets));
        }

        [Fact]
        public void GetTotalValue_MultipleAssets_ReturnsCorrectSum()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Asset 1", "Type1", "Renda Fixa", "Conservador", 100, 1500),
                new Asset("asset2", "Asset 2", "Type2", "Renda Variável", "Moderado", 50, 2500),
                new Asset("asset3", "Asset 3", "Type3", "Renda Fixa", "Conservador", 75, 1000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Moderado", assets);

            // Act
            var totalValue = portfolio.GetTotalValue();

            // Assert
            Assert.Equal(5000m, totalValue);
        }

        [Fact]
        public void Update_ValidPortfolioProfile_UpdatesSuccessfully()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Test Asset", "Test Type", "Renda Fixa", "Conservador", 100, 1000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Conservador", assets);
            var beforeUpdate = DateTime.UtcNow;

            // Act
            portfolio.Update(portfolioProfile: "Moderado");
            var afterUpdate = DateTime.UtcNow;

            // Assert
            Assert.Equal("Moderado", portfolio.PortfolioProfile);
            Assert.NotNull(portfolio.UpdatedAt); // Should not be null after update
            Assert.True(portfolio.UpdatedAt >= beforeUpdate && portfolio.UpdatedAt <= afterUpdate);
        }

        [Fact]
        public void Update_InvalidPortfolioProfile_ThrowsArgumentException()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Test Asset", "Test Type", "Renda Fixa", "Conservador", 100, 1000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Conservador", assets);

            // Act & Assert
            Assert.Throws<ArgumentException>(() => portfolio.Update(portfolioProfile: "InvalidProfile"));
        }

        [Fact]
        public void Update_NewAssets_UpdatesAssetsSuccessfully()
        {
            // Arrange
            var originalAssets = new List<Asset>
            {
                new Asset("asset1", "Original Asset", "Type1", "Renda Fixa", "Conservador", 100, 1000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Conservador", originalAssets);

            var newAssets = new List<Asset>
            {
                new Asset("asset2", "New Asset", "Type2", "Renda Fixa", "Conservador", 200, 2000)
            };

            // Act
            portfolio.Update(assets: newAssets);

            // Assert
            Assert.Single(portfolio.Assets);
            Assert.Equal("asset2", portfolio.Assets.First().AssetId);
            Assert.Equal("New Asset", portfolio.Assets.First().Name);
        }

        [Fact]
        public void ToString_ReturnsFormattedString()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Test Asset", "Test Type", "Renda Fixa", "Conservador", 100, 1000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Moderado", assets);

            // Act
            var result = portfolio.ToString();

            // Assert
            Assert.Contains("Portfolio test-portfolio", result);
            Assert.Contains("Account 1", result);
            Assert.Contains("Profile: Moderado", result);
            Assert.Contains("Assets: 1", result);
        }
    }
}
