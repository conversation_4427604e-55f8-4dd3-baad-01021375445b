import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

const ClientCard = ({ name, profile, totalInvestment, lastContact, status, onPress }) => {
  const isConforme = status === 'Conforme';
  const statusStyle = isConforme ? styles.statusConforme : styles.statusInconforme;
  const statusTextStyle = styles.statusText;

  const handlePress = () => {
    const clientData = {
      name,
      profile,
      totalInvestment,
      lastContact,
      status
    };
    
    if (onPress) {
      onPress(clientData);
    }
  };

  return (
    <TouchableOpacity 
      style={styles.card} 
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <View style={styles.cardHeader}>
        <Text style={styles.name}>{name}</Text>
        <View style={[styles.statusBadge, statusStyle]}>
          <Text style={statusTextStyle}>{status}</Text>
        </View>
      </View>
      <View style={styles.cardBody}>
        <Text style={styles.detailText}>Perfil: {profile}</Text>
        <Text style={styles.detailText}>Investimento Total: R$ {totalInvestment}</Text>
        <Text style={styles.detailText}>Último Contato: {lastContact}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FCF7ED', // Approximate light beige color from image
    borderRadius: 20,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    borderRadius: 15,
    paddingVertical: 5,
    paddingHorizontal: 12,
  },
  statusConforme: {
    backgroundColor: '#24A49D', // Approximate teal color
  },
  statusInconforme: {
    backgroundColor: '#EE3480', // Approximate pink color
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardBody: {
    // No specific styles needed for the body container itself
  },
  detailText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 3,
  },
});

export default ClientCard;