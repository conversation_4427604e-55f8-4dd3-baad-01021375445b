using Microsoft.Extensions.Logging;
using Pati.ClassificationService.Application.Services;
using Quartz;

namespace Pati.ClassificationService.Infrastructure.Jobs
{
    /// <summary>
    /// Job agendado para classificação automática de todas as carteiras
    /// Executa a cada 10 minutos (teste) ou 24 horas (produção)
    /// </summary>
    [DisallowConcurrentExecution] // Evita execução simultânea
    public class ClassificationJob : IJob
    {
        private readonly ILogger<ClassificationJob> _logger;
        private readonly IClassificationService _classificationService;

        public ClassificationJob(
            ILogger<ClassificationJob> logger,
            IClassificationService classificationService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _classificationService = classificationService ?? throw new ArgumentNullException(nameof(classificationService));
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var jobKey = context.JobDetail.Key;
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting scheduled classification job {JobKey} at {StartTime}", 
                    jobKey, startTime);

                // Executar classificação de todas as carteiras
                await _classificationService.ClassifyAllPortfoliosAsync();

                var duration = DateTime.UtcNow - startTime;
                _logger.LogInformation("Scheduled classification job {JobKey} completed successfully in {Duration}ms", 
                    jobKey, duration.TotalMilliseconds);
            }
            catch (Exception ex)
            {
                var duration = DateTime.UtcNow - startTime;
                _logger.LogError(ex, "Scheduled classification job {JobKey} failed after {Duration}ms", 
                    jobKey, duration.TotalMilliseconds);
                
                // Re-throw para que o Quartz registre como falha
                throw;
            }
        }
    }

    /// <summary>
    /// Configurações para o job de classificação
    /// </summary>
    public class ClassificationJobOptions
    {
        public const string SectionName = "ClassificationJob";

        /// <summary>
        /// Intervalo em minutos entre execuções
        /// Padrão: 10 minutos (teste), depois 1440 minutos (24 horas)
        /// </summary>
        public int IntervalMinutes { get; set; } = 10;

        /// <summary>
        /// Se deve executar imediatamente na inicialização
        /// </summary>
        public bool RunOnStartup { get; set; } = false;

        /// <summary>
        /// Horário específico para execução (formato HH:mm)
        /// Se definido, ignora IntervalMinutes e executa diariamente neste horário
        /// </summary>
        public string? DailyExecutionTime { get; set; }

        /// <summary>
        /// Se o job está habilitado
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Timeout em minutos para execução do job
        /// </summary>
        public int TimeoutMinutes { get; set; } = 60;
    }
}
