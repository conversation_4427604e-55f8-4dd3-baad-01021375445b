using Pati.ClassificationService.Domain;

namespace Pati.ClassificationService.Application.Services
{
    /// <summary>
    /// Interface para o serviço de modelo SVD
    /// Baseado no notebook SVD_CollaborativeFiltering_CVM175.ipynb
    /// </summary>
    public interface ISVDModelService
    {
        /// <summary>
        /// Carrega o modelo SVD treinado
        /// </summary>
        /// <returns>True se carregado com sucesso</returns>
        Task<bool> LoadModelAsync();

        /// <summary>
        /// Indica se o modelo está carregado
        /// </summary>
        bool IsModelLoaded { get; }

        /// <summary>
        /// Analisa uma carteira e identifica inconformidades usando o modelo SVD
        /// </summary>
        /// <param name="portfolio">Carteira a ser analisada</param>
        /// <returns>Lista de inconformidades detectadas</returns>
        Task<List<Inconsistency>> AnalyzePortfolioAsync(Portfolio portfolio);

        /// <summary>
        /// Recomenda ativos para um cliente baseado no modelo SVD
        /// </summary>
        /// <param name="accountId">ID da conta do cliente</param>
        /// <param name="portfolioProfile">Perfil da carteira</param>
        /// <param name="topN">Número de recomendações</param>
        /// <returns>Lista de IDs de ativos recomendados</returns>
        Task<List<string>> RecommendAssetsForClientAsync(int accountId, string portfolioProfile, int topN = 5);

        /// <summary>
        /// Prediz a adequação de um ativo para um cliente específico
        /// </summary>
        /// <param name="accountId">ID da conta do cliente</param>
        /// <param name="assetId">ID do ativo</param>
        /// <returns>Score de adequação (0-1)</returns>
        Task<double> PredictAssetSuitabilityAsync(int accountId, string assetId);

        /// <summary>
        /// Identifica ativos problemáticos em uma carteira
        /// </summary>
        /// <param name="portfolio">Carteira a ser analisada</param>
        /// <returns>Lista de ativos problemáticos</returns>
        Task<List<Asset>> IdentifyProblematicAssetsAsync(Portfolio portfolio);

        /// <summary>
        /// Obtém estatísticas do modelo SVD
        /// </summary>
        /// <returns>Estatísticas do modelo</returns>
        Task<SVDModelStats> GetModelStatsAsync();
    }

    /// <summary>
    /// Estatísticas do modelo SVD
    /// </summary>
    public class SVDModelStats
    {
        public int NumberOfClients { get; set; }
        public int NumberOfAssets { get; set; }
        public int NumberOfFactors { get; set; }
        public double TrainingAccuracy { get; set; }
        public DateTime ModelTrainedAt { get; set; }
        public string ModelVersion { get; set; } = string.Empty;
    }
}
