using System;

namespace Pati.ClassificationService.Domain
{
    public class ClientRecommendation
    {
        public int RecommendationId { get; set; }
        public int ClientId { get; set; }
        public int InvestmentId { get; set; }
        public double RecommendationScore { get; set; }
        public DateTime RecommendationDate { get; set; } = DateTime.Now;
        public bool Accepted { get; set; }
        public string Justification { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty; // Descrição da recomendação
        public string RecommendationType { get; set; } = string.Empty; // Ex: "Compra", "Venda", "Manter"
        public string Source { get; set; } = string.Empty; // Ex: "Modelo SVD", "Manual"
        public DateTime? ResponseDate { get; set; }
        public string? ResponseJustification { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public void Accept(string? justification = null)
        {
            Accepted = true;
            ResponseDate = DateTime.Now;
            ResponseJustification = justification ?? string.Empty;
        }

        public void Reject(string? justification = null)
        {
            Accepted = false;
            ResponseDate = DateTime.Now;
            ResponseJustification = justification ?? string.Empty;
        }

        public bool IsValidScore(double min = 0, double max = 1)
        {
            return RecommendationScore >= min && RecommendationScore <= max;
        }

        public void SetJustification(string justification)
        {
            Justification = justification;
        }

        public string GetFullJustification()
        {
            if (!string.IsNullOrEmpty(ResponseJustification))
                return ResponseJustification;
            return Justification ?? "Recomendação baseada no perfil de risco e carteira atual.";
        }
    }
}