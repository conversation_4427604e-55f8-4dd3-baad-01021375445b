<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>a1b2c3d4-e5f6-7890-abcd-ef1234567890</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>../../..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="Quartz" Version="3.14.0" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.4.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />

    <!-- ML.NET for SVD model integration -->
    <PackageReference Include="Microsoft.ML" Version="3.0.1" />
    <PackageReference Include="Microsoft.ML.Recommender" Version="0.21.1" />

    <!-- Force specific version to resolve vulnerabilities NU1903 -->
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <!-- Exclude test files from main project -->
    <Compile Remove="tests\**" />
    <Content Remove="tests\**" />
    <EmbeddedResource Remove="tests\**" />
    <None Remove="tests\**" />
  </ItemGroup>

</Project>