-- <PERSON><PERSON><PERSON> das tabelas

-- Tabela ADVISOR (Assessor)
CREATE TABLE ADVISOR (
    advisor_id SERIAL PRIMARY KEY,
    uid VARCHAR(128) UNIQUE NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_updated_at CHECK (updated_at >= created_at)
);

-- Tabela CLIENT (Cliente)
CREATE TABLE CLIENT (
    client_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20),
    email VARCHAR(100) UNIQUE NOT NULL,
    risk_profile_wallet VARCHAR(50) NOT NULL,
    risk_profile_form VARCHAR(50) NOT NULL,
    compliance BOOLEAN NOT NULL,
    advisor_id INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (advisor_id) REFERENCES ADVISOR(advisor_id) ON DELETE RESTRICT,
    CONSTRAINT check_updated_at CHECK (updated_at >= created_at)
);

-- Tabela INVESTMENT (Investimento)
CREATE TABLE INVESTMENT (
    investment_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    risk VARCHAR(50),
    type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_updated_at CHECK (updated_at >= created_at)
);

-- Tabela CLIENT_INVESTMENT (Investimentos do Cliente)
CREATE TABLE CLIENT_INVESTMENT (
    client_id INTEGER NOT NULL,
    investment_id INTEGER NOT NULL,
    investment_date TIMESTAMP NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity >= 0),
    invested_amount DECIMAL(25,2) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (client_id, investment_id),
    FOREIGN KEY (client_id) REFERENCES CLIENT(client_id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES INVESTMENT(investment_id) ON DELETE RESTRICT,
    CONSTRAINT check_updated_at CHECK (updated_at >= created_at)
);

-- Tabela para rastrear mudanças de status de compliance
CREATE TABLE compliance_history (
    history_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    previous_status BOOLEAN,
    new_status BOOLEAN,
    change_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES CLIENT(client_id) ON DELETE CASCADE
);

-- Tabela ACTIVITY_LOG
CREATE TABLE ACTIVITY_LOG (
    log_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    investment_id INTEGER,
    action VARCHAR(50) NOT NULL,
    details TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES CLIENT(client_id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES INVESTMENT(investment_id) ON DELETE SET NULL
);

-- Índices para melhorar a performance de consultas frequentes
CREATE INDEX idx_client_advisor ON CLIENT(advisor_id);
CREATE INDEX idx_client_investment_date ON CLIENT_INVESTMENT(investment_date);
CREATE INDEX idx_investment_type ON INVESTMENT(type);
CREATE INDEX idx_client_investment_amount ON CLIENT_INVESTMENT (invested_amount);
CREATE INDEX idx_compliance_history_client ON compliance_history(client_id);
CREATE INDEX idx_activity_log_client ON ACTIVITY_LOG(client_id);

-- Regras de negócio implementadas como constraints e triggers

-- Função para verificar se há não conformidade entre os perfis de risco
CREATE OR REPLACE FUNCTION check_risk_profile_compliance()
RETURNS TRIGGER AS $$
BEGIN
    -- Se os perfis de risco forem diferentes, marcar como não conforme (compliance = true)
    IF NEW.risk_profile_wallet != NEW.risk_profile_form THEN
        NEW.compliance := TRUE;
    ELSE
        NEW.compliance := FALSE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar o trigger na tabela CLIENT
CREATE TRIGGER check_client_compliance
BEFORE INSERT OR UPDATE OF risk_profile_wallet, risk_profile_form ON CLIENT
FOR EACH ROW
EXECUTE FUNCTION check_risk_profile_compliance();

-- Função para atualizar o timestamp de updated_at
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar o trigger de atualização de timestamp em todas as tabelas
CREATE TRIGGER update_advisor_timestamp
BEFORE UPDATE ON ADVISOR
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_client_timestamp
BEFORE UPDATE ON CLIENT
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_investment_timestamp
BEFORE UPDATE ON INVESTMENT
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_client_investment_timestamp
BEFORE UPDATE ON CLIENT_INVESTMENT
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- Função para calcular perfil de risco da carteira
CREATE OR REPLACE FUNCTION calculate_wallet_risk_profile(p_client_id INTEGER)
RETURNS VARCHAR(50) AS $$
DECLARE
    total_amount DECIMAL(25,2);
    high_risk_amount DECIMAL(25,2);
    medium_risk_amount DECIMAL(25,2);
    risk_profile VARCHAR(50);
BEGIN
    -- Calcular o valor total investido
    SELECT COALESCE(SUM(invested_amount), 0) INTO total_amount
    FROM CLIENT_INVESTMENT
    WHERE client_id = p_client_id;
    
    -- Se não houver investimentos, retornar Conservador como padrão
    IF total_amount = 0 THEN
        RETURN 'Conservador';
    END IF;
    
    -- Calcular o valor investido em ativos de alto risco
    SELECT COALESCE(SUM(ci.invested_amount), 0) INTO high_risk_amount
    FROM CLIENT_INVESTMENT ci
    JOIN INVESTMENT i ON ci.investment_id = i.investment_id
    WHERE ci.client_id = p_client_id AND i.risk = 'Alto';
    
    -- Calcular o valor investido em ativos de médio risco
    SELECT COALESCE(SUM(ci.invested_amount), 0) INTO medium_risk_amount
    FROM CLIENT_INVESTMENT ci
    JOIN INVESTMENT i ON ci.investment_id = i.investment_id
    WHERE ci.client_id = p_client_id AND i.risk = 'Médio';
    
    -- Determinar o perfil com base nas proporções
    -- Arrojado: mais de 30% em alto risco
    -- Moderado: menos de 30% em alto risco, mais de 30% em médio+alto
    -- Conservador: menos de 30% em médio+alto
    IF (high_risk_amount / total_amount) > 0.3 THEN
        risk_profile := 'Arrojado';
    ELSIF ((high_risk_amount + medium_risk_amount) / total_amount) > 0.3 THEN
        risk_profile := 'Moderado';
    ELSE
        risk_profile := 'Conservador';
    END IF;
    
    RETURN risk_profile;
END;
$$ LANGUAGE plpgsql;

-- Função para atualizar perfil de risco da carteira (INSERT/UPDATE)
CREATE OR REPLACE FUNCTION update_wallet_risk_profile()
RETURNS TRIGGER AS $$
DECLARE
    new_risk_profile VARCHAR(50);
BEGIN
    -- Calcular o novo perfil de risco
    SELECT calculate_wallet_risk_profile(NEW.client_id) INTO new_risk_profile;
    
    -- Atualizar o perfil de risco da carteira do cliente
    UPDATE CLIENT
    SET risk_profile_wallet = new_risk_profile,
        updated_at = CURRENT_TIMESTAMP
    WHERE client_id = NEW.client_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Função para atualizar perfil de risco da carteira (DELETE)
CREATE OR REPLACE FUNCTION update_wallet_risk_profile_delete()
RETURNS TRIGGER AS $$
DECLARE
    new_risk_profile VARCHAR(50);
BEGIN
    -- Calcular o novo perfil de risco usando OLD.client_id
    SELECT calculate_wallet_risk_profile(OLD.client_id) INTO new_risk_profile;
    
    -- Atualizar o perfil de risco da carteira do cliente
    UPDATE CLIENT
    SET risk_profile_wallet = new_risk_profile,
        updated_at = CURRENT_TIMESTAMP
    WHERE client_id = OLD.client_id;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Função para validar compatibilidade de investimento
CREATE OR REPLACE FUNCTION is_investment_compatible_with_profile(
    client_risk VARCHAR(50),
    investment_risk VARCHAR(50)
) RETURNS BOOLEAN AS $$
BEGIN
    -- Lógica de compatibilidade baseada na CVM 175
    -- Conservador: só pode ter investimentos de Baixo risco
    -- Moderado: pode ter investimentos de Baixo e Médio risco
    -- Arrojado/Sofisticado: pode ter investimentos de qualquer risco
    
    IF client_risk = 'Conservador' THEN
        RETURN investment_risk = 'Baixo';
    ELSIF client_risk = 'Moderado' THEN
        RETURN investment_risk IN ('Baixo', 'Médio');
    ELSIF client_risk IN ('Arrojado', 'Sofisticado') THEN
        RETURN TRUE; -- Pode investir em qualquer nível de risco
    ELSE
        RETURN FALSE; -- Perfil desconhecido, não permitir
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Função para validar investimento antes da inserção
CREATE OR REPLACE FUNCTION validate_investment_compatibility()
RETURNS TRIGGER AS $$
DECLARE
    client_profile VARCHAR(50);
    investment_risk VARCHAR(50);
BEGIN
    -- Buscar perfil do cliente e risco do investimento
    SELECT c.risk_profile_form, i.risk 
    INTO client_profile, investment_risk
    FROM CLIENT c, INVESTMENT i
    WHERE c.client_id = NEW.client_id AND i.investment_id = NEW.investment_id;
    
    -- Validar compatibilidade
    IF NOT is_investment_compatible_with_profile(client_profile, investment_risk) THEN
        RAISE EXCEPTION 'Investimento incompatível com perfil de risco do cliente. Cliente: %, Investimento: %', 
                       client_profile, investment_risk;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar os triggers de atualização de perfil de risco
CREATE TRIGGER update_client_risk_profile_after_insert
AFTER INSERT ON CLIENT_INVESTMENT
FOR EACH ROW
EXECUTE FUNCTION update_wallet_risk_profile();

CREATE TRIGGER update_client_risk_profile_after_update
AFTER UPDATE OF investment_id, invested_amount ON CLIENT_INVESTMENT
FOR EACH ROW
EXECUTE FUNCTION update_wallet_risk_profile();

CREATE TRIGGER update_client_risk_profile_after_delete
AFTER DELETE ON CLIENT_INVESTMENT
FOR EACH ROW
EXECUTE FUNCTION update_wallet_risk_profile_delete();

-- Trigger para validar compatibilidade antes de inserir investimento
CREATE TRIGGER validate_investment_before_insert
BEFORE INSERT ON CLIENT_INVESTMENT
FOR EACH ROW
EXECUTE FUNCTION validate_investment_compatibility();

-- Função para registrar atividades no log
CREATE OR REPLACE FUNCTION log_activity(
    p_client_id INTEGER,
    p_investment_id INTEGER,
    p_action VARCHAR(50),
    p_details TEXT
) RETURNS VOID AS $$
BEGIN
    INSERT INTO ACTIVITY_LOG (client_id, investment_id, action, details)
    VALUES (p_client_id, p_investment_id, p_action, p_details);
END;
$$ LANGUAGE plpgsql;

-- Trigger para registrar mudanças de compliance
CREATE OR REPLACE FUNCTION log_compliance_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.compliance IS DISTINCT FROM NEW.compliance THEN
        PERFORM log_activity(
            NEW.client_id,
            NULL,
            CASE WHEN NEW.compliance THEN 'NON_COMPLIANT' ELSE 'COMPLIANT' END,
            'Cliente mudou para ' || CASE WHEN NEW.compliance THEN 'não conforme' ELSE 'conforme' END ||
            '. Perfil formulário: ' || NEW.risk_profile_form || ', Perfil carteira: ' || NEW.risk_profile_wallet
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER log_client_compliance_changes
AFTER UPDATE OF compliance ON CLIENT
FOR EACH ROW
EXECUTE FUNCTION log_compliance_changes();

-- Trigger para registrar mudanças de status de compliance no histórico
CREATE OR REPLACE FUNCTION track_compliance_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.compliance IS DISTINCT FROM NEW.compliance THEN
        INSERT INTO compliance_history (client_id, previous_status, new_status)
        VALUES (NEW.client_id, OLD.compliance, NEW.compliance);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER track_client_compliance_changes
AFTER UPDATE OF compliance ON CLIENT
FOR EACH ROW
EXECUTE FUNCTION track_compliance_changes();

-- View para clientes não conformes
CREATE OR REPLACE VIEW vw_non_compliant_clients AS
SELECT 
    c.client_id,
    c.name,
    c.email,
    c.risk_profile_form,
    c.risk_profile_wallet,
    a.advisor_id,
    COUNT(ci.investment_id) AS total_investments,
    SUM(ci.invested_amount) AS total_invested,
    -- Quando o cliente entrou em não conformidade
    (SELECT MAX(change_date) 
     FROM compliance_history 
     WHERE client_id = c.client_id AND new_status = TRUE) AS non_compliance_since
FROM 
    CLIENT c
JOIN 
    ADVISOR a ON c.advisor_id = a.advisor_id
LEFT JOIN 
    CLIENT_INVESTMENT ci ON c.client_id = ci.client_id
WHERE 
    c.compliance = TRUE
GROUP BY 
    c.client_id, c.name, c.email, c.risk_profile_form, c.risk_profile_wallet, a.advisor_id;

-- Procedure para dados normalizados de investimento
CREATE OR REPLACE PROCEDURE prepare_normalized_investment_data(OUT normalized_data REFCURSOR)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Abrir o cursor para retornar os dados normalizados
    OPEN normalized_data FOR
    WITH stats AS (
        SELECT 
            MIN(invested_amount) AS min_amount, 
            MAX(invested_amount) AS max_amount 
        FROM CLIENT_INVESTMENT
        WHERE invested_amount > 0
    )
    SELECT 
        ci.client_id,
        ci.investment_id,
        CASE 
            WHEN stats.max_amount = stats.min_amount THEN 0.5 -- Evitar divisão por zero
            ELSE (ci.invested_amount - stats.min_amount) / (stats.max_amount - stats.min_amount)
        END AS normalized_amount
    FROM 
        CLIENT_INVESTMENT ci,
        stats
    WHERE 
        ci.invested_amount > 0;
END;
$$;

-- Comentários nas tabelas e colunas para documentação
COMMENT ON TABLE ADVISOR IS 'Assessores de investimento responsáveis pelos clientes';
COMMENT ON COLUMN ADVISOR.advisor_id IS 'Identificador único do assessor';
COMMENT ON COLUMN ADVISOR.uid IS 'Identificador único universal do assessor para integração com sistemas externos';
COMMENT ON COLUMN ADVISOR.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN ADVISOR.updated_at IS 'Data e hora da última atualização do registro';

COMMENT ON TABLE CLIENT IS 'Clientes da plataforma de investimentos';
COMMENT ON COLUMN CLIENT.client_id IS 'Identificador único do cliente';
COMMENT ON COLUMN CLIENT.name IS 'Nome completo do cliente';
COMMENT ON COLUMN CLIENT.phone_number IS 'Número de telefone do cliente';
COMMENT ON COLUMN CLIENT.email IS 'Endereço de e-mail do cliente (único)';
COMMENT ON COLUMN CLIENT.risk_profile_wallet IS 'Perfil de risco baseado na carteira atual';
COMMENT ON COLUMN CLIENT.risk_profile_form IS 'Perfil de risco baseado no formulário de avaliação';
COMMENT ON COLUMN CLIENT.compliance IS 'Indica se há divergência entre os perfis de risco (true=não conforme, false=conforme)';
COMMENT ON COLUMN CLIENT.advisor_id IS 'Referência ao assessor responsável pelo cliente';
COMMENT ON COLUMN CLIENT.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN CLIENT.updated_at IS 'Data e hora da última atualização do registro';

COMMENT ON TABLE INVESTMENT IS 'Investimentos disponíveis na plataforma';
COMMENT ON COLUMN INVESTMENT.investment_id IS 'Identificador único do investimento';
COMMENT ON COLUMN INVESTMENT.name IS 'Nome do investimento';
COMMENT ON COLUMN INVESTMENT.risk IS 'Nível de risco do investimento (Baixo, Médio, Alto)';
COMMENT ON COLUMN INVESTMENT.type IS 'Tipo do investimento';
COMMENT ON COLUMN INVESTMENT.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN INVESTMENT.updated_at IS 'Data e hora da última atualização do registro';

COMMENT ON TABLE CLIENT_INVESTMENT IS 'Investimentos que cada cliente possui';
COMMENT ON COLUMN CLIENT_INVESTMENT.client_id IS 'Referência ao cliente';
COMMENT ON COLUMN CLIENT_INVESTMENT.investment_id IS 'Referência ao investimento';
COMMENT ON COLUMN CLIENT_INVESTMENT.investment_date IS 'Data em que o investimento foi adquirido';
COMMENT ON COLUMN CLIENT_INVESTMENT.quantity IS 'Quantidade do investimento adquirido';
COMMENT ON COLUMN CLIENT_INVESTMENT.invested_amount IS 'Montante financeiro aplicado no investimento';
COMMENT ON COLUMN CLIENT_INVESTMENT.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN CLIENT_INVESTMENT.updated_at IS 'Data e hora da última atualização do registro';

COMMENT ON TABLE compliance_history IS 'Histórico de mudanças de status de compliance dos clientes';
COMMENT ON COLUMN compliance_history.history_id IS 'Identificador único do histórico';
COMMENT ON COLUMN compliance_history.client_id IS 'Referência ao cliente';
COMMENT ON COLUMN compliance_history.previous_status IS 'Status anterior de compliance';
COMMENT ON COLUMN compliance_history.new_status IS 'Novo status de compliance';
COMMENT ON COLUMN compliance_history.change_date IS 'Data e hora da mudança';

COMMENT ON TABLE ACTIVITY_LOG IS 'Log de atividades e mudanças importantes do sistema';
COMMENT ON COLUMN ACTIVITY_LOG.log_id IS 'Identificador único do log';
COMMENT ON COLUMN ACTIVITY_LOG.client_id IS 'Referência ao cliente envolvido na ação';
COMMENT ON COLUMN ACTIVITY_LOG.investment_id IS 'Referência ao investimento (opcional)';
COMMENT ON COLUMN ACTIVITY_LOG.action IS 'Tipo de ação realizada';
COMMENT ON COLUMN ACTIVITY_LOG.details IS 'Detalhes adicionais da ação';
COMMENT ON COLUMN ACTIVITY_LOG.created_at IS 'Data e hora da ação';

COMMENT ON VIEW vw_non_compliant_clients IS 'View que lista todos os clientes em situação de não conformidade';

COMMENT ON FUNCTION calculate_wallet_risk_profile(INTEGER) IS 'Calcula o perfil de risco baseado na carteira atual do cliente';
COMMENT ON FUNCTION is_investment_compatible_with_profile(VARCHAR, VARCHAR) IS 'Verifica se um investimento é compatível com o perfil de risco do cliente';
COMMENT ON FUNCTION log_activity(INTEGER, INTEGER, VARCHAR, TEXT) IS 'Registra uma atividade no log do sistema';