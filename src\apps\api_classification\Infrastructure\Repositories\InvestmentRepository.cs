using Microsoft.EntityFrameworkCore;
using Pati.ClassificationService.Application.Repositories;
using Pati.ClassificationService.Domain;
using Pati.ClassificationService.Infrastructure.Data;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pati.ClassificationService.Infrastructure.Repositories
{
    public class InvestmentRepository : IInvestmentRepository
    {
        private readonly ClassificationDbContext _context;

        public InvestmentRepository(ClassificationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<Investment?> GetByIdAsync(int investmentId)
        {
            return await _context.Investments
                .FirstOrDefaultAsync(i => i.InvestmentId == investmentId);
        }

        public async Task<IEnumerable<Investment>> GetAllAsync()
        {
            return await _context.Investments.ToListAsync();
        }

        public async Task<IEnumerable<Investment>> GetByTypeAsync(string type)
        {
            return await _context.Investments
                .Where(i => i.Type.ToLower() == type.ToLower())
                .ToListAsync();
        }

        public async Task<IEnumerable<Investment>> GetByRiskAsync(string risk)
        {
            return await _context.Investments
                .Where(i => i.Risk.ToLower() == risk.ToLower())
                .ToListAsync();
        }

        public async Task AddAsync(Investment investment)
        {
            if (investment == null)
                throw new ArgumentNullException(nameof(investment));

            await _context.Investments.AddAsync(investment);
        }

        public async Task UpdateAsync(Investment investment)
        {
            if (investment == null)
                throw new ArgumentNullException(nameof(investment));

            _context.Investments.Update(investment);
            await Task.CompletedTask;
        }

        public async Task DeleteAsync(int investmentId)
        {
            var investment = await _context.Investments.FindAsync(investmentId);
            if (investment != null)
            {
                _context.Investments.Remove(investment);
            }
        }

        public async Task<bool> ExistsAsync(int investmentId)
        {
            return await _context.Investments.AnyAsync(i => i.InvestmentId == investmentId);
        }
    }
}
