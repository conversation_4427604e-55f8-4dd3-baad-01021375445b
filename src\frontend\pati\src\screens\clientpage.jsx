import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, SafeAreaView, StatusBar, TextInput, Alert } from 'react-native';
import { Header } from '../components/ClientHeader/ClientHeader';
import InvestmentCard from '../components/InvestmentCard/InvestmentCard';
import ProtectedRoute from '../components/common/ProtectedRoute';
import { useAuth } from '../contexts/AuthContext';

export default function ClientPage({ navigation, route }) {
  // Estado para controlar qual tab está selecionada
  const [selectedTab, setSelectedTab] = useState('recomendacoes');
  // Estado para controlar a busca
  const [searchVisible, setSearchVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  
  const { signOut } = useAuth();

  // Dados do cliente - pode vir via route.params ou ser mockado
  const clientData = route?.params?.clientData || {
    name: "<PERSON>",
    profile: "Conservador",
    totalInvestment: "27.358,00",
    lastContact: "22/04/2025",
    status: "Inconforme"
  };

  // Dados mockados para os investimentos recomendados
  const recommendedInvestments = [
    {
      name: "Trend DI",
      gestora: "XP Investimentos",
      classe: "Renda Fixa"
    },
    {
      name: "Tesouro IPCA+ 2035",
      gestora: "Tesouro Nacional",
      classe: "Renda Fixa"
    },
    {
      name: "CDB Premium 120%",
      gestora: "Banco Inter",
      classe: "Renda Fixa"
    },
    {
      name: "Fundo Conservador FIC",
      gestora: "BTG Pactual",
      classe: "Multimercado"
    },
    {
      name: "LCI Imobiliária",
      gestora: "Itaú Unibanco",
      classe: "Renda Fixa"
    },
    {
      name: "Debênture Incentivada",
      gestora: "Santander Asset",
      classe: "Renda Fixa"
    },
    {
      name: "CRA Verde Sustentável",
      gestora: "Votorantim Asset",
      classe: "Renda Fixa"
    }
  ];

  // Dados mockados para a carteira (você pode personalizar conforme necessário)
  const portfolioInvestments = [
    {
      name: "Fundo Alaska Black",
      gestora: "Alaska Asset",
      classe: "Ações"
    },
    {
      name: "Tesouro Selic 2027",
      gestora: "Tesouro Nacional",
      classe: "Renda Fixa"
    },
    {
      name: "Verde Asset RF",
      gestora: "Verde Asset",
      classe: "Renda Fixa"
    },
    {
      name: "Cripto Index Fund",
      gestora: "Hashdex",
      classe: "Criptomoedas"
    },
    {
      name: "FIIS Shopping Centers",
      gestora: "XP Investimentos",
      classe: "Fundos Imobiliários"
    }
  ];

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleSearchPress = () => {
    setSearchVisible(!searchVisible);
  };

  const handleSearchBlur = () => {
    setSearchVisible(false);
  };
  const handleLogoPress = () => {
    handleLogout();
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Tem certeza que deseja sair?',
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Sair',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await signOut();
              if (result.success) {
                // AuthContext automaticamente redireciona para Login
              } else {
                Alert.alert('Erro', 'Erro ao fazer logout');
              }
            } catch (error) {
              Alert.alert('Erro', 'Ocorreu um erro inesperado');
            }
          },
        },
      ]
    );
  };

  const handleTabPress = (tab) => {
    setSelectedTab(tab);
  };

  // Função para remover acentos e espaços extras
  const normalizeText = (str) => {
    return str.normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .trim()
      .toLowerCase();
  };

  // Função para filtrar investimentos baseado na busca
  const getFilteredInvestments = () => {
    const investments = selectedTab === 'recomendacoes' ? recommendedInvestments : portfolioInvestments;
    
    if (!searchText) {
      return investments;
    }
    
    const normalizedSearch = normalizeText(searchText);
    
    return investments.filter(investment =>
      normalizeText(investment.name).includes(normalizedSearch) ||
      normalizeText(investment.gestora).includes(normalizedSearch) ||
      normalizeText(investment.classe).includes(normalizedSearch)
    );
  };

  const isInconforme = clientData.status === 'Inconforme';
  const currentInvestments = getFilteredInvestments();  const sectionTitle = selectedTab === 'recomendacoes' ? 'Fundos recomendados' : 'Carteira';
  const sectionSubtitle = selectedTab === 'recomendacoes' 
    ? 'Aqui estão algumas recomendações de investimentos para o usuário voltar ao seu perfil de risco ideal.'
    : 'Aqui estão os investimentos que o usuário possui na sua carteira.';

  return (
    <ProtectedRoute navigation={navigation}>
      <View style={styles.container}>
      <StatusBar backgroundColor="rgba(252, 247, 237, 1)" barStyle="dark-content" />
      
      {/* Header */}
      <Header 
        onBackPress={handleBackPress}
        onSearchPress={handleSearchPress}
        onLogoPress={handleLogoPress}
      />

      {/* Search Dropdown */}
      {searchVisible && (
        <View style={styles.searchDropdown}>
          <TextInput
            style={styles.searchInput}
            placeholder={`Pesquisar em ${selectedTab === 'recomendacoes' ? 'recomendações' : 'carteira'}...`}
            value={searchText}
            onChangeText={setSearchText}
            onBlur={handleSearchBlur}
            autoFocus={true}
          />
        </View>
      )}
      
      <SafeAreaView style={styles.safeArea}>
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Client Info Section */}
          <View style={styles.clientInfoSection}>
            <View style={styles.clientHeader}>
              <Text style={styles.clientName}>{clientData.name}</Text>
              <View style={[styles.statusBadge, isInconforme ? styles.statusInconforme : styles.statusConforme]}>
                <Text style={styles.statusText}>{clientData.status}</Text>
              </View>
            </View>
            
            <View style={styles.clientDetails}>
              <Text style={styles.detailText}>Perfil: {clientData.profile}</Text>
              <Text style={styles.detailText}>Investimento Total: R$ {clientData.totalInvestment}</Text>
              <Text style={styles.detailText}>Último Contato: {clientData.lastContact}</Text>
            </View>
          </View>

          {/* Tab Selector */}
          <View style={styles.tabSectionContainer}>
            <View style={styles.tabContainer}>
              <TouchableOpacity 
                style={[
                  styles.tab, 
                  selectedTab === 'recomendacoes' ? styles.tabSelected : styles.tabUnselected
                ]}
                onPress={() => handleTabPress('recomendacoes')}
              >
                <Text style={[
                  styles.tabText,
                  selectedTab === 'recomendacoes' ? styles.tabTextSelected : styles.tabTextUnselected
                ]}>
                  Recomendações
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.tab, 
                  selectedTab === 'carteira' ? styles.tabSelected : styles.tabUnselected
                ]}
                onPress={() => handleTabPress('carteira')}
              >
                <Text style={[
                  styles.tabText,
                  selectedTab === 'carteira' ? styles.tabTextSelected : styles.tabTextUnselected
                ]}>
                  Carteira
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Content Section */}
          <View style={styles.fundsSection}>
            <Text style={styles.sectionTitle}>{sectionTitle}</Text>
            <Text style={styles.sectionSubtitle}>
              {sectionSubtitle}
            </Text>
            
            <View style={styles.investmentsList}>
              {currentInvestments.map((investment, index) => (
                <InvestmentCard
                  key={index}
                  name={investment.name}
                  gestora={investment.gestora}
                  classe={investment.classe}
                />
              ))}
              {currentInvestments.length === 0 && searchText !== '' && (
                <Text style={styles.noResultsText}>
                  Nenhum investimento encontrado com "{searchText}"
                </Text>
              )}            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
    </ProtectedRoute>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
  },
  searchDropdown: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    zIndex: 1000,
  },
  searchInput: {
    fontSize: 16,
    color: '#333',
    height: 40,
  },
  clientInfoSection: {
    backgroundColor: '#722F37',
    paddingHorizontal: 20,
    paddingBottom: 30,
    paddingTop: 10,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  clientName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  statusBadge: {
    borderRadius: 15,
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  statusConforme: {
    backgroundColor: '#24A49D',
  },
  statusInconforme: {
    backgroundColor: '#EE3480',
  },
  statusText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  clientDetails: {
    marginBottom: 8,
  },
  detailText: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 4,
  },
  // Seção das tabs movida para fora da header
  tabSectionContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'transparent',
    borderRadius: 16,
    overflow: 'hidden',
    gap: 2,
  },
  tab: {
    flex: 1,
    minHeight: 48,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 24,
  },
  tabSelected: {
    backgroundColor: '#C71262', // Rosa quando selecionado
  },
  tabUnselected: {
    backgroundColor: 'rgba(80, 90, 102, 0.50)', // Cinza quando não selecionado
    borderRadius: 4,
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  tabTextSelected: {
    color: '#fff',
  },
  tabTextUnselected: {
    color: '#fff',
  },
  fundsSection: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    lineHeight: 20,
  },
  investmentsList: {
    gap: 8,
  },
  noResultsText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 20,
    fontStyle: 'italic',
  },
});