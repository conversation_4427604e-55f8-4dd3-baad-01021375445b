using api_data_presentation.Models.Entities;
using api_data_presentation.Repositories.Interfaces;
using MongoDB.Driver;
using Microsoft.Extensions.Logging;

namespace api_data_presentation.Repositories.MongoDB
{
    public class RecommendationRepository : IRecommendationRepository
    {
        private readonly IMongoClient _mongoClient;
        private readonly IMongoDatabase _mongoDatabase;
        private readonly ILogger<RecommendationRepository> _logger;
        private const string CollectionName = "recommendations";

        public RecommendationRepository(string connectionString, string databaseName, ILogger<RecommendationRepository> logger)
        {
            _logger = logger;

            if (string.IsNullOrWhiteSpace(connectionString))
                throw new ArgumentException("MongoDB connection string cannot be null or empty.", nameof(connectionString));

            if (string.IsNullOrWhiteSpace(databaseName))
                throw new ArgumentException("MongoDB database name cannot be null or empty.", nameof(databaseName));

            try
            {
                _logger.LogInformation("Tentando conectar ao MongoDB com string de conexão: {ConnectionString}", connectionString);
                _mongoClient = new MongoClient(connectionString);
                _mongoDatabase = _mongoClient.GetDatabase(databaseName);
                
                // Tenta listar as coleções para verificar se a conexão está funcionando
                var collections = _mongoDatabase.ListCollectionNames().ToList();
                _logger.LogInformation("Conexão com MongoDB estabelecida com sucesso. Coleções disponíveis: {Collections}", string.Join(", ", collections));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao conectar com MongoDB: {Message}", ex.Message);
                throw;
            }
        }

        public async Task<MongoRecommendation?> GetRecommendationsByClientIdAsync(string clientId)
        {
            try
            {
                _logger.LogInformation("Buscando recomendações para o cliente {ClientId}", clientId);
                var collection = _mongoDatabase.GetCollection<MongoRecommendation>(CollectionName);
                var result = await collection.Find(r => r.ClientId == clientId).FirstOrDefaultAsync();
                
                if (result == null)
                    _logger.LogWarning("Nenhuma recomendação encontrada para o cliente {ClientId}", clientId);
                else
                    _logger.LogInformation("Recomendações encontradas para o cliente {ClientId}", clientId);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar recomendações do MongoDB para o cliente {ClientId}: {Message}", clientId, ex.Message);
                throw;
            }
        }
    }
}
