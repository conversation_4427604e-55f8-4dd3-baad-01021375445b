import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, SafeAreaView, StatusBar, TextInput, Alert } from 'react-native';
import { Header } from '../components/Header/header';
import ClientCard from '../components/ClientCard/ClientCard';
import Chart from '../components/Chart/Chart';
import { useAuth } from '../contexts/AuthContext';
import ProtectedRoute from '../components/common/ProtectedRoute';

export default function Homepage({ navigation }) {
  const [searchVisible, setSearchVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filterVisible, setFilterVisible] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    status: '',
    profile: '',
    investment: '',
    date: ''
  });
  const { signOut, user } = useAuth();

  // Dados mockados para os clientes
  const clientsData = [
    {
      name: "<PERSON>",
      profile: "Conservador",
      totalInvestment: "50.000,00",
      lastContact: "15/05/2025",
      status: "Conforme"
    },
    {
      name: "<PERSON>",
      profile: "Moderado",
      totalInvestment: "120.000,00",
      lastContact: "20/05/2025",
      status: "Inconforme"
    },
    {
      name: "Pedro Oliveira",
      profile: "Arrojado",
      totalInvestment: "85.000,00",
      lastContact: "22/05/2025",
      status: "Conforme"
    },
    {
      name: "Ana Costa",
      profile: "Conservador",
      totalInvestment: "200.000,00",
      lastContact: "25/05/2025",
      status: "Conforme"
    },
    {
      name: "Carlos Pereira",
      profile: "Moderado",
      totalInvestment: "75.000,00",
      lastContact: "10/05/2025",
      status: "Inconforme"
    }
  ];

  const handleSearchPress = () => {
    setSearchVisible(!searchVisible);
    setFilterVisible(false); // Fecha o filtro quando abre a busca
  };

  const handleSearchBlur = () => {
    setSearchVisible(false); // Esconde o dropdown quando o teclado some
  };

  const handleFilterPress = () => {
    setFilterVisible(!filterVisible);
    setSearchVisible(false); // Fecha a busca quando abre o filtro
  };

  const handleFilterSelect = (filterType, value) => {
    setSelectedFilters(prev => ({
      ...prev,
      [filterType]: prev[filterType] === value ? '' : value
    }));
  };

  const clearFilters = () => {
    setSelectedFilters({
      status: '',
      profile: '',
      investment: '',
      date: ''
    });
  };

  // Função para converter valor de investimento para número
  const parseInvestmentValue = (investment) => {
    return parseFloat(investment.replace(/\./g, '').replace(',', '.'));
  };

  // Função para remover acentos e espaços extras
  const normalizeText = (str) => {
    return str.normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .trim()
      .toLowerCase();
  };

  // Função para converter data para comparação
  const parseDate = (dateString) => {
    const [day, month, year] = dateString.split('/');
    return new Date(year, month - 1, day);
  };

  // Filtrar clientes por nome e filtros aplicados
  const getFilteredClients = () => {
    let filtered = clientsData.filter(client =>
      normalizeText(client.name).includes(normalizeText(searchText))
    );

    // Filtrar por status
    if (selectedFilters.status) {
      filtered = filtered.filter(client => client.status === selectedFilters.status);
    }

    // Filtrar por perfil
    if (selectedFilters.profile) {
      filtered = filtered.filter(client => client.profile === selectedFilters.profile);
    }

    // Ordenar por investimento
    if (selectedFilters.investment) {
      filtered = filtered.sort((a, b) => {
        const valueA = parseInvestmentValue(a.totalInvestment);
        const valueB = parseInvestmentValue(b.totalInvestment);
        
        return selectedFilters.investment === 'maior' ? valueB - valueA : valueA - valueB;
      });
    }

    // Ordenar por data
    if (selectedFilters.date) {
      filtered = filtered.sort((a, b) => {
        const dateA = parseDate(a.lastContact);
        const dateB = parseDate(b.lastContact);
        
        return selectedFilters.date === 'recente' ? dateB - dateA : dateA - dateB;
      });
    }

    return filtered;
  };

  const filteredClients = getFilteredClients();
  const handleLogoPress = () => {
    handleLogout();
  };
  const handleClientPress = (clientData) => {
    navigation.navigate('ClientPage', { clientData });
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Tem certeza que deseja sair?',
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Sair',
          style: 'destructive',          onPress: async () => {
            try {
              const result = await signOut();
              if (result.success) {
                // AuthContext automaticamente redireciona para Login
              } else {
                Alert.alert('Erro', 'Erro ao fazer logout');
              }
            } catch (error) {
              Alert.alert('Erro', 'Ocorreu um erro inesperado');
            }
          },
        },
      ]
    );
  };  return (
    <ProtectedRoute navigation={navigation}>
      <View style={styles.container}>
        <StatusBar backgroundColor="rgba(252, 247, 237, 1)" barStyle="dark-content" />
          {/* Header */}        <Header 
          onSearchPress={handleSearchPress}
          onFilterPress={handleFilterPress}
          onLogoPress={handleLogoPress}
        />

        {/* Search Dropdown */}
        {searchVisible && (
          <View style={styles.searchDropdown}>
            <TextInput
              style={styles.searchInput}
              placeholder="Pesquisar por nome..."
              value={searchText}
              onChangeText={setSearchText}
              onBlur={handleSearchBlur}
              autoFocus={true}
            />
          </View>
        )}

        {/* Filter Dropdown */}
        {filterVisible && (
          <View style={styles.filterDropdown}>
            <View style={styles.filterHeader}>
              <Text style={styles.filterTitle}>Filtros</Text>
              <TouchableOpacity onPress={clearFilters} style={styles.clearButton}>
                <Text style={styles.clearButtonText}>Limpar</Text>
              </TouchableOpacity>
            </View>

            {/* Status Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Status</Text>
              <View style={styles.filterOptions}>
                <TouchableOpacity
                  style={[
                    styles.filterOption,
                    selectedFilters.status === 'Conforme' && styles.filterOptionSelected
                  ]}
                  onPress={() => handleFilterSelect('status', 'Conforme')}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilters.status === 'Conforme' && styles.filterOptionTextSelected
                  ]}>
                    Conforme
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.filterOption,
                    selectedFilters.status === 'Inconforme' && styles.filterOptionSelected
                  ]}
                  onPress={() => handleFilterSelect('status', 'Inconforme')}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilters.status === 'Inconforme' && styles.filterOptionTextSelected
                  ]}>
                    Inconforme
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Profile Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Perfil</Text>
              <View style={styles.filterOptions}>
                <TouchableOpacity
                  style={[
                    styles.filterOption,
                    selectedFilters.profile === 'Conservador' && styles.filterOptionSelected
                  ]}
                  onPress={() => handleFilterSelect('profile', 'Conservador')}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilters.profile === 'Conservador' && styles.filterOptionTextSelected
                  ]}>
                    Conservador
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.filterOption,
                    selectedFilters.profile === 'Moderado' && styles.filterOptionSelected
                  ]}
                  onPress={() => handleFilterSelect('profile', 'Moderado')}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilters.profile === 'Moderado' && styles.filterOptionTextSelected
                  ]}>
                    Moderado
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.filterOption,
                    selectedFilters.profile === 'Arrojado' && styles.filterOptionSelected
                  ]}
                  onPress={() => handleFilterSelect('profile', 'Arrojado')}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilters.profile === 'Arrojado' && styles.filterOptionTextSelected
                  ]}>
                    Arrojado
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Investment Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Investimento Total</Text>
              <View style={styles.filterOptions}>
                <TouchableOpacity
                  style={[
                    styles.filterOption,
                    selectedFilters.investment === 'maior' && styles.filterOptionSelected
                  ]}
                  onPress={() => handleFilterSelect('investment', 'maior')}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilters.investment === 'maior' && styles.filterOptionTextSelected
                  ]}>
                    Maior para Menor
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.filterOption,
                    selectedFilters.investment === 'menor' && styles.filterOptionSelected
                  ]}
                  onPress={() => handleFilterSelect('investment', 'menor')}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilters.investment === 'menor' && styles.filterOptionTextSelected
                  ]}>
                    Menor para Maior
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Date Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Último Contato</Text>
              <View style={styles.filterOptions}>
                <TouchableOpacity
                  style={[
                    styles.filterOption,
                    selectedFilters.date === 'recente' && styles.filterOptionSelected
                  ]}
                  onPress={() => handleFilterSelect('date', 'recente')}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilters.date === 'recente' && styles.filterOptionTextSelected
                  ]}>
                    Mais Recente
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.filterOption,
                    selectedFilters.date === 'antiga' && styles.filterOptionSelected
                  ]}
                  onPress={() => handleFilterSelect('date', 'antiga')}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilters.date === 'antiga' && styles.filterOptionTextSelected
                  ]}>
                    Mais Antiga
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
        
        <SafeAreaView style={styles.safeArea}>
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Chart Section */}
            <View style={styles.chartSection}>
              <Chart clientsData={filteredClients} />
            </View>

            {/* Clients Section */}
            <View style={styles.clientsSection}>
              {filteredClients.map((client, index) => (
                <ClientCard
                  key={index}
                  name={client.name}
                  profile={client.profile}
                  totalInvestment={client.totalInvestment}
                  lastContact={client.lastContact}
                  status={client.status}
                  onPress={handleClientPress}
                />
              ))}
              {filteredClients.length === 0 && (searchText !== '' || Object.values(selectedFilters).some(filter => filter !== '')) && (
                <Text style={styles.noResultsText}>
                  {searchText !== '' 
                    ? `Nenhum cliente encontrado com o nome "${searchText}"`
                    : 'Nenhum cliente encontrado com os filtros aplicados'
                  }
                </Text>
              )}
            </View>
          </ScrollView>
        </SafeAreaView>
      </View>
    </ProtectedRoute>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  overlay: {
    flex: 1,
  },
  searchDropdown: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    zIndex: 1000,
  },
  searchInput: {
    fontSize: 16,
    color: '#333',
    height: 40,
  },
  chartSection: {
    marginTop: 20,
    marginBottom: 30,
  },
  chartTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  chartPlaceholder: {
    backgroundColor: '#F8F9FA',
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
    minHeight: 150,
    justifyContent: 'center',
  },
  chartPlaceholderText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
  },
  mockChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 10,
  },
  chartBar: {
    width: 20,
    height: 50,
    backgroundColor: '#EE3480',
    borderRadius: 4,
  },
  clientsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  logoutBtn: {
    width: '100%',
    height: 50,
    backgroundColor: '#24A49D',
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  logoutBtnText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  noResultsText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 20,
    fontStyle: 'italic',
  },
  filterDropdown: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    zIndex: 1000,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#EE3480',
    borderRadius: 15,
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  filterSection: {
    marginBottom: 20,
  },
  filterSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#F8F9FA',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  filterOptionSelected: {
    backgroundColor: '#EE3480',
    borderColor: '#EE3480',
  },
  filterOptionText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  filterOptionTextSelected: {
    color: '#fff',
  },
});