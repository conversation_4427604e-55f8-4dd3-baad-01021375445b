namespace api_data_presentation.Models.DTOs
{
    public class ClientRecommendationsDto
    {
        public int ClientId { get; set; }
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string RiskProfileForm { get; set; } = string.Empty;
        public string RiskProfileWallet { get; set; } = string.Empty;
        public List<RecommendedInvestmentDto> RecommendedInvestments { get; set; } = new();
    }

    public class RecommendedInvestmentDto
    {
        public int InvestmentId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string? Risk { get; set; }
        public double Score { get; set; }
    }
}