using Microsoft.AspNetCore.Mvc;
using ApiMlRecommender.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ApiMlRecommender.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class HealthController : ControllerBase
    {
        private readonly MongoDbService _mongoDbService;
        private readonly PostgreSqlService _postgreSqlService;
        private readonly ILogger<HealthController> _logger;

        public HealthController(MongoDbService mongoDbService, PostgreSqlService postgreSqlService, ILogger<HealthController> logger)
        {
            _mongoDbService = mongoDbService;
            _postgreSqlService = postgreSqlService;
            _logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(typeof(object), 200)]
        public IActionResult GetHealth()
        {
            try
            {
                return Ok(new
                {
                    Status = "Healthy",
                    Service = "API ML Recommender",
                    Timestamp = DateTime.Now,
                    Version = "1.0.0",
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
                });
            }
            catch (Exception ex)
            {   
                _logger.LogError(ex, "Erro no health check básico");
                return StatusCode(500, new { Status = "Unhealthy", Error = ex.Message });
            }
        }

        [HttpGet("detailed")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(typeof(object), 503)]
        public async Task<IActionResult> GetDetailedHealth()
        {
            var healthStatus = new
            {
                Status = "Healthy",
                Service = "API ML Recommender",
                Timestamp = DateTime.Now,
                Version = "1.0.0",
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                Dependencies = new Dictionary<string, object>()
            };

            try
            {
                // Testa MongoDB
                try
                {
                    var mongoStatus = await _mongoDbService.CheckConnectionAsync();
                    healthStatus.Dependencies.Add("MongoDB", new
                    {
                        Status = mongoStatus ? "Healthy" : "Unhealthy",
                        Message = mongoStatus ? "Conexão OK" : "Falha na conexão"
                    });
                }
                catch (Exception mongoEx)
                {
                    healthStatus.Dependencies.Add("MongoDB", new { Status = "Unhealthy", Error = mongoEx.Message });
                }

                // Testa PostgreSQL
                try
                {
                    var pgStatus = await _postgreSqlService.CheckConnectionAsync();
                    healthStatus.Dependencies.Add("PostgreSQL", new
                    {
                        Status = pgStatus ? "Healthy" : "Unhealthy",
                        Message = pgStatus ? "Conexão OK" : "Falha na conexão"
                    });
                }
                catch (Exception pgEx)
                {
                    healthStatus.Dependencies.Add("PostgreSQL", new { Status = "Unhealthy", Error = pgEx.Message });
                }

                var hasUnhealthyDependency = healthStatus.Dependencies.Values
                    .Any(dep => ((dynamic)dep).Status == "Unhealthy");

                if (hasUnhealthyDependency)
                {
                    return StatusCode(503, new
                    {
                        Status = "Degraded",
                        healthStatus.Service,
                        healthStatus.Timestamp,
                        healthStatus.Version,
                        healthStatus.Environment,
                        healthStatus.Dependencies
                    });
                }

                return Ok(healthStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro no health check detalhado");
                return StatusCode(503, new
                {
                    Status = "Unhealthy",
                    Service = "API ML Recommender",
                    Timestamp = DateTime.Now,
                    Error = ex.Message
                });
            }
        }

        [HttpGet("info")]
        [ProducesResponseType(typeof(object), 200)]
        public IActionResult GetInfo()
        {
            return Ok(new
            {
                Name = "API ML Recommender",
                Description = "API para recomendações de investimentos baseada em Machine Learning",
                Version = "1.0.0",
                Endpoints = new[]
                {
                    "POST /api/recomendacao/gerar",
                    "POST /api/recomendacao/score",
                    "POST /api/recomendacao/iniciar",
                    "GET /api/recomendacao/teste",
                    "GET /api/recomendacao/cliente/{clientId}",
                    "GET /api/health",
                    "GET /api/health/detailed",
                    "GET /api/health/info"
                },
                Documentation = "/swagger",
                Timestamp = DateTime.Now
            });
        }
    }
}