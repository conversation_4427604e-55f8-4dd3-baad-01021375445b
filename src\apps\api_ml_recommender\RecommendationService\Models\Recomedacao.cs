namespace RecommendationService.Models
{
    public class Recomendacao
    {
        public string ClienteId { get; set; } = string.Empty;
        public List<AtivoRecomendado> AtivosRecomendados { get; set; } = new List<AtivoRecomendado>();
        public AnaliseCarteira AnaliseCarteira { get; set; } = new AnaliseCarteira();
        public List<Inconsistencia> Inconsistencias { get; set; } = new List<Inconsistencia>();
        public DateTime DataGeracao { get; set; } = DateTime.Now;
        public StatusConformidade StatusConformidade { get; set; }
    }

    public class AtivoRecomendado
    {
        public string NomeAtivo { get; set; } = string.Empty;
        public decimal Score { get; set; } 
        public TipoAtivo TipoAtivo { get; set; }
        public PerfilRisco RiscoAtivo { get; set; }
        public string Justificativa { get; set; } = string.Empty;
    }

    public class AnaliseCarteira
    {
        public decimal PercentualRendaFixaAtual { get; set; } 
        public decimal PercentualRendaFixaIdeal { get; set; } 
        public decimal PercentualRendaVariavelAtual { get; set; } 
        public decimal PercentualRendaVariavelIdeal { get; set; } 
        public decimal ValorTotalCarteira { get; set; }
    }

    public class Inconsistencia
    {
        public TipoInconsistencia Tipo { get; set; }
        public string Descricao { get; set; } = string.Empty;
        public string? AtivoRelacionado { get; set; }
        public string SugestaoCorrecao { get; set; } = string.Empty;
        public GravidadeInconsistencia Gravidade { get; set; }
    }

    public enum TipoInconsistencia
    {
        AtivoIncompativelPerfil = 1,    // Ativo muito arriscado para o perfil
        ProporçãoInadequada = 2,        // RF/RV fora do ideal
        ConcentraçãoExcessiva = 3,      // Muito dinheiro em poucos ativos
        FaltaDiversificacao = 4         // Carteira mal diversificada
    }

    public enum GravidadeInconsistencia
    {
        Baixa = 1,      // Pequeno ajuste
        Media = 2,      // Precisa corrigir
        Alta = 3,       // Urgente
        Critica = 4     // Risco regulatório alto
    }

    public enum StatusConformidade
    {
        Conforme = 1,                       // Tudo OK
        PequenasInconsistencias = 2,        // Pequenos ajustes
        InconsistenciasSignificativas = 3,  // Precisa corrigir
        NaoConforme = 4                     // Fora das regras CVM
    }
}