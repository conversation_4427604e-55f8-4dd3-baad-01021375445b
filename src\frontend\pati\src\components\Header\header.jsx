import React from 'react';
import { View, TouchableOpacity, StyleSheet, Image, Platform, StatusBar, Text } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import SearchIcon from '../../../assets/images/search-icon.png';
import LogoHeader from '../../../assets/images/logo-header.png';
import FilterIcon from '../../../assets/images/filter-icon.png';
import Icon from 'react-native-vector-icons/Ionicons';

export function Header(props) {
  const insets = useSafeAreaInsets();
  
  return (
    <View style={[styles.root, { paddingTop: insets.top }]} testID={props.testID ?? "header-container"}>
      <View style={styles.background} />
      
      <View style={styles.content}>
        {/* Filter Icon */}
        <TouchableOpacity 
          style={styles.iconContainer}
          onPress={props.onFilterPress}
          testID="filter-button"
          activeOpacity={0.7}
        >
          <Image 
            source={FilterIcon}
            style={styles.icon}
            resizeMode="contain"
          />
        </TouchableOpacity>

        {/* Logo */}
        <TouchableOpacity 
          style={styles.logoContainer}
          onPress={props.onLogoPress}
          testID="logo-button"
          activeOpacity={0.7}
        >
          <Image 
            source={LogoHeader}
            style={styles.logo}
            resizeMode="contain"
          />
        </TouchableOpacity>        {/* Search Icon */}
        <TouchableOpacity 
          style={styles.iconContainer}
          onPress={props.onSearchPress}
          testID="search-button"
          activeOpacity={0.7}
        >
          <Image 
            source={SearchIcon}
            style={styles.icon}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  root: {
    width: '100%',
    position: 'relative',
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(252, 247, 237, 1)',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
    height: 64,
  },
  iconContainer: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 22,
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  icon: {
    width: 24,
    height: 24,
  },
  logo: {
    width: 40,
    height: 40,
  },
});