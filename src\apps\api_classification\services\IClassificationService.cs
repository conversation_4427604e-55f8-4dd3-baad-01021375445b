using Pati.ClassificationService.DTOs;

namespace Pati.ClassificationService.Application.Services
{
    /// <summary>
    /// Interface para o serviço de classificação de carteiras
    /// Implementa as regras CVM 175 e integração com modelo SVD
    /// </summary>
    public interface IClassificationService
    {
        /// <summary>
        /// Classifica uma carteira individual identificando inconformidades
        /// </summary>
        /// <param name="portfolioInput">Dados da carteira para classificação</param>
        /// <returns>Resultado da classificação com inconformidades detectadas</returns>
        Task<PortfolioOutputDto> ClassifyPortfolioAsync(PortfolioInputDto portfolioInput);

        /// <summary>
        /// Classifica todas as carteiras do sistema em lote
        /// </summary>
        /// <returns>Resultado do processamento em lote</returns>
        Task<object> ClassifyAllPortfoliosAsync();

        /// <summary>
        /// Verifica se um risco é permitido para um perfil específico
        /// Baseado nas regras CVM 175
        /// </summary>
        /// <param name="profile">Perfil do cliente (Conservador, Moderado, Arrojado, Sofisticado)</param>
        /// <param name="risk">Risco do ativo</param>
        /// <returns>True se o risco é permitido para o perfil</returns>
        bool IsRiskAllowedForProfile(string profile, string risk);

        /// <summary>
        /// Obtém as proporções ideais de Renda Fixa e Renda Variável para um perfil
        /// </summary>
        /// <param name="profile">Perfil do cliente</param>
        /// <returns>Proporções ideais (FixedIncome, VariableIncome)</returns>
        (decimal FixedIncome, decimal VariableIncome) GetIdealProportions(string profile);
    }
}
