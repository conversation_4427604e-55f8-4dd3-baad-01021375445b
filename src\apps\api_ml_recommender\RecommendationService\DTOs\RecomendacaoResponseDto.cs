using System;
using System.Collections.Generic;

namespace ApiMlRecommender.DTOs
{
    public class RecomendacaoResponseDto
    {
        public string Cpf { get; set; } = string.Empty;
        public string Nome { get; set; } = string.Empty;
        public string PerfilRisco { get; set; } = string.Empty;
        public DateTime DataRecomendacao { get; set; }
        public string StatusConformidade { get; set; } = string.Empty;
        public decimal ScoreAdequacao { get; set; }
        public decimal ValorTotalCarteira { get; set; }
        public List<InvestimentoAnalisadoDto> InvestimentosAtuais { get; set; } = new List<InvestimentoAnalisadoDto>();
        public List<RecomendacaoAjusteDto> RecomendacoesAjuste { get; set; } = new List<RecomendacaoAjusteDto>();
        public ResumoRecomendacaoDto Resumo { get; set; } = new ResumoRecomendacaoDto();
    }

    public class InvestimentoAnalisadoDto
    {
        public string Codigo { get; set; } = string.Empty;
        public string Nome { get; set; } = string.Empty;
        public string Categoria { get; set; } = string.Empty;
        public string Subcategoria { get; set; } = string.Empty;
        public string TipoAtivo { get; set; } = string.Empty;
        public int NivelRisco { get; set; }
        public string Risco { get; set; } = string.Empty;
        public decimal ValorAtual { get; set; }
        public decimal PercentualCarteira { get; set; }
        public decimal RentabilidadeEsperada { get; set; }
        public bool EstaAdequado { get; set; }
        public decimal PercentualIdeal { get; set; }
        public decimal DiferencaPercentual { get; set; }
        public string Observacoes { get; set; } = string.Empty;
    }

    public class RecomendacaoAjusteDto
    {
        public string TipoAcao { get; set; } = string.Empty;
        public string CodigoInvestimento { get; set; } = string.Empty;
        public string NomeInvestimento { get; set; } = string.Empty;
        public decimal ValorRecomendado { get; set; }
        public decimal PercentualRecomendado { get; set; }
        public int Prioridade { get; set; }
        public string Justificativa { get; set; } = string.Empty;
    }

    public class ResumoRecomendacaoDto
    {
        public int TotalAjustes { get; set; }
        public int AjustesAltaPrioridade { get; set; }
        public decimal ValorTotalMovimentacao { get; set; }
        public string TempoEstimadoImplementacao { get; set; } = string.Empty;
        public List<string> BeneficiosEsperados { get; set; } = new List<string>();
        public List<string> RiscosIdentificados { get; set; } = new List<string>();
        public string ObservacoesGerais { get; set; } = string.Empty;
        public decimal ProporcaoRendaFixaAtual { get; set; }
        public decimal ProporcaoRendaVariavelAtual { get; set; }
        public decimal ProporcaoRendaFixaIdeal { get; set; }
        public decimal ProporcaoRendaVariavelIdeal { get; set; }
        public string PerfilInvestimentos { get; set; } = string.Empty;
    }
}