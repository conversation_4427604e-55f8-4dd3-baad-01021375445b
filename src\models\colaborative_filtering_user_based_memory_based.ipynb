{"cells": [{"cell_type": "markdown", "metadata": {"id": "bvmOaAPgK_6A"}, "source": ["## Sistema de recomendação por filtragem colaborativa em memória baseada em usuário \n", "\n", "<PERSON><PERSON>, iremos desenvolver o sistema de recomendação de ativos para clientes com inconformidade de perfil de risco.\n", "\n", "Esse sistema de recomendação se concentra em modelos de filtragem colaborativa, inicialmente, em memória.\n", "\n", "O funcionamento esperado desse sistema requer uma base de dados com informações dos clientes, suas carteiras de ativos e os perfis de risco atribuídos ao cliente."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OhYFfDweKVQ7", "outputId": "0f3d9324-e3bb-4132-c4fc-e00d73e57275"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading...\n", "From: https://drive.google.com/uc?id=1LWLaiFtoznlznDF-C7uZk9t_0c_KPvI4\n", "To: /content/inconformidades.csv\n", "100% 19.4M/19.4M [00:00<00:00, 82.7MB/s]\n"]}], "source": ["# Baixando o dataset\n", "!gdown 1LWLaiFtoznlznDF-C7uZk9t_0c_KPvI4"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "OAwvLugWLNl7"}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 989}, "id": "HGiWwdimLPmk", "outputId": "017b4958-e788-4186-c2ca-17ffb158bc6f"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df\",\n  \"rows\": 30,\n  \"fields\": [\n    {\n      \"column\": \"Unnamed: 0\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 8,\n        \"min\": 286115,\n        \"max\": 286145,\n        \"num_unique_values\": 30,\n        \"samples\": [\n          286142,\n          286130,\n          286138\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Conta\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 5543,\n        \"min\": 6178808,\n        \"max\": 6209173,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          6209173,\n          6178808\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Financeiro\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 13970758.915245749,\n        \"min\": 192119.6818,\n        \"max\": 77208247.24043,\n        \"num_unique_values\": 29,\n        \"samples\": [\n          1576248.909183,\n          516989.24\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Quantidade\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 854.9272497112551,\n        \"min\": 1.0,\n        \"max\": 4744.0,\n        \"num_unique_values\": 19,\n        \"samples\": [\n          70.0,\n          700.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Nome Ativo\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 24,\n        \"samples\": [\n          \"ISAEA8 IPCA\",\n          \"LFSC REF\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Tipo Ativo\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"TITULOS RF PUBLICOS BRASIL\",\n          \"DEBENTURES BRASIL\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Perfil Carteira\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"Moderado\",\n          \"Sofisticado\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Tipo Ativo Renda\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"Renda Fixa\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Perfil Investimentos\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"Conservador\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe"}, "text/html": ["\n", "  <div id=\"df-c2662065-a3f7-4724-a25f-8cd942a5279f\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Financeiro</th>\n", "      <th>Quantidade</th>\n", "      <th>Nome Ativo</th>\n", "      <th>Tipo Ativo</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Tipo Ativo Renda</th>\n", "      <th>Perfil Investimentos</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>201269</th>\n", "      <td>286115</td>\n", "      <td>6178808</td>\n", "      <td>2.730230e+05</td>\n", "      <td>70.0</td>\n", "      <td>NTNB IPCA</td>\n", "      <td>TITULOS RF PUBLICOS BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201270</th>\n", "      <td>286116</td>\n", "      <td>6178808</td>\n", "      <td>1.191032e+06</td>\n", "      <td>1200.0</td>\n", "      <td>BCPSA5 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201271</th>\n", "      <td>286117</td>\n", "      <td>6178808</td>\n", "      <td>8.613741e+05</td>\n", "      <td>197.0</td>\n", "      <td>NTNB IPCA</td>\n", "      <td>TITULOS RF PUBLICOS BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201272</th>\n", "      <td>286118</td>\n", "      <td>6178808</td>\n", "      <td>4.700984e+05</td>\n", "      <td>500.0</td>\n", "      <td>EQMAA2 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201273</th>\n", "      <td>286119</td>\n", "      <td>6178808</td>\n", "      <td>5.779123e+05</td>\n", "      <td>616.0</td>\n", "      <td>RAIZ13 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201274</th>\n", "      <td>286120</td>\n", "      <td>6178808</td>\n", "      <td>6.536128e+05</td>\n", "      <td>700.0</td>\n", "      <td>KLBNA5 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201275</th>\n", "      <td>286121</td>\n", "      <td>6178808</td>\n", "      <td>8.403594e+05</td>\n", "      <td>900.0</td>\n", "      <td>KLBNA5 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201276</th>\n", "      <td>286122</td>\n", "      <td>6178808</td>\n", "      <td>8.912677e+05</td>\n", "      <td>800.0</td>\n", "      <td>CCROB6 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201277</th>\n", "      <td>286123</td>\n", "      <td>6178808</td>\n", "      <td>9.773820e+05</td>\n", "      <td>1000.0</td>\n", "      <td>CMGDB0 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201278</th>\n", "      <td>286124</td>\n", "      <td>6178808</td>\n", "      <td>5.975583e+05</td>\n", "      <td>600.0</td>\n", "      <td>ISAEB8 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201279</th>\n", "      <td>286125</td>\n", "      <td>6178808</td>\n", "      <td>3.980845e+05</td>\n", "      <td>400.0</td>\n", "      <td>ISAEA8 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201280</th>\n", "      <td>286126</td>\n", "      <td>6178808</td>\n", "      <td>6.390098e+05</td>\n", "      <td>700.0</td>\n", "      <td>GASC27 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201281</th>\n", "      <td>286127</td>\n", "      <td>6178808</td>\n", "      <td>9.417575e+05</td>\n", "      <td>1000.0</td>\n", "      <td>TQUI13 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201282</th>\n", "      <td>286128</td>\n", "      <td>6178808</td>\n", "      <td>7.878563e+05</td>\n", "      <td>48.0</td>\n", "      <td>LFT REF</td>\n", "      <td>TITULOS RF PUBLICOS BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201283</th>\n", "      <td>286129</td>\n", "      <td>6178808</td>\n", "      <td>3.820366e+05</td>\n", "      <td>400.0</td>\n", "      <td>AESLA7 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201284</th>\n", "      <td>286130</td>\n", "      <td>6178808</td>\n", "      <td>8.118277e+05</td>\n", "      <td>850.0</td>\n", "      <td>AESLA7 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201285</th>\n", "      <td>286131</td>\n", "      <td>6178808</td>\n", "      <td>5.169892e+05</td>\n", "      <td>500.0</td>\n", "      <td>RECV11 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201286</th>\n", "      <td>286132</td>\n", "      <td>6178808</td>\n", "      <td>1.921197e+05</td>\n", "      <td>200.0</td>\n", "      <td>CPFPA7 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201287</th>\n", "      <td>286133</td>\n", "      <td>6178808</td>\n", "      <td>1.921197e+05</td>\n", "      <td>200.0</td>\n", "      <td>CPFPA7 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201288</th>\n", "      <td>286134</td>\n", "      <td>6178808</td>\n", "      <td>9.736436e+05</td>\n", "      <td>1000.0</td>\n", "      <td>GASC16 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201289</th>\n", "      <td>286135</td>\n", "      <td>6178808</td>\n", "      <td>4.161794e+05</td>\n", "      <td>1.0</td>\n", "      <td>LFSC REF</td>\n", "      <td>TITULOS RF PRIVADOS BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201290</th>\n", "      <td>286136</td>\n", "      <td>6178808</td>\n", "      <td>7.878587e+05</td>\n", "      <td>48.0</td>\n", "      <td>LFT REF</td>\n", "      <td>TITULOS RF PUBLICOS BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201291</th>\n", "      <td>286137</td>\n", "      <td>6178808</td>\n", "      <td>9.896126e+05</td>\n", "      <td>1000.0</td>\n", "      <td>MRSAB1 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201292</th>\n", "      <td>286138</td>\n", "      <td>6178808</td>\n", "      <td>4.988558e+05</td>\n", "      <td>500.0</td>\n", "      <td>EGIEA1 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201293</th>\n", "      <td>286139</td>\n", "      <td>6178808</td>\n", "      <td>9.946717e+05</td>\n", "      <td>1000.0</td>\n", "      <td>CLCD27 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201294</th>\n", "      <td>286140</td>\n", "      <td>6178808</td>\n", "      <td>1.333360e+06</td>\n", "      <td>1400.0</td>\n", "      <td>ELTE12 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201295</th>\n", "      <td>286141</td>\n", "      <td>6178808</td>\n", "      <td>3.890751e+05</td>\n", "      <td>400.0</td>\n", "      <td>RSAN16 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201296</th>\n", "      <td>286142</td>\n", "      <td>6178808</td>\n", "      <td>4.049765e+05</td>\n", "      <td>423.0</td>\n", "      <td>CMGDB1 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201297</th>\n", "      <td>286143</td>\n", "      <td>6178808</td>\n", "      <td>1.576249e+06</td>\n", "      <td>1557.0</td>\n", "      <td>EGIEB4 IPCA</td>\n", "      <td>DEBENTURES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201298</th>\n", "      <td>286145</td>\n", "      <td>6209173</td>\n", "      <td>7.720825e+07</td>\n", "      <td>4744.0</td>\n", "      <td>LFT REF</td>\n", "      <td>TITULOS RF PUBLICOS BRASIL</td>\n", "      <td>Moderado</td>\n", "      <td>Renda Fixa</td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-c2662065-a3f7-4724-a25f-8cd942a5279f')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-c2662065-a3f7-4724-a25f-8cd942a5279f button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-c2662065-a3f7-4724-a25f-8cd942a5279f');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-72f9b0a9-f78a-4b66-a9b3-be8ba3588179\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-72f9b0a9-f78a-4b66-a9b3-be8ba3588179')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-72f9b0a9-f78a-4b66-a9b3-be8ba3588179 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["        Unnamed: 0    Conta    Financeiro  Quantidade   Nome Ativo  \\\n", "201269      286115  6178808  2.730230e+05        70.0    NTNB IPCA   \n", "201270      286116  6178808  1.191032e+06      1200.0  BCPSA5 IPCA   \n", "201271      286117  6178808  8.613741e+05       197.0    NTNB IPCA   \n", "201272      286118  6178808  4.700984e+05       500.0  EQMAA2 IPCA   \n", "201273      286119  6178808  5.779123e+05       616.0  RAIZ13 IPCA   \n", "201274      286120  6178808  6.536128e+05       700.0  KLBNA5 IPCA   \n", "201275      286121  6178808  8.403594e+05       900.0  KLBNA5 IPCA   \n", "201276      286122  6178808  8.912677e+05       800.0  CCROB6 IPCA   \n", "201277      286123  6178808  9.773820e+05      1000.0  CMGDB0 IPCA   \n", "201278      286124  6178808  5.975583e+05       600.0  ISAEB8 IPCA   \n", "201279      286125  6178808  3.980845e+05       400.0  ISAEA8 IPCA   \n", "201280      286126  6178808  6.390098e+05       700.0  GASC27 IPCA   \n", "201281      286127  6178808  9.417575e+05      1000.0  TQUI13 IPCA   \n", "201282      286128  6178808  7.878563e+05        48.0      LFT REF   \n", "201283      286129  6178808  3.820366e+05       400.0  AESLA7 IPCA   \n", "201284      286130  6178808  8.118277e+05       850.0  AESLA7 IPCA   \n", "201285      286131  6178808  5.169892e+05       500.0  RECV11 IPCA   \n", "201286      286132  6178808  1.921197e+05       200.0  CPFPA7 IPCA   \n", "201287      286133  6178808  1.921197e+05       200.0  CPFPA7 IPCA   \n", "201288      286134  6178808  9.736436e+05      1000.0  GASC16 IPCA   \n", "201289      286135  6178808  4.161794e+05         1.0     LFSC REF   \n", "201290      286136  6178808  7.878587e+05        48.0      LFT REF   \n", "201291      286137  6178808  9.896126e+05      1000.0  MRSAB1 IPCA   \n", "201292      286138  6178808  4.988558e+05       500.0  EGIEA1 IPCA   \n", "201293      286139  6178808  9.946717e+05      1000.0  CLCD27 IPCA   \n", "201294      286140  6178808  1.333360e+06      1400.0  ELTE12 IPCA   \n", "201295      286141  6178808  3.890751e+05       400.0  RSAN16 IPCA   \n", "201296      286142  6178808  4.049765e+05       423.0  CMGDB1 IPCA   \n", "201297      286143  6178808  1.576249e+06      1557.0  EGIEB4 IPCA   \n", "201298      286145  6209173  7.720825e+07      4744.0      LFT REF   \n", "\n", "                        Tipo Ativo Perfil Carteira Tipo Ativo Renda  \\\n", "201269  TITULOS RF PUBLICOS BRASIL     Sofisticado       Renda Fixa   \n", "201270           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201271  TITULOS RF PUBLICOS BRASIL     Sofisticado       Renda Fixa   \n", "201272           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201273           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201274           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201275           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201276           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201277           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201278           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201279           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201280           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201281           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201282  TITULOS RF PUBLICOS BRASIL     Sofisticado       Renda Fixa   \n", "201283           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201284           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201285           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201286           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201287           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201288           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201289  TITULOS RF PRIVADOS BRASIL     Sofisticado       Renda Fixa   \n", "201290  TITULOS RF PUBLICOS BRASIL     Sofisticado       Renda Fixa   \n", "201291           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201292           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201293           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201294           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201295           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201296           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201297           DEBENTURES BRASIL     Sofisticado       Renda Fixa   \n", "201298  TITULOS RF PUBLICOS BRASIL        Moderado       Renda Fixa   \n", "\n", "       Perfil Investimentos  \n", "201269          Conservador  \n", "201270          Conservador  \n", "201271          Conservador  \n", "201272          Conservador  \n", "201273          Conservador  \n", "201274          Conservador  \n", "201275          Conservador  \n", "201276          Conservador  \n", "201277          Conservador  \n", "201278          Conservador  \n", "201279          Conservador  \n", "201280          Conservador  \n", "201281          Conservador  \n", "201282          Conservador  \n", "201283          Conservador  \n", "201284          Conservador  \n", "201285          Conservador  \n", "201286          Conservador  \n", "201287          Conservador  \n", "201288          Conservador  \n", "201289          Conservador  \n", "201290          Conservador  \n", "201291          Conservador  \n", "201292          Conservador  \n", "201293          Conservador  \n", "201294          Conservador  \n", "201295          Conservador  \n", "201296          Conservador  \n", "201297          Conservador  \n", "201298          Conservador  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('/content/inconformidades.csv')\n", "df.tail(30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 81}, "id": "1kqVr1H9fX2L", "outputId": "0776ad96-d067-480d-fb67-0cc7def0570b"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df[df['Nome Ativo'] == 'MGLU3 Q229']\",\n  \"rows\": 1,\n  \"fields\": [\n    {\n      \"column\": \"Unnamed: 0\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": null,\n        \"min\": 227179,\n        \"max\": 227179,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          227179\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Conta\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": null,\n        \"min\": 2872799,\n        \"max\": 2872799,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          2872799\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Financeiro\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": null,\n        \"min\": 1266.0,\n        \"max\": 1266.0,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          1266.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Quantidade\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": null,\n        \"min\": 100.0,\n        \"max\": 100.0,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          100.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Nome Ativo\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"MGLU3 Q229\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Tipo Ativo\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"OPCOES S/ ACOES BRASIL\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Perfil Carteira\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"Sofisticado\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Tipo Ativo Renda\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"Renda Vari\\u00e1vel\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Perfil Investimentos\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"Conservador\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe"}, "text/html": ["\n", "  <div id=\"df-4ae8e509-1ed7-4dfc-acf6-dadc76daa042\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Financeiro</th>\n", "      <th>Quantidade</th>\n", "      <th>Nome Ativo</th>\n", "      <th>Tipo Ativo</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Tipo Ativo Renda</th>\n", "      <th>Perfil Investimentos</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>160547</th>\n", "      <td>227179</td>\n", "      <td>2872799</td>\n", "      <td>1266.0</td>\n", "      <td>100.0</td>\n", "      <td>MGLU3 Q229</td>\n", "      <td>OPCOES S/ ACOES BRASIL</td>\n", "      <td>Sofisticado</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Conservador</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-4ae8e509-1ed7-4dfc-acf6-dadc76daa042')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-4ae8e509-1ed7-4dfc-acf6-dadc76daa042 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-4ae8e509-1ed7-4dfc-acf6-dadc76daa042');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["        Unnamed: 0    Conta  Financeiro  Quantidade  Nome Ativo  \\\n", "160547      227179  2872799      1266.0       100.0  MGLU3 Q229   \n", "\n", "                    Tipo Ativo Perfil Carteira Tipo Ativo Renda  \\\n", "160547  OPCOES S/ ACOES BRASIL     Sofisticado   Renda Variável   \n", "\n", "       Perfil Investimentos  \n", "160547          Conservador  "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df['Nome Ativo'] == 'MGLU3 Q229']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EvFIfIt9OWS3", "outputId": "20c3050a-69c9-458f-c931-676530c78ca1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Unnamed: 0  Conta    Financeiro  Quantidade   Nome Ativo  \\\n", "0           0     27  16278.264089        16.0     CRI CDIE   \n", "1           1     27  25081.618073        25.0     CRA CDIE   \n", "2           2     27  47046.248881        49.0     CRA IPCA   \n", "3           3     27  36331.476685        35.0  RRRP13 IPCA   \n", "4           4     27  18995.623640        20.0  EEELA1 IPCA   \n", "\n", "                   Tipo Ativo Perfil Carteira Tipo Ativo Renda  \\\n", "0  TITULOS RF PRIVADOS BRASIL        Moderado       Renda Fixa   \n", "1  TITULOS RF PRIVADOS BRASIL        Moderado       Renda Fixa   \n", "2  TITULOS RF PRIVADOS BRASIL        Moderado       Renda Fixa   \n", "3           DEBENTURES BRASIL        Moderado       Renda Fixa   \n", "4           DEBENTURES BRASIL        Moderado       Renda Fixa   \n", "\n", "  Perfil Investimentos  \n", "0          Conservador  \n", "1          Conservador  \n", "2          Conservador  \n", "3          Conservador  \n", "4          Conservador  \n"]}], "source": ["print(df.head())"]}, {"cell_type": "markdown", "metadata": {"id": "ND7AOqyTL3SI"}, "source": ["#### Dicion<PERSON><PERSON> dos dados\n", "\n", "Entre os dados fornecidos, vale a pena ressaltar:\n", "\n", "`Per<PERSON><PERSON>`: Perfil de risco esperado para o cliente (resposta do formulário)\n", "\n", "`Perfil Investimentos`: Perfil de risco definido pela carteira de ativos atual do cliente"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nKOqzDK8N5jQ", "outputId": "fd807e7f-ce08-471d-b32e-7586a7057abc"}, "outputs": [{"data": {"text/plain": ["7207"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Nome Ativo'].nunique()"]}, {"cell_type": "markdown", "metadata": {"id": "kvEHV6GeN-nj"}, "source": ["Percebemos que existem 7200 ativos com nomes diferentes."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 489}, "id": "IYXciAZnMmI5", "outputId": "0ce032d0-e27a-487e-b472-84d37c12227a"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Nome Ativo</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CRA IPCA</th>\n", "      <td>12342</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NTNB IPCA</th>\n", "      <td>10640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CRI IPCA</th>\n", "      <td>10421</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LFT REF</th>\n", "      <td>5725</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CRI CDIE</th>\n", "      <td>5722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BRADPN R125</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BB  ON F274</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BB  ON R274</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ITUB4 R362</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>QQQ 6  V   439.78</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7207 rows × 1 columns</p>\n", "</div><br><label><b>dtype:</b> int64</label>"], "text/plain": ["Nome Ativo\n", "CRA IPCA             12342\n", "NTNB IPCA            10640\n", "CRI IPCA             10421\n", "LFT REF               5725\n", "CRI CDIE              5722\n", "                     ...  \n", "BRADPN R125              1\n", "BB  ON F274              1\n", "BB  ON R274              1\n", "ITUB4 R362               1\n", "QQQ 6  V   439.78        1\n", "Name: count, Length: 7207, dtype: int64"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Nome Ativo'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 335}, "id": "XXPsGkOPNNds", "outputId": "1d6ee962-4fe7-448c-83f6-ea3de14299f5"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>7207.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>27.931039</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>274.084979</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>4.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>12342.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div><br><label><b>dtype:</b> float64</label>"], "text/plain": ["count     7207.000000\n", "mean        27.931039\n", "std        274.084979\n", "min          1.000000\n", "25%          1.000000\n", "50%          1.000000\n", "75%          4.500000\n", "max      12342.000000\n", "Name: count, dtype: float64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Nome Ativo'].value_counts().describe()"]}, {"cell_type": "markdown", "metadata": {"id": "qXn2jbkwNvIG"}, "source": ["<PERSON><PERSON>, conseguimos observar de uma forma simples a distribuição dos dados.\n", "\n", "Os primeiros 3 quartis possuem somente até 5 ocorrências.\n", "\n", "<PERSON><PERSON> enquanto, não vamos explorar isso, mas se mantém uma janela de oportunidade para análises futuras."]}, {"cell_type": "markdown", "metadata": {"id": "cEvnUpk3QsRL"}, "source": ["### Sistema de Recomendação baseado em Filtragem Colaborativa"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ygvLn6cqQwZZ"}, "outputs": [], "source": ["interacao_matriz = pd.DataFrame(df.pivot_table(\n", "    index='Conta',\n", "    columns='Nome Ativo',\n", "    values='Financeiro',\n", "    aggfunc='mean').fillna(0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uL1yQtLhIX6h", "outputId": "3e5a8098-b0ce-48bc-d756-8f87f9fee581"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Número total de células: 32453121\n", "Número de interações: 103291\n", "Esparsidade da matriz de interações: 0.9968\n"]}], "source": ["# Obter o número total de células na matriz\n", "total_celulas = interacao_matriz.shape[0] * interacao_matriz.shape[1]\n", "\n", "# Obter o número de interações (células não zero)\n", "numero_interacoes = (interacao_matriz != 0).sum().sum()\n", "\n", "# Calcular a esparsidade\n", "esparsidade = 1 - (numero_interacoes / total_celulas)\n", "\n", "print(f\"Número total de células: {total_celulas}\")\n", "print(f\"Número de interações: {numero_interacoes}\")\n", "print(f\"Esparsidade da matriz de interações: {esparsidade:.4f}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 472}, "id": "tgyQb_6UR__S", "outputId": "5c6a26fa-694c-402e-fb26-dc8894725e65"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "interacao_matriz"}, "text/html": ["\n", "  <div id=\"df-da7a4634-35b3-444a-9d0e-97be63af47c1\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Nome Ativo</th>\n", "      <th>1832421D CN</th>\n", "      <th>1SANPN</th>\n", "      <th>2299955D CN</th>\n", "      <th>2458693D US</th>\n", "      <th>700 HK</th>\n", "      <th>8001 JP</th>\n", "      <th>8031 JP</th>\n", "      <th>8058 JP</th>\n", "      <th>9988 HK</th>\n", "      <th>A1AP34</th>\n", "      <th>...</th>\n", "      <th>YUFI11</th>\n", "      <th>Z US.</th>\n", "      <th>Z1OM34</th>\n", "      <th>ZAGH11</th>\n", "      <th>ZAMP3</th>\n", "      <th>ZAVI11</th>\n", "      <th>ZIFI11</th>\n", "      <th>ZS US</th>\n", "      <th>ZTO US</th>\n", "      <th>ZTS US</th>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>736</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>747</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6129455</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6146263</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6150189</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6178808</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6209173</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4503 rows × 7207 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-da7a4634-35b3-444a-9d0e-97be63af47c1')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-da7a4634-35b3-444a-9d0e-97be63af47c1 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-da7a4634-35b3-444a-9d0e-97be63af47c1');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-21780a55-fafb-432e-ac54-8b0a58070159\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-21780a55-fafb-432e-ac54-8b0a58070159')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-21780a55-fafb-432e-ac54-8b0a58070159 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "  <div id=\"id_d3482acd-ff05-4120-a5dc-c9478d09443d\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('interacao_matriz')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_d3482acd-ff05-4120-a5dc-c9478d09443d button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('interacao_matriz');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["Nome Ativo  1832421D CN  1SANPN  2299955D CN  2458693D US  700 HK  8001 JP  \\\n", "Conta                                                                        \n", "27                  0.0     0.0          0.0          0.0     0.0      0.0   \n", "44                  0.0     0.0          0.0          0.0     0.0      0.0   \n", "103                 0.0     0.0          0.0          0.0     0.0      0.0   \n", "736                 0.0     0.0          0.0          0.0     0.0      0.0   \n", "747                 0.0     0.0          0.0          0.0     0.0      0.0   \n", "...                 ...     ...          ...          ...     ...      ...   \n", "6129455             0.0     0.0          0.0          0.0     0.0      0.0   \n", "6146263             0.0     0.0          0.0          0.0     0.0      0.0   \n", "6150189             0.0     0.0          0.0          0.0     0.0      0.0   \n", "6178808             0.0     0.0          0.0          0.0     0.0      0.0   \n", "6209173             0.0     0.0          0.0          0.0     0.0      0.0   \n", "\n", "Nome Ativo  8031 JP  8058 JP  9988 HK  A1AP34  ...  YUFI11  Z US.  Z1OM34  \\\n", "Conta                                          ...                          \n", "27              0.0      0.0      0.0     0.0  ...     0.0    0.0     0.0   \n", "44              0.0      0.0      0.0     0.0  ...     0.0    0.0     0.0   \n", "103             0.0      0.0      0.0     0.0  ...     0.0    0.0     0.0   \n", "736             0.0      0.0      0.0     0.0  ...     0.0    0.0     0.0   \n", "747             0.0      0.0      0.0     0.0  ...     0.0    0.0     0.0   \n", "...             ...      ...      ...     ...  ...     ...    ...     ...   \n", "6129455         0.0      0.0      0.0     0.0  ...     0.0    0.0     0.0   \n", "6146263         0.0      0.0      0.0     0.0  ...     0.0    0.0     0.0   \n", "6150189         0.0      0.0      0.0     0.0  ...     0.0    0.0     0.0   \n", "6178808         0.0      0.0      0.0     0.0  ...     0.0    0.0     0.0   \n", "6209173         0.0      0.0      0.0     0.0  ...     0.0    0.0     0.0   \n", "\n", "Nome Ativo  ZAGH11  ZAMP3  ZAVI11  ZIFI11  ZS US  ZTO US  ZTS US  \n", "Conta                                                             \n", "27             0.0    0.0     0.0     0.0    0.0     0.0     0.0  \n", "44             0.0    0.0     0.0     0.0    0.0     0.0     0.0  \n", "103            0.0    0.0     0.0     0.0    0.0     0.0     0.0  \n", "736            0.0    0.0     0.0     0.0    0.0     0.0     0.0  \n", "747            0.0    0.0     0.0     0.0    0.0     0.0     0.0  \n", "...            ...    ...     ...     ...    ...     ...     ...  \n", "6129455        0.0    0.0     0.0     0.0    0.0     0.0     0.0  \n", "6146263        0.0    0.0     0.0     0.0    0.0     0.0     0.0  \n", "6150189        0.0    0.0     0.0     0.0    0.0     0.0     0.0  \n", "6178808        0.0    0.0     0.0     0.0    0.0     0.0     0.0  \n", "6209173        0.0    0.0     0.0     0.0    0.0     0.0     0.0  \n", "\n", "[4503 rows x 7207 columns]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["interacao_matriz"]}, {"cell_type": "markdown", "metadata": {"id": "-tZAgFTiRDs8"}, "source": ["<PERSON>essa et<PERSON> da análise, já reconhecemos uma grande esparsidade nos dados.\n", "\n", "<PERSON><PERSON><PERSON>, vamos procurar entender melhor esses dados."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 367}, "id": "3g9cF7dRSdz0", "outputId": "93586d20-3957-4647-f129-a9092799f0a7"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe"}, "text/html": ["\n", "  <div id=\"df-f9d8ad9f-6f9f-42ac-aa04-61964532005d\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Nome Ativo</th>\n", "      <th>1832421D CN</th>\n", "      <th>1SANPN</th>\n", "      <th>2299955D CN</th>\n", "      <th>2458693D US</th>\n", "      <th>700 HK</th>\n", "      <th>8001 JP</th>\n", "      <th>8031 JP</th>\n", "      <th>8058 JP</th>\n", "      <th>9988 HK</th>\n", "      <th>A1AP34</th>\n", "      <th>...</th>\n", "      <th>YUFI11</th>\n", "      <th>Z US.</th>\n", "      <th>Z1OM34</th>\n", "      <th>ZAGH11</th>\n", "      <th>ZAMP3</th>\n", "      <th>ZAVI11</th>\n", "      <th>ZIFI11</th>\n", "      <th>ZS US</th>\n", "      <th>ZTO US</th>\n", "      <th>ZTS US</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>4503.000000</td>\n", "      <td>4.503000e+03</td>\n", "      <td>4503.000000</td>\n", "      <td>4503.000000</td>\n", "      <td>4.503000e+03</td>\n", "      <td>4503.000000</td>\n", "      <td>4503.000000</td>\n", "      <td>4503.000000</td>\n", "      <td>4.503000e+03</td>\n", "      <td>4503.000000</td>\n", "      <td>...</td>\n", "      <td>4503.000000</td>\n", "      <td>4503.000000</td>\n", "      <td>4503.000000</td>\n", "      <td>4.503000e+03</td>\n", "      <td>4.503000e+03</td>\n", "      <td>4503.000000</td>\n", "      <td>4.503000e+03</td>\n", "      <td>4503.000000</td>\n", "      <td>4.503000e+03</td>\n", "      <td>4503.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>11.842216</td>\n", "      <td>1.199964e+03</td>\n", "      <td>0.000037</td>\n", "      <td>10.189122</td>\n", "      <td>4.325186e+02</td>\n", "      <td>122.232524</td>\n", "      <td>112.897928</td>\n", "      <td>119.581708</td>\n", "      <td>2.438533e+03</td>\n", "      <td>4.212991</td>\n", "      <td>...</td>\n", "      <td>33.046747</td>\n", "      <td>-46.383197</td>\n", "      <td>0.860040</td>\n", "      <td>9.238978e+02</td>\n", "      <td>1.017503e+03</td>\n", "      <td>24.518750</td>\n", "      <td>1.275219e+03</td>\n", "      <td>30.709608</td>\n", "      <td>2.283936e+02</td>\n", "      <td>57.167075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>794.664724</td>\n", "      <td>3.181954e+04</td>\n", "      <td>0.001951</td>\n", "      <td>683.734905</td>\n", "      <td>2.902390e+04</td>\n", "      <td>8202.339712</td>\n", "      <td>7575.947319</td>\n", "      <td>8024.458292</td>\n", "      <td>1.211289e+05</td>\n", "      <td>276.612560</td>\n", "      <td>...</td>\n", "      <td>2216.695283</td>\n", "      <td>3112.516448</td>\n", "      <td>57.712463</td>\n", "      <td>6.199296e+04</td>\n", "      <td>2.960991e+04</td>\n", "      <td>1645.315898</td>\n", "      <td>8.557280e+04</td>\n", "      <td>2060.749722</td>\n", "      <td>1.532621e+04</td>\n", "      <td>3836.162034</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>-208863.537174</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-6.686700e+05</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>53325.496547</td>\n", "      <td>1.596000e+06</td>\n", "      <td>0.124232</td>\n", "      <td>45881.618000</td>\n", "      <td>1.947631e+06</td>\n", "      <td>550413.054475</td>\n", "      <td>508379.371117</td>\n", "      <td>538476.429155</td>\n", "      <td>7.192267e+06</td>\n", "      <td>18557.400000</td>\n", "      <td>...</td>\n", "      <td>148750.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3872.760000</td>\n", "      <td>4.160000e+06</td>\n", "      <td>1.390660e+06</td>\n", "      <td>110407.930000</td>\n", "      <td>5.742311e+06</td>\n", "      <td>138285.365964</td>\n", "      <td>1.028456e+06</td>\n", "      <td>257423.337300</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8 rows × 7207 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-f9d8ad9f-6f9f-42ac-aa04-61964532005d')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-f9d8ad9f-6f9f-42ac-aa04-61964532005d button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-f9d8ad9f-6f9f-42ac-aa04-61964532005d');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-e0799ef7-2078-4309-81e7-6ea94d6cd47e\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-e0799ef7-2078-4309-81e7-6ea94d6cd47e')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-e0799ef7-2078-4309-81e7-6ea94d6cd47e button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["Nome Ativo   1832421D CN        1SANPN  2299955D CN   2458693D US  \\\n", "count        4503.000000  4.503000e+03  4503.000000   4503.000000   \n", "mean           11.842216  1.199964e+03     0.000037     10.189122   \n", "std           794.664724  3.181954e+04     0.001951    683.734905   \n", "min             0.000000  0.000000e+00     0.000000      0.000000   \n", "25%             0.000000  0.000000e+00     0.000000      0.000000   \n", "50%             0.000000  0.000000e+00     0.000000      0.000000   \n", "75%             0.000000  0.000000e+00     0.000000      0.000000   \n", "max         53325.496547  1.596000e+06     0.124232  45881.618000   \n", "\n", "Nome Ativo        700 HK        8001 JP        8031 JP        8058 JP  \\\n", "count       4.503000e+03    4503.000000    4503.000000    4503.000000   \n", "mean        4.325186e+02     122.232524     112.897928     119.581708   \n", "std         2.902390e+04    8202.339712    7575.947319    8024.458292   \n", "min         0.000000e+00       0.000000       0.000000       0.000000   \n", "25%         0.000000e+00       0.000000       0.000000       0.000000   \n", "50%         0.000000e+00       0.000000       0.000000       0.000000   \n", "75%         0.000000e+00       0.000000       0.000000       0.000000   \n", "max         1.947631e+06  550413.054475  508379.371117  538476.429155   \n", "\n", "Nome Ativo       9988 HK        A1AP34  ...         YUFI11          Z US.  \\\n", "count       4.503000e+03   4503.000000  ...    4503.000000    4503.000000   \n", "mean        2.438533e+03      4.212991  ...      33.046747     -46.383197   \n", "std         1.211289e+05    276.612560  ...    2216.695283    3112.516448   \n", "min         0.000000e+00      0.000000  ...       0.000000 -208863.537174   \n", "25%         0.000000e+00      0.000000  ...       0.000000       0.000000   \n", "50%         0.000000e+00      0.000000  ...       0.000000       0.000000   \n", "75%         0.000000e+00      0.000000  ...       0.000000       0.000000   \n", "max         7.192267e+06  18557.400000  ...  148750.000000       0.000000   \n", "\n", "Nome Ativo       Z1OM34        ZAGH11         ZAMP3         ZAVI11  \\\n", "count       4503.000000  4.503000e+03  4.503000e+03    4503.000000   \n", "mean           0.860040  9.238978e+02  1.017503e+03      24.518750   \n", "std           57.712463  6.199296e+04  2.960991e+04    1645.315898   \n", "min            0.000000  0.000000e+00 -6.686700e+05       0.000000   \n", "25%            0.000000  0.000000e+00  0.000000e+00       0.000000   \n", "50%            0.000000  0.000000e+00  0.000000e+00       0.000000   \n", "75%            0.000000  0.000000e+00  0.000000e+00       0.000000   \n", "max         3872.760000  4.160000e+06  1.390660e+06  110407.930000   \n", "\n", "Nome Ativo        ZIFI11          ZS US        ZTO US         ZTS US  \n", "count       4.503000e+03    4503.000000  4.503000e+03    4503.000000  \n", "mean        1.275219e+03      30.709608  2.283936e+02      57.167075  \n", "std         8.557280e+04    2060.749722  1.532621e+04    3836.162034  \n", "min         0.000000e+00       0.000000  0.000000e+00       0.000000  \n", "25%         0.000000e+00       0.000000  0.000000e+00       0.000000  \n", "50%         0.000000e+00       0.000000  0.000000e+00       0.000000  \n", "75%         0.000000e+00       0.000000  0.000000e+00       0.000000  \n", "max         5.742311e+06  138285.365964  1.028456e+06  257423.337300  \n", "\n", "[8 rows x 7207 columns]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# Vamos gerar estatísticas descritivas sobre a nossa matriz de interações\n", "interacao_matriz.describe()"]}, {"cell_type": "markdown", "metadata": {"id": "p3pEKa00Tf7Y"}, "source": ["<PERSON><PERSON> observando as duas primeiras colunas, isto <PERSON>, dois ativos diferentes, já podemos perceber que os valores de investimento podem variar muito, começando em 0 (o cliente não investiu naquele ativo), desde investimentos de 0.12 real, até 50 mil ou 1.5 milhão de reais\n", "\n", "<PERSON><PERSON> modo, precisamos avaliar futuramente  se a normalização dos dados pode afetar ou não a precisão do sistema de recomendação"]}, {"cell_type": "markdown", "metadata": {"id": "ARXO3T9kV-IJ"}, "source": ["#### <PERSON><PERSON><PERSON><PERSON> da Métrica de Similaridade\n", "\n", "Para realizar as recomendações de ativos, precisamos antes encontrar clientes com comportamentos similares.\n", "\n", "<PERSON>r isso, é necessário definir uma métrica para quantificar a \"proximidade\" entre os clientes.\n", "\n", "<PERSON><PERSON><PERSON>, considerando o contexto dos dados, segue-se:\n", "\n", "1. Similaridade de Cosseno: <PERSON><PERSON>, iremos avaliar o ângulo entre dois vetores para definir a sua similaridade. Dessa forma, a grandeza dos vetores não importa, sendo pouco sensível à escala (É um benefício pensando que diferentes clientes podem investir quantidades extremamente diferentes uns dos outros).\n", "\n", "2. Correla<PERSON> <PERSON> Pearson: Considera-se a magnitude e a direção entre dois vetores para calcular a sua similaridade. É adequado para abafar ruídos de 'viés' inerentes a cada usuário (se um usuário costuma sempre investir maiores quantidades financeiras)\n", "\n", "Considerando o atual contexto, iremos analisar primeiro a similaridade de cosseno."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "J_XguomyZBvD"}, "outputs": [], "source": ["from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# Calculando a similaridade de cosseno entre os usuários\n", "similaridade_usuario_cosseno = cosine_similarity(interacao_matriz)\n", "\n", "# Criamos um DataFrame para visualizar melhor as similaridades\n", "similaridade_usuario_cosseno_df = pd.DataFrame(similaridade_usuario_cosseno, index=interacao_matriz.index, columns=interacao_matriz.index)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 287}, "id": "ZKqBgL8CZP6z", "outputId": "1e62c7ed-b810-4762-db07-26ba4f62322b"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "similaridade_usuario_cosseno_df"}, "text/html": ["\n", "  <div id=\"df-a33d075d-9eac-47e2-9b68-167ddfb85fea\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th><PERSON><PERSON></th>\n", "      <th>27</th>\n", "      <th>44</th>\n", "      <th>103</th>\n", "      <th>736</th>\n", "      <th>747</th>\n", "      <th>751</th>\n", "      <th>856</th>\n", "      <th>882</th>\n", "      <th>944</th>\n", "      <th>947</th>\n", "      <th>...</th>\n", "      <th>6104670</th>\n", "      <th>6106049</th>\n", "      <th>6116234</th>\n", "      <th>6116313</th>\n", "      <th>6127250</th>\n", "      <th>6129455</th>\n", "      <th>6146263</th>\n", "      <th>6150189</th>\n", "      <th>6178808</th>\n", "      <th>6209173</th>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>1.000000</td>\n", "      <td>0.495229</td>\n", "      <td>0.440738</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.000000</td>\n", "      <td>0.471925</td>\n", "      <td>0.146625</td>\n", "      <td>0.389919</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.045135</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>0.495229</td>\n", "      <td>1.000000</td>\n", "      <td>0.563662</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.000000</td>\n", "      <td>0.746724</td>\n", "      <td>0.177633</td>\n", "      <td>0.570456</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.113346</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>0.440738</td>\n", "      <td>0.563662</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.000000</td>\n", "      <td>0.655354</td>\n", "      <td>0.081579</td>\n", "      <td>0.450269</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.158524</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>736</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000499</td>\n", "      <td>0.00000</td>\n", "      <td>0.323196</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>0.552955</td>\n", "      <td>0.0</td>\n", "      <td>0.243904</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.117535</td>\n", "      <td>0.240970</td>\n", "      <td>0.0</td>\n", "      <td>0.016872</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>747</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000499</td>\n", "      <td>1.000000</td>\n", "      <td>0.03382</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>0.000903</td>\n", "      <td>0.0</td>\n", "      <td>0.009368</td>\n", "      <td>0.192456</td>\n", "      <td>0.0</td>\n", "      <td>0.004247</td>\n", "      <td>0.125715</td>\n", "      <td>0.0</td>\n", "      <td>0.045969</td>\n", "      <td>0.227478</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 4503 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-a33d075d-9eac-47e2-9b68-167ddfb85fea')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-a33d075d-9eac-47e2-9b68-167ddfb85fea button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-a33d075d-9eac-47e2-9b68-167ddfb85fea');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-82cc8217-6b20-4c9d-987a-11868b51dd13\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-82cc8217-6b20-4c9d-987a-11868b51dd13')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-82cc8217-6b20-4c9d-987a-11868b51dd13 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["Conta   27        44        103       736       747      751       856      \\\n", "Conta                                                                        \n", "27     1.000000  0.495229  0.440738  0.000000  0.000000  0.00000  0.000000   \n", "44     0.495229  1.000000  0.563662  0.000000  0.000000  0.00000  0.000000   \n", "103    0.440738  0.563662  1.000000  0.000000  0.000000  0.00000  0.000000   \n", "736    0.000000  0.000000  0.000000  1.000000  0.000499  0.00000  0.323196   \n", "747    0.000000  0.000000  0.000000  0.000499  1.000000  0.03382  0.000000   \n", "\n", "Conta   882       944       947      ...   6104670  6106049   6116234  \\\n", "Conta                                ...                                \n", "27     0.471925  0.146625  0.389919  ...  0.000000      0.0  0.000000   \n", "44     0.746724  0.177633  0.570456  ...  0.000000      0.0  0.000000   \n", "103    0.655354  0.081579  0.450269  ...  0.000000      0.0  0.000000   \n", "736    0.000000  0.000000  0.000000  ...  0.552955      0.0  0.243904   \n", "747    0.000000  0.000000  0.000000  ...  0.000903      0.0  0.009368   \n", "\n", "Conta   6116313  6127250   6129455   6146263  6150189   6178808   6209173  \n", "Conta                                                                      \n", "27     0.000000      0.0  0.000000  0.000000      0.0  0.045135  0.000000  \n", "44     0.000000      0.0  0.000000  0.000000      0.0  0.113346  0.000000  \n", "103    0.000000      0.0  0.000000  0.000000      0.0  0.158524  0.000000  \n", "736    0.000000      0.0  0.117535  0.240970      0.0  0.016872  0.000000  \n", "747    0.192456      0.0  0.004247  0.125715      0.0  0.045969  0.227478  \n", "\n", "[5 rows x 4503 columns]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["similaridade_usuario_cosseno_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yhRV1qAMZ6j2"}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON>, precisamos encontrar os usuários mais semelhantes para um determinado cliente\n", "def encontrar_cliente_semelhantes(cliente_id, k=3, matriz_similaridade=similaridade_usuario_cosseno_df):\n", "  if cliente_id not in matriz_similaridade.index:\n", "    return None\n", "  similaridade_usuario = matriz_similaridade[cliente_id].sort_values(ascending=False)\n", "  clientes_semelhantes = similaridade_usuario[1:k+1]\n", "  return clientes_semelhantes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RaQ3Sc2Bav6n"}, "outputs": [], "source": ["cliente_id = 6129455\n", "clientes_semelhantes = encontrar_cliente_semelhantes(cliente_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5mZN2gc0TnTG", "outputId": "9dd05701-0fb1-4bec-dd6a-657e0f1298d5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Os 3 usuários mais similares ao cliente 87245 (<PERSON>ssen<PERSON>) são:\n", "Conta\n", "305736     1.0\n", "1207440    1.0\n", "6002871    1.0\n", "Name: 6129455, dtype: float64\n"]}], "source": ["if clientes_se<PERSON><PERSON><PERSON> is not None:\n", "    print(f\"\\nOs 3 usuários mais similares ao cliente {cliente_id} (Cosseno) são:\")\n", "    print(clientes_semelhantes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FlNQNfdba6-D"}, "outputs": [], "source": ["def recomendar_ativos_cosseno(user_id, top_n=5, interaction_matrix=interacao_matriz, similarity_matrix=similaridade_usuario_cosseno_df):\n", "    usuarios_similares = encontrar_cliente_semelhantes(user_id)\n", "    print(\"usuarios similares: \", usuarios_similares)\n", "    if usuarios_similares is None:\n", "        return None\n", "\n", "    # Bora encontrar os ativos investidos pelo usuário atual\n", "    ativos_usuario_atual = interaction_matrix.loc[user_id][interaction_matrix.loc[user_id] > 0].index.tolist()\n", "    print(\"ativos do usuário atual: \", ativos_usuario_atual)\n", "\n", "    recomendacoes = {}\n", "    for outro_usuario, similaridade in usuarios_similares.items():\n", "        # Ativos investidos pelo usuário similar que o usuário atual não investiu\n", "        ativos_outro_usuario = interaction_matrix.loc[outro_usuario][interaction_matrix.loc[outro_usuario] > 0].index.tolist()\n", "        print(\"ativos do outro usuário: \", ativos_outro_usuario)\n", "        ativos_para_recomendar = [ativo for ativo in ativos_outro_usuario if ativo not in ativos_usuario_atual]\n", "        print(\"ativos para recomendar: \", ativos_para_recomendar)\n", "\n", "        # Ad<PERSON><PERSON>ndo as recomendações ponderadas pela similaridade\n", "        for ativo in ativos_para_recomendar:\n", "            if ativo not in recomendacoes:\n", "                recomendacoes[ativo] = 0\n", "            recomendacoes[ativo] += similaridade * interaction_matrix.loc[outro_usuario, ativo] # Pondera pelo investimento do vizinho\n", "\n", "    # <PERSON><PERSON><PERSON><PERSON> as recomendações pela pontuação (maior primeiro)\n", "    recomendacoes_ordenadas = sorted(recomendacoes.items(), key=lambda item: item[1], reverse=True)\n", "\n", "    return recomendacoes_ordenadas[:top_n]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YHzYL_wuUqp4", "outputId": "0778b2ab-b664-4857-a76b-f2e4b485b987"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["usuarios similares:  Conta\n", "19554      0.971225\n", "1195551    0.924126\n", "1189864    0.909836\n", "Name: 87245, dtype: float64\n", "ativos do usuário atual:  ['AESOA1 IPCA', 'APFD19 IPCA', 'CBAN12 IPCA', 'CEED21 IPCA', 'CGASA2 IPCA', 'CHSF13 IPCA', 'CLIS13 IPCA', 'CLTM14 IPCA', 'COMR14 IPCA', 'COMR15 IPCA', 'CRA CDIE', 'CRA IPCA', 'CRI CDIE', 'CRI IPCA', 'CRTR12 IPCA', 'EEELA1 IPCA', 'EGIEA1 IPCA', 'EKTT11 IPCA', 'ELTE12 IPCA', 'ENAT24 IPCA', 'ENERB4 IPCA', 'ENEV15 IPCA', 'ENEVB0 IPCA', 'ENGIB2 IPCA', 'ENGIB6 IPCA', 'EQSP21 IPCA', 'ERDVC4 IPCA', 'GASC18 IPCA', 'GASC23 IPCA', 'GASC25 IPCA', 'HVSP11 IPCA', 'IRJS14 IPCA', 'ITPO14 IPCA', 'IVIAA0 IPCA', 'MRSAB1 IPCA', 'MSGT23 IPCA', 'NMCH11 IPCA', 'PEJA22 IPCA', 'POTE12 IPCA', 'RAIZ13 IPCA', 'RAIZ23 IPCA', 'RIS414 IPCA', 'RMSA12 IPCA', 'RSAN26 IPCA', 'SERI11 IPCA', 'STBP45 IPCA', 'SUMI17 IPCA', 'TRRG12 IPCA', 'UNEG11 IPCA', 'UTPS22 IPCA', 'VERO44 IPCA']\n", "ativos do outro usuário:  ['APFD19 IPCA', 'CBAN12 IPCA', 'CEED21 IPCA', 'CGASA2 IPCA', 'CHSF13 IPCA', 'CLIS13 IPCA', 'CLTM14 IPCA', 'COMR14 IPCA', 'COMR15 IPCA', 'CRA CDIE', 'CRA IPCA', 'CRI CDIE', 'CRI IPCA', 'CRTR12 IPCA', 'EEELA1 IPCA', 'EGIEA1 IPCA', 'EKTT11 IPCA', 'ELTE12 IPCA', 'ENAT24 IPCA', 'ENERB4 IPCA', 'ENEV15 IPCA', 'ENEVB0 IPCA', 'ENGIB2 IPCA', 'ENGIB6 IPCA', 'EQSP21 IPCA', 'ERDVC4 IPCA', 'GASC18 IPCA', 'GASC23 IPCA', 'GASC25 IPCA', 'HVSP11 IPCA', 'IRJS14 IPCA', 'ITPO14 IPCA', 'IVIAA0 IPCA', 'MRSAB1 IPCA', 'MSGT23 IPCA', 'PEJA22 IPCA', 'POTE12 IPCA', 'RAIZ13 IPCA', 'RAIZ23 IPCA', 'RIS414 IPCA', 'RSAN26 IPCA', 'SERI11 IPCA', 'STBP45 IPCA', 'SUMI17 IPCA', 'TRRG12 IPCA', 'UNEG11 IPCA', 'UTPS22 IPCA', 'VERO13 IPCA', 'VERO44 IPCA']\n", "ativos para recomendar:  ['VERO13 IPCA']\n", "ativos do outro usuário:  ['APFD19 IPCA', 'CBAN12 IPCA', 'CEED21 IPCA', 'CGASA2 IPCA', 'CHSF13 IPCA', 'CLIS13 IPCA', 'CLTM14 IPCA', 'COMR14 IPCA', 'COMR15 IPCA', 'CRA CDIE', 'CRA IPCA', 'CRI CDIE', 'CRI IPCA', 'CRTR12 IPCA', 'EEELA1 IPCA', 'EGIEA1 IPCA', 'EKTT11 IPCA', 'ELTE12 IPCA', 'ENAT24 IPCA', 'ENERB4 IPCA', 'ENEVA0 IPCA', 'ENGIB2 IPCA', 'ENGIB6 IPCA', 'EQSP21 IPCA', 'ERDVC4 IPCA', 'GASC18 IPCA', 'GASC23 IPCA', 'GASC25 IPCA', 'IRJS14 IPCA', 'ITPO14 IPCA', 'IVIAA0 IPCA', 'MRSAB1 IPCA', 'MSGT23 IPCA', 'NMCH11 IPCA', 'PEJA22 IPCA', 'PEJA23 IPCA', 'POTE12 IPCA', 'RAIZ13 IPCA', 'RAIZ23 IPCA', 'RIS414 IPCA', 'RMSA12 IPCA', 'RSAN26 IPCA', 'SERI11 IPCA', 'STBP45 IPCA', 'SUMI17 IPCA', 'TRRG12 IPCA', 'UNEG11 IPCA', 'UTPS22 IPCA', 'VERO13 IPCA', 'VERO44 IPCA']\n", "ativos para recomendar:  ['ENEVA0 IPCA', 'PEJA23 IPCA', 'VERO13 IPCA']\n", "ativos do outro usuário:  ['CBAN12 IPCA', 'CEED21 IPCA', 'CGASA2 IPCA', 'CHSF13 IPCA', 'CLIS13 IPCA', 'CLTM14 IPCA', 'COMR14 IPCA', 'COMR15 IPCA', 'CRA CDIE', 'CRA IPCA', 'CRI CDIE', 'CRI IPCA', 'CRTR12 IPCA', 'EEELA1 IPCA', 'EGIEA1 IPCA', 'EKTT11 IPCA', 'ELTE12 IPCA', 'ENAT24 IPCA', 'ENERB4 IPCA', 'ENEV15 IPCA', 'ENEVB0 IPCA', 'ENGIB2 IPCA', 'ENGIB6 IPCA', 'EQSP21 IPCA', 'ERDVC4 IPCA', 'GASC18 IPCA', 'GASC23 IPCA', 'GASC25 IPCA', 'IRJS14 IPCA', 'ITPO14 IPCA', 'IVIAA0 IPCA', 'MRSAB1 IPCA', 'MSGT23 IPCA', 'NMCH11 IPCA', 'PEJA22 IPCA', 'POTE12 IPCA', 'RAIZ13 IPCA', 'RAIZ23 IPCA', 'RIS414 IPCA', 'RMSA<PERSON> IPCA', 'RRRP13 IPCA', 'RSAN26 IPCA', 'SERI11 IPCA', 'STBP45 IPCA', 'SUMI17 IPCA', 'TRRG12 IPCA', 'UNEG11 IPCA', 'UTPS22 IPCA', 'VERO13 IPCA', 'VERO44 IPCA']\n", "ativos para recomendar:  ['RRRP13 IPCA', 'VERO13 IPCA']\n", "[('ENEVA0 IPCA', np.float64(21521.9429291326)), ('VERO13 IPCA', np.float64(15208.389944638642)), ('RRRP13 IPCA', np.float64(4722.2420790859205)), ('PEJA23 IPCA', np.float64(3573.113585287827))]\n", "\n", "Top 5 recomendações de ativos para o cliente 87245 (Cosseno):\n", "- ENEVA0 IPCA: Pontuação = 21521.9429\n", "- VERO13 IPCA: Pontuação = 15208.3899\n", "- RRRP13 IPCA: Pontuação = 4722.2421\n", "- PEJA23 IPCA: Pontuação = 3573.1136\n"]}], "source": ["# Exemplo: Recomendar 5 ativos para o cliente 27 usando cosseno\n", "cliente_id = 87245\n", "top_recomendacoes_cosseno = recomendar_ativos_cosseno(cliente_id, top_n=5)\n", "\n", "print(top_recomendacoes_cosseno)\n", "\n", "if top_recomendacoes_cosseno is not None:\n", "    print(f\"\\nTop 5 recomendações de ativos para o cliente {cliente_id} (Cosseno):\")\n", "    for ativo, pontuacao in top_recomendacoes_cosseno:\n", "        print(f\"- {ativo}: Pontuação = {pontuacao:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KFdai4NneTCZ"}, "outputs": [], "source": ["# Criando um mapeamento do 'Nome Ativo' para 'Tipo Ativo Renda'\n", "nome_ativo_para_tipo_renda = df.set_index('Nome Ativo')['Tipo Ativo Renda'].to_dict()\n", "\n", "def obter_tipo_renda_ativo(nome_ativo):\n", "    return nome_ativo_para_tipo_renda.get(nome_ativo)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "9PfAtFreeQOA"}, "outputs": [], "source": ["def filtrar_recomendacoes_por_perfil(user_id, recomendacoes, df_clientes):\n", "    # Obter o '<PERSON><PERSON>l Carteira' e 'Perfil Investimentos' do cliente\n", "    cliente_info = df_clientes[df_clientes['Conta'] == user_id].iloc[0]\n", "    perfil_carteira = cliente_info['Perfil Carteira']\n", "    perfil_investimentos = cliente_info['Perfil Investimentos']\n", "\n", "    recomendacoes_filtradas = []\n", "\n", "    if perfil_carteira == 'Conservador' or (perfil_carteira == 'Moderado' and perfil_investimentos == 'Arrojado'):\n", "        # Filtrar para Renda Fixa\n", "        print(\"Condição 1 (Renda Fixa) avaliada como True\")\n", "        for ativo, pontuacao in recomendacoes:\n", "            tipo_renda = obter_tipo_renda_ativo(ativo)\n", "            if tipo_renda == 'Renda Fixa':\n", "                recomendacoes_filtradas.append((ativo, pontuacao))\n", "    elif perfil_carteira == 'Sofisticado' or (perfil_carteira == 'Moderado' and perfil_investimentos == 'Conservador'):\n", "        # Filtrar para Renda Variável\n", "        print(\"Condição 2 (Renda Variável) avaliada como True\")\n", "        for ativo, pontuacao in recomendacoes:\n", "            tipo_renda = obter_tipo_renda_ativo(ativo)\n", "            print(f\"  Ativo: {ativo}, Tip<PERSON> Renda: {tipo_renda}\") # <---- ADICIONE ESTA LINHA\n", "            if tipo_renda == 'Renda Variável':\n", "                recomendacoes_filtradas.append((ativo, pontuacao))\n", "    else:\n", "        # Sem filtro se não houver essa inconformidade específica\n", "        print(\"Sem filtro\")\n", "        recomendacoes_filtradas = recomendacoes\n", "\n", "    return recomendacoes_filtradas"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QRTK5d4IiK9Q"}, "outputs": [], "source": ["def recomendar_ativos_filtrado(user_id, top_n=5, interaction_matrix=interacao_matriz, similarity_matrix=similaridade_usuario_cosseno_df, df_clientes=df):\n", "    # Obter as recomendações brutas do sistema de filtragem colaborativa\n", "    recomendacoes_brutas = recomendar_ativos_cosseno(user_id, top_n * 5, interaction_matrix, similarity_matrix) # Gerar mais recomendações brutas para ter opções após o filtro\n", "\n", "    if recomendacoes_brutas is None:\n", "        return None\n", "\n", "    # Filtrar as recomendações com base no perfil de risco\n", "    recomendacoes_filtradas = filtrar_recomendacoes_por_perfil(user_id, recomendacoes_brutas, df_clientes)\n", "\n", "    return recomendacoes_filtradas[:top_n] # Retornar os top_n após a filtragem"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3l244v59NH21", "outputId": "5f18397b-2ec4-456d-b323-73e1c6c0a143"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["usuarios similares:  Conta\n", "19554      0.971225\n", "1195551    0.924126\n", "1189864    0.909836\n", "Name: 87245, dtype: float64\n", "ativos do usuário atual:  ['AESOA1 IPCA', 'APFD19 IPCA', 'CBAN12 IPCA', 'CEED21 IPCA', 'CGASA2 IPCA', 'CHSF13 IPCA', 'CLIS13 IPCA', 'CLTM14 IPCA', 'COMR14 IPCA', 'COMR15 IPCA', 'CRA CDIE', 'CRA IPCA', 'CRI CDIE', 'CRI IPCA', 'CRTR12 IPCA', 'EEELA1 IPCA', 'EGIEA1 IPCA', 'EKTT11 IPCA', 'ELTE12 IPCA', 'ENAT24 IPCA', 'ENERB4 IPCA', 'ENEV15 IPCA', 'ENEVB0 IPCA', 'ENGIB2 IPCA', 'ENGIB6 IPCA', 'EQSP21 IPCA', 'ERDVC4 IPCA', 'GASC18 IPCA', 'GASC23 IPCA', 'GASC25 IPCA', 'HVSP11 IPCA', 'IRJS14 IPCA', 'ITPO14 IPCA', 'IVIAA0 IPCA', 'MRSAB1 IPCA', 'MSGT23 IPCA', 'NMCH11 IPCA', 'PEJA22 IPCA', 'POTE12 IPCA', 'RAIZ13 IPCA', 'RAIZ23 IPCA', 'RIS414 IPCA', 'RMSA12 IPCA', 'RSAN26 IPCA', 'SERI11 IPCA', 'STBP45 IPCA', 'SUMI17 IPCA', 'TRRG12 IPCA', 'UNEG11 IPCA', 'UTPS22 IPCA', 'VERO44 IPCA']\n", "ativos do outro usuário:  ['APFD19 IPCA', 'CBAN12 IPCA', 'CEED21 IPCA', 'CGASA2 IPCA', 'CHSF13 IPCA', 'CLIS13 IPCA', 'CLTM14 IPCA', 'COMR14 IPCA', 'COMR15 IPCA', 'CRA CDIE', 'CRA IPCA', 'CRI CDIE', 'CRI IPCA', 'CRTR12 IPCA', 'EEELA1 IPCA', 'EGIEA1 IPCA', 'EKTT11 IPCA', 'ELTE12 IPCA', 'ENAT24 IPCA', 'ENERB4 IPCA', 'ENEV15 IPCA', 'ENEVB0 IPCA', 'ENGIB2 IPCA', 'ENGIB6 IPCA', 'EQSP21 IPCA', 'ERDVC4 IPCA', 'GASC18 IPCA', 'GASC23 IPCA', 'GASC25 IPCA', 'HVSP11 IPCA', 'IRJS14 IPCA', 'ITPO14 IPCA', 'IVIAA0 IPCA', 'MRSAB1 IPCA', 'MSGT23 IPCA', 'PEJA22 IPCA', 'POTE12 IPCA', 'RAIZ13 IPCA', 'RAIZ23 IPCA', 'RIS414 IPCA', 'RSAN26 IPCA', 'SERI11 IPCA', 'STBP45 IPCA', 'SUMI17 IPCA', 'TRRG12 IPCA', 'UNEG11 IPCA', 'UTPS22 IPCA', 'VERO13 IPCA', 'VERO44 IPCA']\n", "ativos para recomendar:  ['VERO13 IPCA']\n", "ativos do outro usuário:  ['APFD19 IPCA', 'CBAN12 IPCA', 'CEED21 IPCA', 'CGASA2 IPCA', 'CHSF13 IPCA', 'CLIS13 IPCA', 'CLTM14 IPCA', 'COMR14 IPCA', 'COMR15 IPCA', 'CRA CDIE', 'CRA IPCA', 'CRI CDIE', 'CRI IPCA', 'CRTR12 IPCA', 'EEELA1 IPCA', 'EGIEA1 IPCA', 'EKTT11 IPCA', 'ELTE12 IPCA', 'ENAT24 IPCA', 'ENERB4 IPCA', 'ENEVA0 IPCA', 'ENGIB2 IPCA', 'ENGIB6 IPCA', 'EQSP21 IPCA', 'ERDVC4 IPCA', 'GASC18 IPCA', 'GASC23 IPCA', 'GASC25 IPCA', 'IRJS14 IPCA', 'ITPO14 IPCA', 'IVIAA0 IPCA', 'MRSAB1 IPCA', 'MSGT23 IPCA', 'NMCH11 IPCA', 'PEJA22 IPCA', 'PEJA23 IPCA', 'POTE12 IPCA', 'RAIZ13 IPCA', 'RAIZ23 IPCA', 'RIS414 IPCA', 'RMSA12 IPCA', 'RSAN26 IPCA', 'SERI11 IPCA', 'STBP45 IPCA', 'SUMI17 IPCA', 'TRRG12 IPCA', 'UNEG11 IPCA', 'UTPS22 IPCA', 'VERO13 IPCA', 'VERO44 IPCA']\n", "ativos para recomendar:  ['ENEVA0 IPCA', 'PEJA23 IPCA', 'VERO13 IPCA']\n", "ativos do outro usuário:  ['CBAN12 IPCA', 'CEED21 IPCA', 'CGASA2 IPCA', 'CHSF13 IPCA', 'CLIS13 IPCA', 'CLTM14 IPCA', 'COMR14 IPCA', 'COMR15 IPCA', 'CRA CDIE', 'CRA IPCA', 'CRI CDIE', 'CRI IPCA', 'CRTR12 IPCA', 'EEELA1 IPCA', 'EGIEA1 IPCA', 'EKTT11 IPCA', 'ELTE12 IPCA', 'ENAT24 IPCA', 'ENERB4 IPCA', 'ENEV15 IPCA', 'ENEVB0 IPCA', 'ENGIB2 IPCA', 'ENGIB6 IPCA', 'EQSP21 IPCA', 'ERDVC4 IPCA', 'GASC18 IPCA', 'GASC23 IPCA', 'GASC25 IPCA', 'IRJS14 IPCA', 'ITPO14 IPCA', 'IVIAA0 IPCA', 'MRSAB1 IPCA', 'MSGT23 IPCA', 'NMCH11 IPCA', 'PEJA22 IPCA', 'POTE12 IPCA', 'RAIZ13 IPCA', 'RAIZ23 IPCA', 'RIS414 IPCA', 'RMSA<PERSON> IPCA', 'RRRP13 IPCA', 'RSAN26 IPCA', 'SERI11 IPCA', 'STBP45 IPCA', 'SUMI17 IPCA', 'TRRG12 IPCA', 'UNEG11 IPCA', 'UTPS22 IPCA', 'VERO13 IPCA', 'VERO44 IPCA']\n", "ativos para recomendar:  ['RRRP13 IPCA', 'VERO13 IPCA']\n", "Condição 2 (Renda Variável) avaliada como True\n", "  Ativo: ENEVA0 IPCA, Tipo Renda: Renda Fixa\n", "  Ativo: VERO13 IPCA, Tipo Renda: Renda Fixa\n", "  Ativo: RRRP13 IPCA, Tipo Renda: Renda Fixa\n", "  Ativo: PEJA23 IPCA, Tipo Renda: Renda Fixa\n", "Nenhum dos ativos recomendados auxilia na adequação do perfil de risco do cliente\n"]}], "source": ["# Exemplo: Obter as recomendações filtradas para o cliente 27\n", "cliente_id = 87245\n", "top_recomendacoes_filtradas = recomendar_ativos_filtrado(cliente_id, top_n=5)\n", "\n", "if len(top_recomendacoes_filtradas) == 0:\n", "  print(\"Nenhum dos ativos recomendados auxilia na adequação do perfil de risco do cliente\")\n", "else:\n", "  print(f\"\\nTop 5 recomendações de ativos filtradas para o cliente {cliente_id}:\")\n", "  for ativo, pontuacao in top_recomendacoes_filtradas:\n", "      tipo_renda = obter_tipo_renda_ativo(ativo)\n", "      print(f\"- {ativo} ({tipo_renda}): Pontuação = {pontuacao:.4f}\")"]}, {"cell_type": "markdown", "metadata": {"id": "s7k2H26sLbLu"}, "source": ["### Tentativa de mudança de abordagem"]}, {"cell_type": "markdown", "metadata": {"id": "NdcAFg-4_re6"}, "source": ["Acabamos de perceber que nenhuma das recomendações estão auxiliando a adequar o perfil de risco do cliente.\n", "\n", "Podemos apelar à filtragem baseada em conteúdo ou à abordagem híbrida.\n", "\n", "Ou podemos tentar encontrar somente vizinhos (usuários similares) dentro do dataset original, considerando somente pessoas com os perfis de risco de objetivo (Perfil Investimentos)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lRYzTaHM_qRv"}, "outputs": [], "source": ["# # <PERSON><PERSON> enquanto, iremos trabalhar somente com a conta 27, pois vamos analisar somente um caso para validar uma hipótese\n", "# # <PERSON><PERSON>o tenhamos resultados positivos, tentaremos estender a análise para outros usuários\n", "\n", "# # Recuperamos o dataset com as classificações de carteiras\n", "# cf_df = pd.read_csv('/content/carteiras_cliente.csv')\n", "# cf_df = cf_df.drop(columns=['Unnamed: 0'])\n", "# cf_df"]}, {"cell_type": "markdown", "metadata": {"id": "GuZMEp5DBqSa"}, "source": ["Sabemos que a conta 27 deveria ter um perfil de risco 'Moderado'.\n", "<PERSON><PERSON><PERSON>, o risco atrelado à carteira 27 é 'Conservador'.\n", "\n", "<PERSON><PERSON><PERSON>, vamos procurar os clientes mais parecidos com a conta 27, contudo, somente dentre aqueles clientes que tem uma carteira ('Perfil Investimentos') com risco 'Moderado'.\n", "\n", "<PERSON><PERSON><PERSON> isso, iremos filtrar somente os ativos que ajudarem na adequação de risco do cliente."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lXMDeqH9C9xX"}, "outputs": [], "source": ["# cf_df_filtrado = cf_df[cf_df['Perfil Investimentos'] == 'Moderado']\n", "# cf_df_filtrado"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "d9rfXeS0DwRH"}, "outputs": [], "source": ["# interacao_matriz_conta_27 = pd.DataFrame(df.pivot_table(\n", "#     index='Conta',\n", "#     columns='Nome Ativo',\n", "#     values='Financeiro',\n", "#     aggfunc='mean').fillna(0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "vxxvbIFYD71Z"}, "outputs": [], "source": ["# # Calculando a similaridade de cosseno entre os usuários\n", "# similaridade_usuario_cosseno_conta_27 = cosine_similarity(interacao_matriz_conta_27)\n", "\n", "# # Criamos um DataFrame para visualizar melhor as similaridades\n", "# similaridade_usuario_cosseno_conta_27_df = pd.DataFrame(similaridade_usuario_cosseno_conta_27, index=interacao_matriz_conta_27.index, columns=interacao_matriz_conta_27.index)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Jr4uNp2sDN1V"}, "outputs": [], "source": ["# # Vamos procurar os ativos\n", "# ativos_conta_27 = recomendar_ativos_cosseno(user_id=27, top_n=300, interaction_matrix=interacao_matriz_conta_27, similarity_matrix=similaridade_usuario_cosseno_conta_27_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cfTg68XKEZdJ"}, "outputs": [], "source": ["# ativos_conta_27"]}, {"cell_type": "markdown", "metadata": {"id": "mtKge1Z5K3fU"}, "source": ["Ao fim da análise, a nova tentativa de recomendação não gerou ganhos produtivos. <PERSON><PERSON> assim, a abordagem foi abandonada, os códigos foram comentados e a análise anterior se mantém vigente.\n", "\n", "Por fim, vale-se analisar outras abordagens para a construção do sistema de recomendação, seja na extensão do modelo aqui construído com técnicas de redução de dimensionalidade ou troca de métricas de similaridades, seja na construção de outros modelos, baseados em machine learning, filtragem por conteúdo ou híbrida."]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}