using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Pati.ClassificationService.Domain
{
    /// <summary>
    /// Entidade que representa uma carteira de investimentos
    /// </summary>
    public class Portfolio
    {
        public int AccountId { get; private set; }
        public string PortfolioId { get; private set; } = string.Empty;
        public string PortfolioProfile { get; private set; } = string.Empty;
        public List<Asset> Assets { get; private set; } = new();
        public DateTime CreatedAt { get; private set; }
        public DateTime? UpdatedAt { get; private set; }

        // Construtor padrão para Entity Framework
        protected Portfolio() { }

        public Portfolio(int accountId, string portfolioId, string portfolioProfile, List<Asset> assets)
        {
            if (accountId <= 0)
                throw new ArgumentException("Account ID must be greater than zero.", nameof(accountId));

            if (string.IsNullOrWhiteSpace(portfolioId))
                throw new ArgumentException("Portfolio ID is required.", nameof(portfolioId));

            if (string.IsNullOrWhiteSpace(portfolioProfile))
                throw new ArgumentException("Portfolio profile is required.", nameof(portfolioProfile));

            if (assets == null || !assets.Any())
                throw new ArgumentException("Portfolio must contain at least one asset.", nameof(assets));

            var validProfiles = new[] { "Conservador", "Moderado", "Arrojado", "Sofisticado" };
            if (!validProfiles.Contains(portfolioProfile))
                throw new ArgumentException("Invalid portfolio profile.", nameof(portfolioProfile));

            AccountId = accountId;
            PortfolioId = portfolioId;
            PortfolioProfile = portfolioProfile;
            Assets = assets ?? new List<Asset>();
            CreatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Função risco_permitido do notebook SVD - verifica se um risco é permitido para um perfil
        /// </summary>
        public static bool IsRiskAllowedForProfile(string profile, string risk)
        {
            var riskRules = new Dictionary<string, string[]>
            {
                { "Conservador", new[] { "Conservador" } },
                { "Moderado", new[] { "Conservador", "Moderado" } },
                { "Arrojado", new[] { "Conservador", "Moderado", "Arrojado" } },
                { "Sofisticado", new[] { "Conservador", "Moderado", "Sofisticado" } }
            };

            return riskRules.ContainsKey(profile) && riskRules[profile].Contains(risk);
        }

        /// <summary>
        /// Proporções ideais do notebook SVD
        /// </summary>
        public static Dictionary<string, (decimal FixedIncome, decimal VariableIncome)> GetIdealProportions()
        {
            return new Dictionary<string, (decimal, decimal)>
            {
                { "Conservador", (0.9m, 0.1m) },
                { "Moderado", (0.6m, 0.4m) },
                { "Arrojado", (0.3m, 0.7m) },
                { "Sofisticado", (0.3m, 0.7m) }
            };
        }

        /// <summary>
        /// Calcula as proporções atuais de Renda Fixa e Renda Variável
        /// </summary>
        public (decimal FixedIncome, decimal VariableIncome) CalculateCurrentProportions()
        {
            var totalValue = Assets.Sum(a => Math.Abs(a.Value));

            if (totalValue == 0)
                return (0, 0);

            var fixedIncomeValue = Assets
                .Where(a => a.IncomeType == "Renda Fixa")
                .Sum(a => Math.Abs(a.Value));

            var variableIncomeValue = Assets
                .Where(a => a.IncomeType == "Renda Variável")
                .Sum(a => Math.Abs(a.Value));

            return (fixedIncomeValue / totalValue, variableIncomeValue / totalValue);
        }

        /// <summary>
        /// Alias para CalculateCurrentProportions para compatibilidade com testes
        /// </summary>
        public (decimal FixedIncome, decimal VariableIncome) CalculateIncomeProportions()
        {
            return CalculateCurrentProportions();
        }

        /// <summary>
        /// Calcula o valor total da carteira
        /// </summary>
        public decimal GetTotalValue()
        {
            return Assets.Sum(a => Math.Abs(a.Value));
        }

        /// <summary>
        /// Identifica inconformidades na carteira baseado nas regras CVM 175
        /// </summary>
        public List<Inconsistency> IdentifyInconsistencies()
        {
            var inconsistencies = new List<Inconsistency>();

            // 1. Verificar ativos com perfil de investimento incompatível com perfil da carteira
            foreach (var asset in Assets)
            {
                if (!IsRiskAllowedForProfile(PortfolioProfile, asset.InvestmentProfile))
                {
                    var description = $"Ativo de {asset.IncomeType} com perfil {asset.InvestmentProfile} incompatível com perfil {PortfolioProfile}";
                    var severity = CalculateSeverity(PortfolioProfile, asset.InvestmentProfile);

                    inconsistencies.Add(new Inconsistency(
                        asset.AssetId,
                        asset.Name,
                        description,
                        severity
                    ));
                }
            }

            // 2. Verificar proporções de Renda Fixa/Variável fora das ideais
            var currentProportions = CalculateCurrentProportions();
            var idealProportions = GetIdealProportions();

            if (idealProportions.ContainsKey(PortfolioProfile))
            {
                var ideal = idealProportions[PortfolioProfile];
                var tolerance = 0.15m; // 15% de tolerância

                var fixedIncomeDiff = Math.Abs(currentProportions.FixedIncome - ideal.FixedIncome);
                var variableIncomeDiff = Math.Abs(currentProportions.VariableIncome - ideal.VariableIncome);

                if (fixedIncomeDiff > tolerance)
                {
                    var description = $"Proporção de Renda Fixa ({currentProportions.FixedIncome:P1}) fora do ideal para perfil {PortfolioProfile} ({ideal.FixedIncome:P1})";
                    inconsistencies.Add(new Inconsistency(
                        "PORTFOLIO_ALLOCATION",
                        "Alocação da Carteira",
                        description,
                        fixedIncomeDiff > 0.3m ? "high" : "medium"
                    ));
                }

                if (variableIncomeDiff > tolerance)
                {
                    var description = $"Proporção de Renda Variável ({currentProportions.VariableIncome:P1}) fora do ideal para perfil {PortfolioProfile} ({ideal.VariableIncome:P1})";
                    inconsistencies.Add(new Inconsistency(
                        "PORTFOLIO_ALLOCATION",
                        "Alocação da Carteira",
                        description,
                        variableIncomeDiff > 0.3m ? "high" : "medium"
                    ));
                }
            }

            return inconsistencies;
        }

        /// <summary>
        /// Calcula a severidade da inconformidade baseado na diferença entre perfis
        /// </summary>
        private string CalculateSeverity(string portfolioProfile, string assetProfile)
        {
            var profileRanking = new Dictionary<string, int>
            {
                { "Conservador", 1 },
                { "Moderado", 2 },
                { "Arrojado", 3 },
                { "Sofisticado", 4 }
            };

            if (!profileRanking.ContainsKey(portfolioProfile) || !profileRanking.ContainsKey(assetProfile))
                return "medium";

            var portfolioRank = profileRanking[portfolioProfile];
            var assetRank = profileRanking[assetProfile];
            var difference = Math.Abs(portfolioRank - assetRank);

            return difference switch
            {
                0 => "low",
                1 => "medium",
                >= 2 => "high",
                _ => "medium"
            };
        }

        /// <summary>
        /// Atualiza a carteira
        /// </summary>
        public void Update(string? portfolioProfile = null, List<Asset>? assets = null)
        {
            if (!string.IsNullOrWhiteSpace(portfolioProfile))
            {
                var validProfiles = new[] { "Conservador", "Moderado", "Arrojado", "Sofisticado" };
                if (!validProfiles.Contains(portfolioProfile))
                    throw new ArgumentException("Invalid portfolio profile.", nameof(portfolioProfile));

                PortfolioProfile = portfolioProfile;
            }

            if (assets != null && assets.Any())
            {
                Assets = assets;
            }

            UpdatedAt = DateTime.UtcNow;
        }

        public override string ToString()
        {
            return $"Portfolio {PortfolioId} - Account {AccountId} - Profile: {PortfolioProfile} - Assets: {Assets.Count}";
        }
    }
}
