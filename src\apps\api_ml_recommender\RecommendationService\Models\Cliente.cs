namespace RecommendationService.Models
{
    public class Cliente
    {
        public Cliente(string id, string nome, PerfilRisco perfilRisco, List<Investimento>? carteira = null)
        {
            Id = id;
            Nome = nome;
            PerfilRisco = perfilRisco;
            Carteira = carteira ?? new List<Investimento>();
            UltimaAtualizacao = DateTime.Now;
        }

        public Cliente()
        {
            Id = string.Empty;
            Nome = string.Empty;
            PerfilRisco = PerfilRisco.Conservador; // Valor padrão
            Carteira = new List<Investimento>();
            UltimaAtualizacao = DateTime.Now;
        }

        public string Id { get; set; }
        public string Nome { get; set; }
        public PerfilRisco PerfilRisco { get; set; }
        public List<Investimento> Carteira { get; set; }
        public DateTime UltimaAtualizacao { get; set; }
    }

    public enum PerfilRisco
    {
        Conservador = 1,    // Baixo risco
        Moderado = 3,       // Risco médio (alinhado com InvestimentoDto)
        Arrojado = 4,       // Alto risco
        Sofisticado = 5     // Muito alto risco
    }
}