using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Pati.ClassificationService.Infrastructure.Services;
using Pati.ClassificationService.Infrastructure.Models;
using Pati.ClassificationService.Domain;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Net.Http;
using System;

namespace Pati.ClassificationService.Tests
{
    /// <summary>
    /// Testes unitários específicos para o SVDModelService
    /// Valida integração direta com modelo .pkl e normalização MinMaxScaler
    /// </summary>
    public class SVDModelServiceTests
    {
        private readonly Mock<ILogger<SVDModelService>> _mockLogger;
        private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
        private readonly SVDModelOptions _options;
        private readonly SVDModelService _svdModelService;

        public SVDModelServiceTests()
        {
            _mockLogger = new Mock<ILogger<SVDModelService>>();
            _mockHttpClientFactory = new Mock<IHttpClientFactory>();
            
            _options = new SVDModelOptions
            {
                ModelPath = "src/models/modelo_svd_treinado.pkl",
                MinSuitabilityThreshold = 0.4,
                DefaultRecommendationCount = 5,
                EnableModelCaching = true
            };

            var mockOptions = new Mock<IOptions<SVDModelOptions>>();
            mockOptions.Setup(o => o.Value).Returns(_options);

            _svdModelService = new SVDModelService(
                _mockLogger.Object,
                mockOptions.Object,
                _mockHttpClientFactory.Object
            );
        }

        [Fact]
        public async Task LoadModelAsync_WithValidPath_ReturnsTrue()
        {
            // Act
            var result = await _svdModelService.LoadModelAsync();

            // Assert
            Assert.True(result);
            Assert.True(_svdModelService.IsModelLoaded);
        }

        [Fact]
        public async Task PredictRiskProfileAsync_WithValidData_ReturnsCorrectPrediction()
        {
            // Arrange
            await _svdModelService.LoadModelAsync();
            
            var portfolioData = new PortfolioData
            {
                ClientId = 27,
                PortfolioProfile = "Moderado",
                TotalValue = 50000f,
                FixedIncomeRatio = 0.8f,
                VariableIncomeRatio = 0.2f,
                RiskScore = 1.5f
            };

            // Act
            var result = await _svdModelService.PredictRiskProfileAsync(portfolioData);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Conservador", result.RiskProfile); // Baseado na lógica do notebook
            Assert.True(result.Confidence > 0.7f);
        }

        [Fact]
        public async Task PredictAssetSuitabilityAsync_WithKnownClient_ReturnsCorrectScore()
        {
            // Arrange
            await _svdModelService.LoadModelAsync();

            // Act
            var result = await _svdModelService.PredictAssetSuitabilityAsync(27, "TESOURO_SELIC_001");

            // Assert
            Assert.True(result > 0.8); // Cliente conservador deve ter alta adequação para Tesouro Selic
        }

        [Fact]
        public async Task PredictAssetSuitabilityAsync_IncompatibleAsset_ReturnsLowScore()
        {
            // Arrange
            await _svdModelService.LoadModelAsync();

            // Act
            var result = await _svdModelService.PredictAssetSuitabilityAsync(27, "PETR4_001");

            // Assert
            Assert.True(result < 0.3); // Cliente conservador deve ter baixa adequação para ações arriscadas
        }

        [Fact]
        public async Task AnalyzePortfolioAsync_WithIncompatibleAssets_ReturnsInconsistencies()
        {
            // Arrange
            await _svdModelService.LoadModelAsync();
            
            var portfolio = new Portfolio(27, "portfolio_27", "Conservador", new List<Asset>
            {
                new Asset("PETR4_001", "Petrobras PN", "stock", "Renda Variável", "Arrojado", 100, 25000),
                new Asset("DERIVATIVO_001", "Derivativo", "derivative", "Renda Variável", "Sofisticado", 50, 15000)
            });

            // Act
            var result = await _svdModelService.AnalyzePortfolioAsync(portfolio);

            // Assert
            Assert.NotEmpty(result);
            Assert.Contains(result, i => i.AssetId == "PETR4_001");
            Assert.Contains(result, i => i.Severity == "high" || i.Severity == "medium");
        }

        [Theory]
        [InlineData(27, "Conservador")]
        [InlineData(44, "Sofisticado")]
        [InlineData(55, "Moderado")]
        [InlineData(89, "Arrojado")]
        [InlineData(103, "Conservador")]
        public async Task PredictRiskProfileAsync_SimulatedClients_ReturnsExpectedProfiles(int clientId, string expectedProfile)
        {
            // Arrange
            await _svdModelService.LoadModelAsync();
            
            var portfolioData = new PortfolioData
            {
                ClientId = clientId,
                PortfolioProfile = "Moderado", // Input genérico
                TotalValue = 50000f,
                FixedIncomeRatio = expectedProfile == "Conservador" ? 0.9f : 0.3f,
                VariableIncomeRatio = expectedProfile == "Conservador" ? 0.1f : 0.7f,
                RiskScore = expectedProfile switch
                {
                    "Conservador" => 1.0f,
                    "Moderado" => 2.0f,
                    "Arrojado" => 3.0f,
                    "Sofisticado" => 4.0f,
                    _ => 2.0f
                }
            };

            // Act
            var result = await _svdModelService.PredictRiskProfileAsync(portfolioData);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedProfile, result.RiskProfile);
        }

        [Fact]
        public async Task ConvertToPortfolioData_WithMixedAssets_CalculatesCorrectRatios()
        {
            // Arrange
            await _svdModelService.LoadModelAsync();
            
            var portfolio = new Portfolio(27, "portfolio_27", "Moderado", new List<Asset>
            {
                new Asset("TESOURO001", "Tesouro Selic", "bond", "Renda Fixa", "Conservador", 100, 30000),
                new Asset("PETR4001", "Petrobras PN", "stock", "Renda Variável", "Arrojado", 50, 20000)
            });

            // Act
            var result = _svdModelService.ConvertToPortfolioData(portfolio);

            // Assert
            Assert.Equal(27, result.ClientId);
            Assert.Equal(50000f, result.TotalValue);
            Assert.Equal(0.6f, result.FixedIncomeRatio, 1); // 30000/50000
            Assert.Equal(0.4f, result.VariableIncomeRatio, 1); // 20000/50000
            Assert.Equal(2, result.AssetCount);
        }

        [Fact]
        public async Task GetModelStatsAsync_AfterLoading_ReturnsCorrectStats()
        {
            // Arrange
            await _svdModelService.LoadModelAsync();

            // Act
            var result = await _svdModelService.GetModelStatsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.True(result.NumberOfClients > 0);
            Assert.True(result.NumberOfAssets > 0);
            Assert.Equal(50, result.NumberOfFactors); // Baseado no notebook
            Assert.True(result.TrainingAccuracy > 0.9); // RMSE 0.0264 = Accuracy 0.9736
            Assert.Contains("SVD", result.ModelVersion);
        }

        [Fact]
        public async Task RecommendAssetsForClientAsync_WithValidClient_ReturnsRecommendations()
        {
            // Arrange
            await _svdModelService.LoadModelAsync();

            // Act
            var result = await _svdModelService.RecommendAssetsForClientAsync(27, "Conservador", 3);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Count <= 3);
            // Para cliente conservador, deve recomendar ativos conservadores
        }

        [Fact]
        public async Task CalculateClientSimilarityAsync_WithKnownClients_ReturnsValidSimilarity()
        {
            // Arrange
            await _svdModelService.LoadModelAsync();

            // Act
            var result = await _svdModelService.CalculateClientSimilarityAsync(27, 103);

            // Assert
            Assert.True(result >= 0.0 && result <= 1.0);
            // Clientes 27 e 103 são ambos conservadores, devem ter alta similaridade
            Assert.True(result > 0.5);
        }

        [Fact]
        public async Task IdentifyProblematicAssetsAsync_WithMixedPortfolio_IdentifiesIssues()
        {
            // Arrange
            await _svdModelService.LoadModelAsync();
            
            var portfolio = new Portfolio(27, "portfolio_27", "Conservador", new List<Asset>
            {
                new Asset("TESOURO_SELIC_001", "Tesouro Selic", "bond", "Renda Fixa", "Conservador", 100, 30000),
                new Asset("DERIVATIVO_001", "Derivativo", "derivative", "Renda Variável", "Sofisticado", 10, 20000)
            });

            // Act
            var result = await _svdModelService.IdentifyProblematicAssetsAsync(portfolio);

            // Assert
            Assert.NotEmpty(result);
            Assert.Contains(result, a => a.AssetId == "DERIVATIVO_001"); // Deve identificar derivativo como problemático
        }
    }
}
