using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Quartz;

namespace Pati.ClassificationService.Infrastructure.Jobs
{
    /// <summary>
    /// Serviço para gerenciar jobs agendados do Quartz
    /// </summary>
    public class QuartzJobService : IHostedService
    {
        private readonly ILogger<QuartzJobService> _logger;
        private readonly ISchedulerFactory _schedulerFactory;
        private readonly ClassificationJobOptions _options;
        private IScheduler? _scheduler;

        public QuartzJobService(
            ILogger<QuartzJobService> logger,
            ISchedulerFactory schedulerFactory,
            IOptions<ClassificationJobOptions> options)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _schedulerFactory = schedulerFactory ?? throw new ArgumentNullException(nameof(schedulerFactory));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Starting Quartz Job Service");

                if (!_options.Enabled)
                {
                    _logger.LogInformation("Classification job is disabled in configuration");
                    return;
                }

                // Obter scheduler
                _scheduler = await _schedulerFactory.GetScheduler(cancellationToken);

                // Configurar job de classificação
                await ConfigureClassificationJobAsync();

                // Iniciar scheduler
                await _scheduler.Start(cancellationToken);

                _logger.LogInformation("Quartz Job Service started successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start Quartz Job Service");
                throw;
            }
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Stopping Quartz Job Service");

                if (_scheduler != null && _scheduler.IsStarted)
                {
                    await _scheduler.Shutdown(cancellationToken);
                }

                _logger.LogInformation("Quartz Job Service stopped successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping Quartz Job Service");
            }
        }

        private async Task ConfigureClassificationJobAsync()
        {
            // Definir o job
            var jobDetail = JobBuilder.Create<ClassificationJob>()
                .WithIdentity("classification-job", "classification-group")
                .WithDescription("Job para classificação automática de todas as carteiras")
                .Build();

            // Configurar trigger baseado nas opções
            ITrigger trigger;

            if (!string.IsNullOrEmpty(_options.DailyExecutionTime))
            {
                // Execução diária em horário específico
                if (TimeSpan.TryParse(_options.DailyExecutionTime, out var executionTime))
                {
                    trigger = TriggerBuilder.Create()
                        .WithIdentity("classification-daily-trigger", "classification-group")
                        .WithDescription($"Trigger diário às {_options.DailyExecutionTime}")
                        .WithSchedule(CronScheduleBuilder.DailyAtHourAndMinute(executionTime.Hours, executionTime.Minutes))
                        .Build();

                    _logger.LogInformation("Classification job scheduled to run daily at {Time}", _options.DailyExecutionTime);
                }
                else
                {
                    throw new ArgumentException($"Invalid DailyExecutionTime format: {_options.DailyExecutionTime}. Use HH:mm format.");
                }
            }
            else
            {
                // Execução por intervalo
                var triggerBuilder = TriggerBuilder.Create()
                    .WithIdentity("classification-interval-trigger", "classification-group")
                    .WithDescription($"Trigger de intervalo a cada {_options.IntervalMinutes} minutos")
                    .WithSimpleSchedule(x => x
                        .WithIntervalInMinutes(_options.IntervalMinutes)
                        .RepeatForever());

                if (_options.RunOnStartup)
                {
                    triggerBuilder.StartNow();
                    _logger.LogInformation("Classification job will start immediately and repeat every {Minutes} minutes", _options.IntervalMinutes);
                }
                else
                {
                    triggerBuilder.StartAt(DateTimeOffset.UtcNow.AddMinutes(_options.IntervalMinutes));
                    _logger.LogInformation("Classification job will start in {Minutes} minutes and repeat every {Minutes} minutes", 
                        _options.IntervalMinutes, _options.IntervalMinutes);
                }

                trigger = triggerBuilder.Build();
            }

            // Agendar o job
            await _scheduler!.ScheduleJob(jobDetail, trigger);

            _logger.LogInformation("Classification job scheduled successfully");
        }
    }
}
