<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="./assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>

# Nome do Projeto: Pati (Plataforma de Adequação de Tipo de Investidor)

## Nome do Grupo: MMs (Meninas Malvadas)

# Manual de Instalação

## Sumário
- [Pré-requisitos](#pré-requisitos)
- [Variáveis de Ambiente](#variáveis-de-ambiente)
- [Instalação](#instalação)
- [Configuração](#configuração)
- [Troubleshooting](#troubleshooting)

## Pré-requisitos

- Docker 20.10+
- Docker Compose 2.0+
- Git
- PostgreSQL 16 (opcional, se não usar Docker)
- .NET 8.0 SDK (para desenvolvimento)
- Node.js 18+ (para frontend)

## Instalação

### Opção 1: Docker Compose (Recomendado)

1. Clone o repositório:
```bash
git clone https://github.com/Inteli-College/2025-1B-T13-ES06-G02.git
cd 2025-1B-T13-ES06-G02
```

2. Configure as variáveis de ambiente:
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

3. Execute o ambiente completo:
```bash
cd src
docker-compose up -d
```

4. Verifique se os serviços estão rodando:
```bash
docker-compose ps
```

### Opção 2: Instalação Local

1. Configure o banco PostgreSQL:
```bash
# Criar banco de dados
createdb pati_db

# Executar scripts de criação
psql -d pati_db -f src/database/01_create_database.sql
psql -d pati_db -f src/database/02_initial_data.sql
```

2. Configure e execute a API:
```bash
cd src/apps/api_data_presentation
dotnet restore
dotnet run
```

3. Configure e execute o frontend:
```bash
cd src/frontend/pati
npm install
npm start
```

## Configuração

### Configuração do Banco de Dados

1. Verifique a conexão:
```bash
psql -h localhost -U pati_user -d pati_db -c "SELECT COUNT(*) FROM client;"
```

2. Execute as migrações (se necessário):
```bash
cd src/database
psql -d pati_db -f migrations/01_to_02.sql
```

### Configuração da API

1. Teste a API:
```bash
curl http://localhost:3000/health
```

2. Verifique os logs:
```bash
docker-compose logs api
```

## Troubleshooting

### Problemas Comuns

#### Erro: Conexão recusada ao banco
- **Sintoma**: `ECONNREFUSED` ou `Connection refused`
- **Solução**:
  1. Verifique o status do Docker Compose: `docker-compose ps`
  2. Confirme as variáveis de ambiente no arquivo `.env`
  3. Reinicie os containers: `docker-compose restart`
  4. Verifique os logs: `docker-compose logs postgres`

#### Erro: Porta já em uso
- **Sintoma**: `Port 5432 is already in use`
- **Solução**:
  1. Pare o PostgreSQL local: `sudo systemctl stop postgresql`
  2. Ou altere a porta no `.env`: `POSTGRES_PORT=5433`
  3. Reinicie o Docker Compose: `docker-compose up -d`

#### Erro: Tabelas não encontradas
- **Sintoma**: `relation "client" does not exist`
- **Solução**:
  1. Execute os scripts de criação:
     ```bash
     docker-compose exec postgres psql -U pati_user -d pati_db -f /docker-entrypoint-initdb.d/01_create_database.sql
     ```
  2. Verifique se as tabelas foram criadas:
     ```bash
     docker-compose exec postgres psql -U pati_user -d pati_db -c "\dt"
     ```

#### Erro: Falha na autenticação
- **Sintoma**: `authentication failed for user`
- **Solução**:
  1. Verifique as credenciais no `.env`
  2. Recrie o container do banco:
     ```bash
     docker-compose down
     docker volume rm src_postgres_data
     docker-compose up -d
     ```

#### Erro: API não responde
- **Sintoma**: `Connection timeout` ou `502 Bad Gateway`
- **Solução**:
  1. Verifique se a API está rodando: `docker-compose ps`
  2. Verifique os logs da API: `docker-compose logs api`
  3. Teste a conectividade: `curl http://localhost:3000/health`
  4. Reinicie o serviço: `docker-compose restart api`

### Comandos Úteis

```bash
# Verificar status dos containers
docker-compose ps

# Ver logs de um serviço específico
docker-compose logs -f api

# Reiniciar um serviço
docker-compose restart postgres

# Acessar o banco de dados
docker-compose exec postgres psql -U pati_user -d pati_db

# Limpar volumes e reiniciar
docker-compose down -v
docker-compose up -d

# Verificar conectividade de rede
docker-compose exec api ping postgres
```

### Logs e Monitoramento

- **Logs da API**: `docker-compose logs api`
- **Logs do Banco**: `docker-compose logs postgres`
- **Logs do Frontend**: `docker-compose logs frontend`
- **Métricas de Sistema**: `docker stats`

Para mais informações, consulte a [documentação completa do projeto](./project.md).
