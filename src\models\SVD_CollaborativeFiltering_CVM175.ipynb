{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# **Filtragem Colaborativa baseada em SVD (Singular Value Decomposition)**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Objetivo** \n", "Neste notebook, desenvolvemos um sistema de recomendação de ativos financeiros para clientes cujas carteiras apresentam inconformidades em relação ao perfil de risco esperado, conforme as exigências da regulação CVM 175. O objetivo é sugerir ativos que estejam em conformidade com o perfil do investidor, promovendo ajustes mínimos e mantendo a satisfação do cliente."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### **Escolha de Abordagem**\n", "\n", "Após a análise de diferentes abordagens de recomendação, optamos por utilizar o modelo de **Filtragem Colaborativa baseada em SVD (Singular Value Decomposition)**. A recomendação é feita com base no comportamento de investimento de clientes semelhantes, usando como métrica o valor investido em cada ativo. Essa escolha foi motivada por diversos fatores:\n", "\n", "- **Robustez e Personalização:** O SVD permite decompor a matriz de interações cliente-ativo em fatores latentes, capturando padrões de comportamento mesmo em cenários de dados esparsos. Isso possibilita recomendações mais personalizadas e relevantes, indo além da simples similaridade entre clientes.\n", "- **Escalabilidade:** O método é amplamente utilizado em sistemas de recomendação de larga escala, sendo eficiente para grandes volumes de dados.\n", "- **Facilidade de Integração com Regras Regulatórias:** O SVD pode ser facilmente combinado com filtros pós-modelo, garantindo que todas as recomendações estejam em conformidade com o perfil de risco do cliente, conforme exigido pela CVM 175.\n", "- **Aprovação Acadêmica e Técnica:** A escolha do SVD foi validada tanto por pesquisa de mercado quanto por recomendação de especialistas acadêmicos, incluindo o professor de matemática do grupo.\n", "\n", "Além do SVD, testamos abordagens baseadas em similaridade de usuários (KNN), mas observamos limitações quanto à diversidade e profundidade das recomendações, especialmente em situações de cold start e alta esparsidade dos dados. O SVD demonstrou desempenho superior nesses aspectos, justificando sua adoção como modelo principal deste projeto."]}, {"cell_type": "markdown", "metadata": {}, "source": ["O modelo SVD não conhece regras de negócio, perfis de risco ou classificações CVM. Ele **trabalha exclusivamente com a matriz de interação `Cliente x Ativo`, usando como nota de afinidade o valor financeiro investido**.\n", "\n", "O modelo parte do seguinte princípio:\n", "\n", "> “Se clientes semelhantes ao cliente A investem em ativos X, Y e Z, então é provável que A também se interesse por esses ativos, mesmo que ainda não os tenha.”\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### **Contexto e Lições da Entrega Anterior**\n", "\n", "Este notebook utiliza os resultados da entrega anterior, documentada em `Classifier_Pipeline.ipynb`, que gerou o arquivo `inconformidades.csv`. Naquela etapa, processamos o dataset `PosicaoIntelli.csv` para identificar carteiras inconformes com os perfis de risco (Conservador, Moderado, Arrojado, Sofisticado) usando um modelo de classificação baseado em **Random Forest**, que considerou variáveis como valores financeiros e tipos de ativos. As principais lições foram:\n", "\n", "- **Alta taxa de inconformidade**: 70,35% das carteiras estavam desalinhadas com os perfis de risco, destacando a necessidade de recomendações para readequação.\n", "- **Inconsistências nos dados**: Valores negativos na coluna `Financeiro` e nomes de ativos não padronizados (e.g., espaços extras) exigiram tratamentos como remoção e normalização.\n", "- **Distribuição dos perfis**: A maioria dos clientes é Sofisticada (102.019), segu<PERSON> por <PERSON> (82.913) e Conservador (16.367), indicando a importância de priorizar Renda Fixa para Conservadores e Moderados.\n", "\n", "Essas liç<PERSON>es foram aplicadas ao:\n", "- Padronizar dinamicamente os nomes dos ativos para evitar erros no merge com `riscos.csv`.\n", "- Normalizar os valores financeiros com MinMaxScaler para scores consistentes.\n", "- Implementar o SVD com filtros regulatórios (CVM 175) para recomendações conformes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from surprise import SVD, Dataset, Reader\n", "from surprise.model_selection import train_test_split\n", "from surprise.model_selection import GridSearchCV\n", "\n", "plt.style.use('seaborn')\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Carregamento dos dados\n", "inconformidades = pd.read_csv('inconformidades.csv')\n", "riscos = pd.read_csv('Tipo_ativos_com_risco.csv')\n", "\n", "print(\"Tabela de Inconformidades:\") \n", "print(inconformidades.head())\n", "print(\"Tabela de Riscos:\")\n", "print(riscos.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Estatísticas descritivas dos valores financeiros\n", "print(\"Estatísticas dos valores financeiros (Financeiro):\")\n", "print(inconformidades['Financeiro'].describe())\n", "\n", "# Distribuição dos perfis de risco\n", "print(\"\\nDistribuição dos perfis de risco:\")\n", "print(inconformidades['Perfil Carteira'].value_counts())\n", "\n", "# Visualização: Histograma dos valores financeiros\n", "plt.figure(figsize=(10, 6))\n", "sns.histplot(inconformidades['Financeiro'], bins=50, kde=True)\n", "plt.title('Distribuição dos Valores Financeiros Investidos')\n", "plt.xlabel('Valor Financeiro (R$)')\n", "plt.ylabel('Frequência')\n", "plt.show()\n", "\n", "# Visualização: Contagem de perfis de risco\n", "plt.figure(figsize=(8, 5))\n", "sns.countplot(x='Perfil Carteira', data=inconformidades)\n", "plt.title('Distribuição dos Perfis de Risco dos Clientes')\n", "plt.xlabel('Perfil de Risco')\n", "plt.ylabel('Número de Clientes')\n", "plt.show()\n", "\n", "# Análise de esparsidade da matriz Cliente x Ativo\n", "matriz_interacao = inconformidades.pivot_table(index='Conta', columns='Nome Ativo', values='Financeiro')\n", "sparsity = 1 - (matriz_interacao.count().sum() / (matriz_interacao.shape[0] * matriz_interacao.shape[1]))\n", "print(f\"\\nEsparsidade da matriz Cliente x Ativo: {sparsity:.2%}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### **<PERSON><PERSON><PERSON><PERSON>**\n", "A análise acima revela características importantes dos dados:\n", "\n", "- **Valores Financeiros**: A média dos valores financeiros investidos é de R$ 1.808.409, com um desvio padrão elevado (R$ 30.487.800), indicando alta variabilidade. O valor mínimo é negativo (R$ -2.678.495.000), refletindo desvalorizações significativas, enquanto o máximo é de R$ 8.456.713.000. A mediana (R$ 37.797,92) é muito menor que a média, confirmando uma distribuição assimétrica com muitos valores baixos e alguns outliers elevados. Essa assimetria justifica a normalização com MinMaxScaler, implementada para evitar distorções no modelo SVD, especialmente considerando que a entrega anterior (Project_Notebook (1).ipynb) já havia identificado valores negativos na coluna Financeiro.\n", "- **<PERSON><PERSON><PERSON>**: <PERSON><PERSON> 201.299 registros, a maioria dos clientes inconformes tem perfil Sofi<PERSON>ado (102.019), se<PERSON><PERSON> <PERSON>or <PERSON> (82.913) e Conservador (16.367). No entanto, a entrega anterior mostrou que 70,35% das carteiras foram classificadas como Conservador (4.318 clientes), indicando que muitos clientes Sofisticados e Moderados têm carteiras desalinhadas (muito conservadoras). As recomendações devem priorizar ativos de Renda Variável para readequar essas carteiras, especialmente para os Sofisticados.\n", "- **Esparsidade**: A matriz Cliente x Ativo apresenta uma esparsidade de 99,67%, o que é esperado dado o grande número de ativos e a concentração de investimentos em poucos deles por cliente. Isso reforça a escolha do SVD, que é robusto para dados esparsos ao capturar fatores latentes, e alinha-se com a observação da entrega anterior de que muitos clientes investem em poucos ativos populares.\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A base de dados apresenta alguns vieses potenciais que podem impactar o modelo:\n", "- **Concentração em Renda Fixa**: A entrega anterior identificou que 70,35% das carteiras são classificadas como Conservador, com forte presença de ativos de Renda Fixa. Isso pode levar o modelo SVD a priorizar esses ativos, mesmo para clientes Sofisticados que poderiam se beneficiar de Renda Variável.\n", "- **Assimetria nos Valores Financeiros**: A diferença entre a média (R$ 1.808.409) e a mediana (R$ 37.797,92) indica que poucos clientes concentram grandes investimentos, o que pode enviesar as recomendações para ativos de alto valor.\n", "- **Representatividade**: A predominância de perfis <PERSON> (102.019 registros) pode sub-representar clientes Conservadores (16.367), dificultando recomendações precisas para esse grupo."]}, {"cell_type": "markdown", "metadata": {}, "source": ["A matriz de entrada para o modelo foi construída a partir das seguintes colunas do dataset `inconformidades.csv`:\n", "\n", "```python\n", "df_model = inconformidades[['Conta', 'Nome Ativo', 'Financeiro']]"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [], "source": ["# Selecionar apenas colunas necessárias\n", "df_model = inconformidades[['Conta', 'Nome Ativo', 'Financeiro']].copy()\n", "\n", "# Remover valores negativos (prejudicam a recomendação)\n", "df_model['Financeiro'] = df_model['Financeiro'].clip(lower=0)\n", "\n", "# <PERSON><PERSON><PERSON> leitor do surprise\n", "reader = Reader(rating_scale=(0, df_model['Financeiro'].max()))\n", "data = Dataset.load_from_df(df_model[['Con<PERSON>', 'Nome Ativo', 'Financeiro']], reader)\n", "\n", "# Dividir treino e teste\n", "trainset, testset = train_test_split(data, test_size=0.2, random_state=42)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Definir intervalo de hiperparâmetros para teste\n", "param_grid = {\n", "    'n_factors': [10, 20, 50],  # <PERSON><PERSON><PERSON><PERSON> de fatores latentes\n", "    'n_epochs': [20, 30],       # Número de iterações\n", "    'lr_all': [0.005, 0.01],    # Taxa de aprendizado\n", "    'reg_all': [0.02, 0.1]      # Regularização\n", "}\n", "\n", "# Configurar GridSearchCV\n", "gs = GridSearchCV(SVD, param_grid, measures=['rmse', 'mae'], cv=3)\n", "gs.fit(data)\n", "\n", "# <PERSON><PERSON><PERSON> me<PERSON><PERSON>\n", "print(\"Melhores hiperparâmetros:\", gs.best_params['rmse'])\n", "print(\"Melhor RMSE:\", gs.best_score['rmse'])\n", "\n", "# Usar os melhores hiperparâmetros para o modelo\n", "model = SVD(n_factors=gs.best_params['rmse']['n_factors'],\n", "            n_epochs=gs.best_params['rmse']['n_epochs'],\n", "            lr_all=gs.best_params['rmse']['lr_all'],\n", "            reg_all=gs.best_params['rmse']['reg_all'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### **Otimização de Hiperparâmetros**\n", "Utilizamos GridSearchCV para testar combinações dos seguintes hiperparâmetros:\n", "- `n_factors`: [10, 20, 50, 100] (número de fatores latentes)\n", "- `n_epochs`: [10, 20, 30] (número de iterações)\n", "- `lr_all`: [0.005, 0.01, 0.02] (taxa de aprendizado)\n", "- `reg_all`: [0.02, 0.1, 0.2] (regularização)\n", "O melhor modelo foi selecionado com base no menor RMSE no conjunto de validação cruzada."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = SVD()\n", "model.fit(trainset)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Selecionar cliente exemplo\n", "cliente_alvo = df_model['Conta'].iloc[750]\n", "print(\"Cliente alvo: \", cliente_alvo)\n", "print(\"\")\n", "print(\"Ativos já possuídos: \", df_model[df_model['Conta'] == cliente_alvo]['Nome Ativo'].unique())\n", "print(\"\")\n", "\n", "print(\"-\"*100)\n", "print(\"\")\n", "# Ativos que ele ainda não tem\n", "ativos_todos = set(df_model['Nome Ativo'].unique())\n", "ativos_cliente = set(df_model[df_model['Conta'] == cliente_alvo]['Nome Ativo'])\n", "ativos_para_prever = list(ativos_todos - ativos_cliente)\n", "\n", "# Previsões\n", "recomendacoes = []\n", "for ativo in ativos_para_prever:\n", "    pred = model.predict(uid=cliente_alvo, iid=ativo)\n", "    recomendacoes.append((ativo, pred.est))\n", "\n", "# Ordenar por afinidade prevista\n", "recomendacoes_ordenadas = sorted(recomendacoes, key=lambda x: x[1], reverse=True)\n", "\n", "\n", "print(\"Afinidade prevista para os ativos:\")\n", "# Mostrar top 10\n", "for ativo, score in recomendacoes_ordenadas[:10]:\n", "    print(f'{ativo}: {score:.2f}')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Na primeira execução do modelo, observou-se que todos os **scores de recomendação eram iguais** e com **valores extremamente altos** (ex: `8456713057.73` para todos os ativos recomendados).  \n", "Esse comportamento foi identificado como consequência da ausência de normalização dos dados financeiros de entrada. Os valores originais da coluna `Financeiro` variavam na casa dos **milhares ou milhões de reais**, distorcendo completamente o comportamento do modelo.\n", "\n", "**Implicações:**\n", "- Todos os ativos foram atribuídos com a mesma afinidade.\n", "- O modelo não aprendeu nenhuma distinção significativa entre os ativos.\n", "- O sistema não era capaz de ranquear preferências reais.\n", "\n", "Vamos tentar ajustar e ver se esse comportamento se altera."]}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [], "source": ["# Escalar os valores entre 0 e 1 (min-max)\n", "from sklearn.preprocessing import MinMaxScaler\n", "\n", "scaler = MinMaxScaler()\n", "df_model['Financeiro_Normalizado'] = scaler.fit_transform(df_model[['Financeiro']])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### **Justificativa da Normalização com MinMaxScaler**\n", "\n", "O MinMaxScaler foi escolhido para normalizar os valores financeiros (coluna `Financeiro`) para o intervalo [0, 1] pelos seguintes motivos:\n", "- **Compatibilidade com SVD**: O SVD da biblioteca `surprise` espera notas de afinidade em uma escala definida. O MinMaxScaler garante que todos os valores estejam dentro de um intervalo consistente.\n", "- **Preservação da Distribuição Relativa**: Diferentemente do StandardScaler, que centraliza os dados em média zero, o MinMaxScaler mantém a proporcionalidade dos valores financeiros, importante para refletir a afinidade do cliente com os ativos.\n", "- **Robustez a Outliers**: Embora a distribuição dos valores financeiros seja assimétrica (média de R$ 1.808.409 vs. mediana de R$ 37.797,92), o MinMaxScaler é menos sensível a outliers extremos, garan<PERSON><PERSON> que os scores normalizados sejam interpretáveis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Usar a coluna normalizada\n", "reader = Reader(rating_scale=(0, 1))\n", "data = Dataset.load_from_df(df_model[['Con<PERSON>', 'Nome Ativo', 'Financeiro_Normalizado']], reader)\n", "\n", "trainset, testset = train_test_split(data, test_size=0.2, random_state=42)\n", "model = SVD()\n", "model.fit(trainset)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Selecionar cliente exemplo\n", "cliente_alvo = df_model['Conta'].iloc[750]\n", "print(\"Cliente alvo: \", cliente_alvo)\n", "print(\"\")\n", "#print(\"Ativos já possuídos: \", df_model[df_model['Conta'] == cliente_alvo]['Nome Ativo'].unique())\n", "#print(\"\")\n", "\n", "print(\"-\"*100)\n", "print(\"\")\n", "\n", "# Ativos que ele ainda não tem\n", "ativos_todos = set(df_model['Nome Ativo'].unique())\n", "ativos_cliente = set(df_model[df_model['Conta'] == cliente_alvo]['Nome Ativo'])\n", "ativos_para_prever = list(ativos_todos - ativos_cliente)\n", "\n", "# Previsões\n", "recomendacoes = []\n", "for ativo in ativos_para_prever:\n", "    pred = model.predict(uid=cliente_alvo, iid=ativo)\n", "    recomendacoes.append((ativo, pred.est))\n", "\n", "# Ordenar por afinidade prevista\n", "recomendacoes_ordenadas = sorted(recomendacoes, key=lambda x: x[1], reverse=True)\n", "\n", "\n", "print(\"Afinidade prevista para os ativos:\")\n", "# Mostrar top 10\n", "for ativo, score in recomendacoes_ordenadas[:10]:\n", "    print(f'{ativo}: {score:.2f}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Para contornar o problema, aplicou-se a técnica de **Min-Max Normalization** à coluna `Financeiro`, reescalando os valores para o intervalo `[0, 1]`.  \n", "A nova coluna `Financeiro_Normalizado` passou a ser utilizada como métrica de entrada para o modelo SVD.\n", "\n", "```python\n", "from sklearn.preprocessing import MinMaxScaler\n", "\n", "scaler = MinMaxScaler()\n", "df_model['Financeiro_Normalizado'] = scaler.fit_transform(df_model[['Financeiro']])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "----\n", "O modelo SVD é **colaborativo puro** —ele **não sabe nada sobre perfis de risco, nem sobre regras regulatórias**. Ele apenas encontra padrões matemáticos nas interações.\n", "\n", "**Implicação crítica:**\n", "\n", "> Um cliente com perfil `Conservador` pode receber recomendação de um ativo `Sofisticado`, **caso esse ativo seja comum entre carteiras similares.**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Logo, para garantir a **conformidade regulatória**, será necessário um **filtro pós-modelo** para verificar o perfil carteira esperado do cliente, avaliar o nível de risco de cada ativo recomendado e remover os ativos que não são adequados para o perfil do cliente.\n", "Exemplo:\n", "> Cliente conservador -> somente ativos classificados como \"Renda Fixa\" ou risco \"Baixo\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. <PERSON><PERSON><PERSON> nome da coluna em riscos\n", "riscos.rename(columns={'Tipo de Ativo': 'Tipo Ativo'}, inplace=True)\n", "\n", "# Merge com base no tipo de ativo para trazer o nível de risco\n", "inconformidades_com_risco = inconformidades.merge(\n", "    riscos,\n", "    on='Tipo Ativo',\n", "    how='left',\n", "    suffixes=('', '_Risco')\n", ")\n", "\n", "# Verificação rápida\n", "print(\"Ativos sem risco classificado:\", inconformidades_com_risco['Risco'].isna().sum())\n", "print(inconformidades_com_risco['Risco'].value_counts())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ver todos os tipos de ativo que ficaram sem risco\n", "tipos_sem_classificacao = inconformidades_com_risco[inconformidades_com_risco['Risco'].isna()]['Tipo Ativo'].unique()\n", "print(\"Tipos de Ativo sem classificação de risco:\", tipos_sem_classificacao)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Percebemos um número de ativos que ficaram sem classificação. \n", "Fazendo uma análise de quais foram esses ativos, consegui perceber que eles estão presentes na tabela ```riscos.csv```, o problema é que os nomes de alguns ativos na tabela inconformidades possuem espaços extras à direita.\n", "Vamos então filtrar esses espaços."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Tabela de Riscos:\")\n", "print(riscos.columns)"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [], "source": ["# Remover espaços extras antes de mesclar\n", "inconformidades['Tipo Ativo'] = inconformidades['Tipo Ativo'].str.strip()\n", "riscos['Tipo Ativo'] = riscos['Tipo Ativo'].str.strip()"]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [], "source": ["# Realizar o merge agora com nomes padronizados\n", "inconformidades_com_risco = inconformidades.merge(\n", "    riscos, left_on='Tipo Ativo', right_on='Tipo Ativo', how='left')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["riscos.rename(columns={'Tipo de Ativo': 'Tipo Ativo'}, inplace=True)\n", "\n", "# Merge com base no tipo de ativo para trazer o nível de risco\n", "inconformidades_com_risco = inconformidades.merge(\n", "    riscos,\n", "    on='Tipo Ativo',\n", "    how='left',\n", "    suffixes=('', '_Risco')\n", ")\n", "\n", "# Verificação rápida\n", "print(\"Ativos sem risco classificado:\", inconformidades_com_risco['Risco'].isna().sum())\n", "print(inconformidades_com_risco['Risco'].value_counts())\n", "print(inconformidades_com_risco.columns)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Percebemos agora que o número de ativos sem risco classificado diminuiu bastante de 2838 para 67.\n", "Seguimos com uma análise para solucionar o problema por completo."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ver todos os tipos de ativo que ficaram sem risco\n", "tipos_sem_classificacao = inconformidades_com_risco[inconformidades_com_risco['Risco'].isna()]['Tipo Ativo'].unique()\n", "print(\"Tipos de Ativo sem classificação de risco:\", tipos_sem_classificacao)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<PERSON><PERSON>, percebe-se que os ativos existem na minha tabelas ativos_classificados_teste.csv, mas com nomenclaturas diferentes, então fiz a substituição."]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [], "source": ["# Substituir diretamente na coluna original, sem criar nova\n", "correcoes_tipo_ativo = {\n", "    'Peso Mexicano por Dólar dos Es': 'Peso Mexicano por Dólar dos Estados Unidos',\n", "    '<PERSON><PERSON> por Dólar dos Esta': '<PERSON><PERSON> por Dólar dos Estados Unidos',\n", "    '<PERSON><PERSON> por Dólar dos Est': '<PERSON><PERSON> por Dólar dos Estados Unidos',\n", "    'Franco Suíço por Dólar dos Est': 'Franco Suíço por Dólar dos Estados Unidos',\n", "    'Coroa Sueca por Dólar dos Esta': 'Coroa Sueca por Dólar dos Estados Unidos',\n", "    '<PERSON>roa <PERSON>ue<PERSON> por Dólar dos': '<PERSON><PERSON>ue<PERSON> por Dólar dos Estados Unidos',\n", "    'Rande da África do Sul por Dól': 'Rande da África do Sul por Dólar dos Estados Unidos',\n", "    'Peso Chileno por Dólar dos Est': 'Peso Chileno por Dólar dos Estados Unidos',\n", "    'Dólar dos Estados Unidos da Am': 'DOLAR FUTURO',\n", "    'Dólar Canadense por Dólar dos': 'DOLAR FUTURO'\n", "}\n", "\n", "# Fazer uma cópia e só depois aplicar substituição\n", "inconformidades['Tipo Ativo Original'] = inconformidades['Tipo Ativo']\n", "inconformidades['Tipo Ativo'] = inconformidades['Tipo Ativo'].replace(correcoes_tipo_ativo)\n", "\n", "# Garantir limpeza de espaços\n", "inconformidades['Tipo Ativo'] = inconformidades['Tipo Ativo'].str.strip()\n", "riscos['Tipo Ativo'] = riscos['Tipo Ativo'].str.strip()\n", "\n", "# Merge limpo e direto\n", "inconformidades_com_risco = inconformidades.merge(\n", "    riscos, left_on='Tipo Ativo', right_on='Tipo Ativo', how='left'\n", ")\n", "\n", "# (Opcional) Eliminar coluna 'Tipo de Ativo' duplicada\n", "inconformidades_com_risco.drop(columns=['Tipo Ativo'], inplace=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inconformidades_com_risco = inconformidades.merge(\n", "    riscos, left_on='Tipo Ativo', right_on='Tipo Ativo', how='left'\n", ")\n", "print(\"Restam ativos sem risco classificado:\", inconformidades_com_risco['Risco'].isna().sum())\n", "\n", "# Ver todos os tipos de ativo que ficaram sem risco\n", "tipos_sem_classificacao = inconformidades_com_risco[inconformidades_com_risco['Risco'].isna()]['Tipo Ativo'].unique()\n", "print(\"Tipos de Ativo sem classificação de risco:\", tipos_sem_classificacao)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Agora restaram apenas 5, vou tentar visualizar quantas vezes cada ativo sem risco classificado aparece no DataFrame de inconformidades."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ativos_sem_risco = [\n", "    'OPÇÃO S/ FUTURO DE D13',\n", "    'FUTURO DE HYPE3 FUTURO fev2019',\n", "    'FUTURO DE PSSA3 FUTURO fev2019',\n", "    'WEUF'\n", "]\n", "\n", "ocorrencias = inconformidades[inconformidades['Tipo Ativo'].isin(ativos_sem_risco)]['Tipo Ativo'].value_counts()\n", "print(ocorrencias)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Como o número de ocorrências é baixo, vamos eliminar esses ativos."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inconformidades_com_risco = inconformidades_com_risco[~inconformidades_com_risco['Tipo Ativo'].isin(ativos_sem_risco)]\n", "\n", "# Ver todos os tipos de ativo que ficaram sem risco\n", "tipos_sem_classificacao = inconformidades_com_risco[inconformidades_com_risco['Risco'].isna()]['Tipo Ativo'].unique()\n", "print(\"Tipos de Ativo sem classificação de risco:\", tipos_sem_classificacao)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Perfeito! "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def risco_permitido(perfil, risco):\n", "    \"\"\"Verifica se o risco do ativo é permitido para o perfil do cliente.\"\"\"\n", "    regras_perfil = {\n", "        'Conservador': ['Conservador'],\n", "        'Moderado': ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],\n", "        'Arrojado': ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],\n", "        'Sofisticado': ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Sofisticado']\n", "    }\n", "    return risco in regras_perfil.get(perfil, [])\n", "\n", "def recomendar_para_cliente(cliente_id, model, df_model, risco_df, top_n=10):\n", "    \"\"\"Gera recomendações para rebalancear a carteira do cliente conforme o perfil.\n", "    \n", "    Parâmetros:\n", "    - cliente_id: ID do cliente (int ou str).\n", "    - model: Objeto do modelo SVD treinado.\n", "    - df_model: DataFrame com colunas ['Conta', 'Nome Ativo', 'Financeiro_Normalizado'].\n", "    - risco_df: DataFrame com colunas ['<PERSON><PERSON>', 'Nome Ativo', '<PERSON><PERSON>', 'Tipo Ativo Renda', '<PERSON><PERSON><PERSON>', 'Perfil Investimentos'].\n", "    - top_n: Número de recomendações a retornar (int, padrão=10).\n", "    \n", "    Retorna:\n", "    - Lista de tuplas (ativo, score, tipo_renda_ativo) com as top-N recomendações, perfil do cliente, proporções e inconsistências.\n", "    \"\"\"\n", "    # Obter perfil do cliente\n", "    perfil_carteira = risco_df[risco_df['Conta'] == cliente_id]['Perfil Carteira'].unique()\n", "    if len(perfil_carteira) == 0:\n", "        raise ValueError(f\"Cliente {cliente_id} não encontrado.\")\n", "    perfil_carteira = perfil_carteira[0]\n", "    \n", "    # Obter perfil de investimentos\n", "    perfil_investimentos = risco_df[risco_df['Conta'] == cliente_id]['Perfil Investimentos'].unique()\n", "    perfil_investimentos = perfil_investimentos[0] if len(perfil_investimentos) > 0 else 'Desconhecido'\n", "    \n", "    # Identificar ativos da carteira\n", "    carteira_cliente = risco_df[risco_df['Conta'] == cliente_id][['Nome Ativo', 'Risco', 'Tipo Ativo Renda']]\n", "    \n", "    # Calcular proporção atual\n", "    total_ativos = len(carteira_cliente)\n", "    renda_fixa_count = len(carteira_cliente[carteira_cliente['Tipo Ativo Renda'] == 'Renda Fixa'])\n", "    proporcao_renda_fixa = renda_fixa_count / total_ativos if total_ativos > 0 else 0\n", "    proporcao_renda_variavel = 1 - proporcao_renda_fixa\n", "    \n", "    # Definir proporções ideais\n", "    proporcoes_ideais = {\n", "        'Conservador': {'Renda Fixa': 0.9, 'Renda Variável': 0.1},\n", "        'Moderado': {'Renda Fixa': 0.6, 'Ren<PERSON>ável': 0.4},\n", "        'Arrojado': {'Renda Fixa': 0.3, 'Renda Variável': 0.7},\n", "        'Sofisticado': {'Renda Fixa': 0.3, 'Renda Variável': 0.7}\n", "    }\n", "    proporcao_ideal = proporcoes_ideais.get(perfil_carteira, {'Renda Fixa': 0.5, 'Renda Variável': 0.5})\n", "    \n", "    # Verificar inconsistências\n", "    inconsistencias = []\n", "    for _, row in carteira_cliente.iterrows():\n", "        if not risco_permitido(perfil_carteira, row['Risco']):\n", "            inconsistencias.append((row['Nome Ativo'], row['<PERSON><PERSON>'], row['Tipo Ativo Renda']))\n", "    \n", "    # Determinar tipo de recomendação para rebalancear\n", "    n_renda_fixa = int(top_n * proporcao_ideal['Renda Fixa'])\n", "    n_renda_variavel = top_n - n_renda_fixa\n", "    \n", "    # Ajustar com base no desalinhamento\n", "    if proporcao_renda_fixa < proporcao_ideal['Renda Fixa'] - 0.1:  # Tolerância de 10%\n", "        n_renda_fixa = min(top_n, n_renda_fixa + 2)  # <PERSON><PERSON><PERSON> ma<PERSON>\n", "        n_renda_variavel = top_n - n_renda_fixa\n", "    elif propor<PERSON><PERSON>_renda_variavel < proporcao_ideal['Renda Variável'] - 0.1:\n", "        n_renda_variavel = min(top_n, n_renda_variavel + 2)  # <PERSON><PERSON><PERSON> ma<PERSON>\n", "        n_renda_fixa = top_n - n_renda_variavel\n", "    \n", "    # Obter ativos não possuídos\n", "    ativos_cliente = set(df_model[df_model['Conta'] == cliente_id]['Nome Ativo'])\n", "    ativos_todos = set(df_model['Nome Ativo'].unique())\n", "    ativos_para_prever = list(ativos_todos - ativos_cliente)\n", "    \n", "    # G<PERSON>r previsões\n", "    recomendacoes_fixa = []\n", "    recomendacoes_variavel = []\n", "    for ativo in ativos_para_prever:\n", "        try:\n", "            pred = model.predict(uid=cliente_id, iid=ativo)\n", "            risco_ativo = risco_df.loc[risco_df['Nome Ativo'] == ativo, 'Risco'].values\n", "            tipo_renda_ativo = risco_df.loc[risco_df['Nome Ativo'] == ativo, 'Tipo Ativo Renda'].values\n", "            if risco_ativo.size > 0 and risco_permitido(perfil_carteira, risco_ativo[0]):\n", "                tipo = tipo_renda_ativo[0] if tipo_renda_ativo.size > 0 else 'Desconhecido'\n", "                if tipo == 'Renda Fixa':\n", "                    recomendacoes_fixa.append((ativo, pred.est, tipo))\n", "                elif tipo == 'Renda Variável':\n", "                    recomendacoes_variavel.append((ativo, pred.est, tipo))\n", "        except Exception as e:\n", "            print(f\"Erro ao prever para {ativo}: {e}\")\n", "    \n", "    # Ordenar recomendações\n", "    recomendacoes_fixa = sorted(recomendacoes_fixa, key=lambda x: x[1], reverse=True)[:n_renda_fixa]\n", "    recomendacoes_variavel = sorted(recomendacoes_variavel, key=lambda x: x[1], reverse=True)[:n_renda_variavel]\n", "    \n", "    # Combinar recomendações\n", "    recomendacoes_ordenadas = recomendacoes_fixa + recomendacoes_variavel\n", "    recomendacoes_ordenadas = sorted(recomendacoes_ordenadas, key=lambda x: x[1], reverse=True)[:top_n]\n", "    \n", "    return (recomendacoes_ordenadas, perfil_carteira, perfil_investimentos, \n", "            proporcao_renda_fixa, proporcao_renda_variavel, inconsistencias)\n", "\n", "# Testar a função\n", "cliente_alvo = 856\n", "\n", "# Definir proporções ideais para o teste (mesmo dicionário da função)\n", "proporcoes_ideais = {\n", "    'Conservador': {'Renda Fixa': 0.9, 'Renda Variável': 0.1},\n", "    'Moderado': {'Renda Fixa': 0.6, 'Ren<PERSON>ável': 0.4},\n", "    'Arrojado': {'Renda Fixa': 0.3, 'Renda Variável': 0.7},\n", "    'Sofisticado': {'Renda Fixa': 0.3, 'Renda Variável': 0.7}\n", "}\n", "\n", "#Recomendações para rebalancear a carteira\n", "print(\"-\" * 100)\n", "print(\"Recomendações para rebalancear a carteira\")\n", "(recomendacoes, perfil_carteira, perfil_investimentos, \n", " proporcao_renda_fixa, proporcao_renda_variavel, inconsistencias) = recomendar_para_cliente(\n", "    cliente_id=cliente_alvo,\n", "    model=model,\n", "    df_model=df_model,\n", "    risco_df=inconformidades_com_risco,\n", "    top_n=10\n", ")\n", "print(f\"Cliente alvo: {cliente_alvo}\")\n", "print(f\"Per<PERSON><PERSON> (declarado): {perfil_carteira}\")\n", "print(f\"Perfil Investimentos (real): {perfil_investimentos}\")\n", "print(f\"Proporção atual - Renda Fixa: {proporcao_renda_fixa:.2%}, Renda Variável: {proporcao_renda_variavel:.2%}\")\n", "print(f\"Proporção ideal - Renda Fixa: {proporcoes_ideais[perfil_carteira]['Renda Fixa']:.2%}, \"\n", "      f\"Renda Variável: {proporcoes_ideais[perfil_carteira]['Renda Variável']:.2%}\")\n", "print(\"\\nInconsistências na carteira:\")\n", "if inconsistencias:\n", "    for ativo, risco, tipo in inconsistencias:\n", "        print(f\"- {ativo} (Risco: {risco}, Tipo: {tipo})\")\n", "else:\n", "    print(\"Nenhuma inconsistência encontrada.\")\n", "print(\"\\nTop 10 recomendações:\")\n", "if recomendacoes:\n", "    for ativo, score, tipo in recomendacoes:\n", "        print(f\"- {ativo}: Score {score:.2f} (Tipo: {tipo})\")\n", "else:\n", "    print(\"Nenhuma recomendação válida encontrada.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### **Conformidade com a Regulação CVM 175**\n", "A Instrução CVM 175 exige que os investimentos sejam adequados ao perfil de risco do cliente, definido como Conservador, Moderado, <PERSON><PERSON><PERSON><PERSON> ou Sofisticado. As regras aplicadas no modelo incluem:\n", "- **Conservador**: Apenas ativos com risco classificado como Conservador são permitidos, priorizando Renda Fixa (90% da carteira).\n", "- **Moderado**: Ativos com risco Conservador ou Moderado, com proporção ideal de 60% Renda Fixa e 40% Renda Variável.\n", "- **Arrojado/Sofisticado**: Permite ativos de todos os níveis de risco, com proporção ideal de 30% Renda Fixa e 70% Renda Variável.\n", "A função `risco_permitido` implementa essas restrições, filtrando recomendações para garantir que apenas ativos compatíveis com o perfil sejam sugeridos."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from surprise import accuracy\n", "\n", "# Previsões no conjunto de teste\n", "predictions = model.test(testset)\n", "\n", "# C<PERSON>l<PERSON>lo das métricas\n", "rmse = accuracy.rmse(predictions, verbose=True)\n", "mae = accuracy.mae(predictions, verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Após o treinamento, realizamos a avaliação de desempenho utilizando métricas padronizadas para problemas de regressão, já que o modelo aprende a prever um valor contínuo de afinidade (baseado na normalização dos valores financeiros investidos).\n", "\n", "#### **Métricas Utilizadas:**\n", "\n", "* **RMSE (Root Mean Squared Error)**:\n", "\n", "  * Mede o erro quadrático médio entre os valores previstos e os reais.\n", "  * Penaliza erros maiores mais severamente, destacando casos onde a previsão foi muito imprecisa.\n", "\n", "* **MAE (Mean Absolute Error)**:\n", "\n", "  * Mede o erro absoluto médio.\n", "  * Fornece uma média direta da diferença entre o valor previsto e o real, sendo menos sensível a outliers.\n", "\n", "#### **Resultados Obtidos:**\n", "\n", "```plaintext\n", "RMSE: 0.0264\n", "MAE:  0.0107\n", "```\n", "\n", "#### **Interpretação dos Resultados:**\n", "\n", "Considerando que os dados foram **normalizados entre 0 e 1**, esses valores indicam que o modelo é **extremamente preciso**:\n", "\n", "* O RMSE de 0.0264 representa um erro médio quadrático de apenas 2,64% do intervalo de notas possíveis.\n", "* O MAE de 0.0107 indica que, em média, o modelo erra suas previsões em apenas 1,07%.\n", "\n", "Tais métricas demonstram que o modelo **consegue prever com alta fidelidade a afinidade dos clientes com diferentes ativos financeiros**, mesmo sem conhecer diretamente seus perfis ou regras de negócio."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "def precision_at_k(predictions, k=10, threshold=0.1):\n", "    \"\"\"Calcula a precisão@k para recomendações.\"\"\"\n", "    user_est_true = defaultdict(list)\n", "    for uid, _, true_r, est, _ in predictions:\n", "        user_est_true[uid].append((est, true_r))\n", "    \n", "    precisions = []\n", "    for uid, user_ratings in user_est_true.items():\n", "        user_ratings.sort(key=lambda x: x[0], reverse=True)  # Ordenar por estimativa\n", "        top_k = user_ratings[:k]\n", "        n_rel = sum((true_r >= threshold) for (_, true_r) in top_k)  # Relevantes\n", "        precisions.append(n_rel / k if k > 0 else 0)\n", "    \n", "    return sum(precisions) / len(precisions) if precisions else 0\n", "\n", "# Calcular precisão@10\n", "precision_k = precision_at_k(predictions, k=10, threshold=0.1)\n", "print(f\"Precisão@10: {precision_k:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Interpretação da Precisão@10**: O valor de 0.0001 reflete a alta esparsidade da matriz (99,67%) e o limiar conservador (0.1). Como poucos clientes possuem investimentos significativos em muitos ativos, a métrica subestima a relevância das recomendações. Futuras análises podem ajustar o limiar ou usar métricas como NDCG para capturar melhor a ordenação."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import joblib\n", "\n", "# Salvar o modelo SVD treinado\n", "joblib.dump(model, 'modelo_svd_treinado.pkl')\n", "print(\"Modelo salvo em 'modelo_svd_treinado.pkl'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Compartilhamento do Projeto\n", "O código deste notebook foi versionado e disponibilizado no GitHub do grupo, na pasta src/models:\n", "\n", "Repositório GitHub: [https://github.com/Inteli-College/2025-1B-T13-ES06-G02]\n", "Caminho do Notebook: src/models/SVD.ipynb\n", "Caminho do Modelo Salvo: src/models/modelo_svd_treinado.pkl"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}