# Dockerfile para o serviço Python SVD
# Baseado no notebook SVD_CollaborativeFiltering_CVM175.ipynb
FROM python:3.9-slim

WORKDIR /app

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copiar requirements
COPY requirements.txt .

# Instalar dependências Python
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código do serviço
COPY svd_model_service.py .

# Criar diretório para modelos
RUN mkdir -p /app/models

# Copiar modelo treinado (será montado via volume)
# COPY ../models/modelo_svd_treinado.pkl /app/models/

# Expor porta
EXPOSE 5000

# Variáveis de ambiente
ENV FLASK_APP=svd_model_service.py
ENV FLASK_ENV=production
ENV PORT=5000

# Comando para iniciar o serviço
CMD ["python", "svd_model_service.py"]
