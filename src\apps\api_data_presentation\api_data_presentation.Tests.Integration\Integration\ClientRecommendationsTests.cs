﻿using System;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Testing;
using Xunit;
using FluentAssertions;
using Npgsql;
using MongoDB.Driver;
using api_data_presentation.Models.DTOs;
using api_data_presentation.Models.Entities;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Hosting;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace api_data_presentation.Tests.Integration.Integration
{
    public class CustomWebApplicationFactory : WebApplicationFactory<Program>
    {
        protected override IHost CreateHost(IHostBuilder builder)
        {
            builder.UseEnvironment("Testing");

            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            });

            return base.CreateHost(builder);
        }
    }

    public class ClientRecommendationsTests : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly CustomWebApplicationFactory _factory;
        private readonly HttpClient _client;
        private readonly string _postgresConnectionString;
        private readonly string _mongoConnectionString;
        private readonly string _mongoDatabaseName;

        public ClientRecommendationsTests(CustomWebApplicationFactory factory)
        {
            _factory = factory;
            _client = _factory.CreateClient(new WebApplicationFactoryClientOptions
            {
                AllowAutoRedirect = false
            });

            // Carregar configurações do appsettings.json
            var configuration = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json")
                .Build();

            // Determinar se estamos rodando em container ou localmente
            var isRunningInContainer = Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") == "true";

            // Usar as strings de conexão apropriadas
            _postgresConnectionString = isRunningInContainer
                ? "Host=db;Database=mean_girls;Username=inteli;Password=**********"
                : configuration.GetConnectionString("PostgreSQL");

            _mongoConnectionString = isRunningInContainer
                ? "*****************************************************"
                : configuration.GetConnectionString("MongoDB");

            _mongoDatabaseName = configuration.GetSection("MongoDB:DatabaseName").Value ?? "mean_girls_mongo";
        }

        [Fact(DisplayName = "Deve retornar recomendações para cliente com perfil desalinhado")]
        public async Task GetClientRecommendations_WhenClientExists_ShouldReturnRecommendations()
        {
            // Arrange
            const int clientId = 123;
            await PrepareTestData(clientId);

            try
            {
                // Act
                var response = await _client.GetAsync($"/api/advisor/clients/{clientId}/recommendations");
                
                // Log para debug
                Console.WriteLine($"Response Status Code: {response.StatusCode}");
                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Response Content: {content}");

                // Assert - primeiro verificar o status code
                response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK, 
                    because: $"Expected OK status code but got {response.StatusCode} with content: {content}");

                // Se chegou aqui, o status code é OK e podemos deserializar
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                var recommendations = JsonSerializer.Deserialize<ClientRecommendationsDto>(content, options);

                // Assert
                recommendations.Should().NotBeNull();
                recommendations!.ClientId.Should().Be(clientId);
                recommendations.RiskProfileForm.Should().Be("Moderado");
                recommendations.RiskProfileWallet.Should().Be("Conservador");
                recommendations.RecommendedInvestments.Should().NotBeEmpty();
                recommendations.RecommendedInvestments.Should().AllSatisfy(investment =>
                {
                    investment.InvestmentId.Should().BeGreaterThan(0);
                    investment.Score.Should().BeInRange(0, 1);
                    investment.Name.Should().NotBeNullOrEmpty();
                    investment.Type.Should().NotBeNullOrEmpty();
                    investment.Risk.Should().NotBeNullOrEmpty();
                });
            }
            finally
            {
                // Cleanup
                await CleanupTestData(clientId);
            }
        }

        private async Task PrepareTestData(int clientId)
        {
            // 1. Preparar dados no PostgreSQL
            await using var conn = new NpgsqlConnection(_postgresConnectionString);
            await conn.OpenAsync();

            // Primeiro criar o advisor (necessário devido à chave estrangeira)
            var sqlAdvisor = @"
                INSERT INTO ADVISOR (advisor_id, uid)
                VALUES (@advisorId, 'test-advisor-uid')
                ON CONFLICT (advisor_id) DO UPDATE SET
                    uid = EXCLUDED.uid;";

            await using var cmdAdvisor = new NpgsqlCommand(sqlAdvisor, conn);
            cmdAdvisor.Parameters.AddWithValue("advisorId", 999); // ID fixo para testes
            await cmdAdvisor.ExecuteNonQueryAsync();

            // Inserir cliente
            var sqlClient = @"
                INSERT INTO CLIENT (client_id, name, email, phone_number, risk_profile_form, risk_profile_wallet, compliance, advisor_id)
                VALUES (@id, 'Test Client', '<EMAIL>', '11999999999', 'Moderado', 'Conservador', true, @advisorId)
                ON CONFLICT (client_id) DO UPDATE SET
                    email = EXCLUDED.email,
                    phone_number = EXCLUDED.phone_number,
                    risk_profile_form = EXCLUDED.risk_profile_form,
                    risk_profile_wallet = EXCLUDED.risk_profile_wallet;";

            await using var cmdClient = new NpgsqlCommand(sqlClient, conn);
            cmdClient.Parameters.AddWithValue("id", clientId);
            cmdClient.Parameters.AddWithValue("advisorId", 999);
            await cmdClient.ExecuteNonQueryAsync();

            // Inserir investimentos de exemplo
            var sqlInvestment = @"
                INSERT INTO INVESTMENT (investment_id, name, type, risk)
                VALUES 
                    (@id1, 'Tesouro IPCA+ 2026', 'Renda Fixa', 'Moderado'),
                    (@id2, 'Fundo Multimercado XYZ', 'Fundo', 'Moderado')
                ON CONFLICT (investment_id) DO UPDATE SET
                    name = EXCLUDED.name,
                    type = EXCLUDED.type,
                    risk = EXCLUDED.risk;";

            await using var cmdInvestment = new NpgsqlCommand(sqlInvestment, conn);
            cmdInvestment.Parameters.AddWithValue("id1", 456);
            cmdInvestment.Parameters.AddWithValue("id2", 457);
            await cmdInvestment.ExecuteNonQueryAsync();

            // 2. Preparar dados no MongoDB
            var mongoClient = new MongoClient(_mongoConnectionString);
            var database = mongoClient.GetDatabase(_mongoDatabaseName);
            var collection = database.GetCollection<MongoRecommendation>("recommendations");

            var recommendation = new MongoRecommendation
            {
                ClientId = clientId.ToString(),
                Investments = new List<RecommendedInvestment>
                {
                    new RecommendedInvestment { InvestmentId = "456", Score = 0.85 },
                    new RecommendedInvestment { InvestmentId = "457", Score = 0.75 }
                },
                RecommendationDate = DateTime.UtcNow
            };

            await collection.ReplaceOneAsync(
                r => r.ClientId == clientId.ToString(),
                recommendation,
                new ReplaceOptions { IsUpsert = true }
            );
        }

        private async Task CleanupTestData(int clientId)
        {
            // Limpar dados do PostgreSQL
            await using var conn = new NpgsqlConnection(_postgresConnectionString);
            await conn.OpenAsync();

            // Primeiro limpar o cliente devido à chave estrangeira
            var sqlClient = "DELETE FROM CLIENT WHERE client_id = @id;";
            await using var cmdClient = new NpgsqlCommand(sqlClient, conn);
            cmdClient.Parameters.AddWithValue("id", clientId);
            await cmdClient.ExecuteNonQueryAsync();

            // Depois limpar o advisor
            var sqlAdvisor = "DELETE FROM ADVISOR WHERE advisor_id = @advisorId;";
            await using var cmdAdvisor = new NpgsqlCommand(sqlAdvisor, conn);
            cmdAdvisor.Parameters.AddWithValue("advisorId", 999);
            await cmdAdvisor.ExecuteNonQueryAsync();

            // Limpar dados do MongoDB
            var mongoClient = new MongoClient(_mongoConnectionString);
            var database = mongoClient.GetDatabase(_mongoDatabaseName);
            var collection = database.GetCollection<MongoRecommendation>("recommendations");
            await collection.DeleteOneAsync(r => r.ClientId == clientId.ToString());
        }
    }
}
