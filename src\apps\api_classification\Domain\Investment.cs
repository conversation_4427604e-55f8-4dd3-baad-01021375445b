using System;
using System.Collections.Generic;

namespace Pati.ClassificationService.Domain
{
    public class Investment
    {
        public int InvestmentId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // Ex: stock, bond, fund, etc
        public string Risk { get; set; } = string.Empty; // Ex: conservative, moderate, aggressive
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Construtor padrão para Entity Framework
        public Investment() { }

        public Investment(int investmentId, string name, string type, string risk)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Investment name is required.");
            if (string.IsNullOrWhiteSpace(type)) throw new ArgumentException("Investment type is required.");
            if (string.IsNullOrWhiteSpace(risk)) throw new ArgumentException("Investment risk is required.");

            InvestmentId = investmentId;
            Name = name;
            Type = type;
            Risk = risk;
            CreatedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public string GetInvestmentSummary()
        {
            return $"{Name} ({Type}) - Risco: {Risk}";
        }

        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) && !string.IsNullOrWhiteSpace(Type) && !string.IsNullOrWhiteSpace(Risk);
        }
    }
}