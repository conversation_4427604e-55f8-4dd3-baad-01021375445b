namespace api_data_presentation.Models.Entities
{
    public class Client
    {
        public int ClientId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string Email { get; set; } = string.Empty;
        public string RiskProfileWallet { get; set; } = string.Empty;
        public string RiskProfileForm { get; set; } = string.Empty;
        public bool Compliance { get; set; }
        public int AdvisorId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}