using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Pati.ClassificationService.Application.Services;
using Pati.ClassificationService.Controllers;
using Pati.ClassificationService.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Pati.ClassificationService.Tests
{
    public class ClassificationControllerTests
    {
        private readonly Mock<IClassificationService> _mockClassificationService;
        private readonly Mock<ILogger<ClassificationController>> _mockLogger;
        private readonly ClassificationController _controller;

        public ClassificationControllerTests()
        {
            _mockClassificationService = new Mock<IClassificationService>();
            _mockLogger = new Mock<ILogger<ClassificationController>>();
            _controller = new ClassificationController(_mockClassificationService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task ClassifyPortfolio_ValidInput_ReturnsOkResult()
        {
            // Arrange
            var portfolioInput = new PortfolioInputDto
            {
                AccountId = 1,
                PortfolioId = "test-portfolio",
                ClientId = 1,
                UserId = 1,
                Name = "Test Client",
                Email = "<EMAIL>",
                PhoneNumber = "*********",
                RiskProfileForm = "Moderado",
                AdvisorId = 1,
                PortfolioProfile = "Moderado",
                Assets = new List<AssetInputDto>
                {
                    new AssetInputDto
                    {
                        AssetId = "asset1",
                        Name = "Test Asset",
                        Type = "Test Type",
                        IncomeType = "Renda Fixa",
                        InvestmentProfile = "Conservador",
                        Quantity = 100,
                        Value = 1000
                    }
                },
                Investments = new List<PortfolioInvestmentDto>
                {
                    new PortfolioInvestmentDto
                    {
                        InvestmentId = 1,
                        Amount = 1000,
                        Type = "Test Investment",
                        Name = "Test Investment Name"
                    }
                }
            };

            var expectedOutput = new PortfolioOutputDto
            {
                AccountId = 1,
                PortfolioId = "test-portfolio",
                PortfolioProfile = "Moderado",
                Assets = new List<AssetOutputDto>(),
                Inconsistencies = new List<InconsistencyDto>(),
                IncomeAllocation = new IncomeAllocationDto { FixedIncome = 0.8m, VariableIncome = 0.2m }
            };

            _mockClassificationService
                .Setup(s => s.ClassifyPortfolioAsync(It.IsAny<PortfolioInputDto>()))
                .ReturnsAsync(expectedOutput);

            // Act
            var result = await _controller.ClassifyPortfolio(portfolioInput);

            // Assert
            var okResult = Assert.IsType<ActionResult<PortfolioOutputDto>>(result);
            var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
            var returnValue = Assert.IsType<PortfolioOutputDto>(okObjectResult.Value);
            Assert.Equal(expectedOutput.PortfolioId, returnValue.PortfolioId);
        }

        [Fact]
        public async Task GetPortfolioStatus_ValidId_ReturnsOkResult()
        {
            // Arrange
            var portfolioId = "test-portfolio";
            var expectedStatus = new { portfolioId = portfolioId, status = "active" };

            _mockClassificationService
                .Setup(s => s.GetPortfolioStatusAsync(portfolioId))
                .ReturnsAsync(expectedStatus);

            // Act
            var result = await _controller.GetPortfolioStatus(portfolioId);

            // Assert
            var okResult = Assert.IsType<ActionResult<object>>(result);
            var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
            Assert.NotNull(okObjectResult.Value);
        }

        [Fact]
        public async Task GetPortfolioDetails_ValidId_ReturnsOkResult()
        {
            // Arrange
            var portfolioId = "test-portfolio";
            var expectedDetails = new PortfolioOutputDto
            {
                PortfolioId = portfolioId,
                AccountId = 1,
                PortfolioProfile = "Moderado"
            };

            _mockClassificationService
                .Setup(s => s.GetPortfolioDetailsAsync(portfolioId))
                .ReturnsAsync(expectedDetails);

            // Act
            var result = await _controller.GetPortfolioDetails(portfolioId);

            // Assert
            var okResult = Assert.IsType<ActionResult<PortfolioOutputDto>>(result);
            var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
            var returnValue = Assert.IsType<PortfolioOutputDto>(okObjectResult.Value);
            Assert.Equal(portfolioId, returnValue.PortfolioId);
        }

        [Fact]
        public async Task GetNonCompliantClients_ReturnsOkResult()
        {
            // Arrange
            var expectedClients = new List<object>
            {
                new { clientId = 1, name = "Client A" },
                new { clientId = 2, name = "Client B" }
            };

            _mockClassificationService
                .Setup(s => s.GetNonCompliantClientsAsync())
                .ReturnsAsync(expectedClients);

            // Act
            var result = await _controller.GetNonCompliantClients();

            // Assert
            var okResult = Assert.IsType<ActionResult<IEnumerable<object>>>(result);
            var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
            Assert.NotNull(okObjectResult.Value);
        }

        [Fact]
        public async Task GetDashboard_ReturnsOkResult()
        {
            // Arrange
            var expectedDashboard = new { totalPortfolios = 100, compliantPortfolios = 90 };

            _mockClassificationService
                .Setup(s => s.GetDashboardDataAsync())
                .ReturnsAsync(expectedDashboard);

            // Act
            var result = await _controller.GetDashboard();

            // Assert
            var okResult = Assert.IsType<ActionResult<object>>(result);
            var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
            Assert.NotNull(okObjectResult.Value);
        }

        [Fact]
        public async Task ClassifyAllPortfolios_ValidRequest_ReturnsOkResult()
        {
            // Arrange
            _mockClassificationService.Setup(s => s.ClassifyAllPortfoliosAsync())
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.ClassifyAllPortfolios();

            // Assert
            var okResult = Assert.IsType<ActionResult<object>>(result);
            var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
            Assert.NotNull(okObjectResult.Value);
        }

        [Fact]
        public async Task ClassifyAllPortfolios_ServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            _mockClassificationService.Setup(s => s.ClassifyAllPortfoliosAsync())
                .ThrowsAsync(new Exception("Batch processing failed"));

            // Act
            var result = await _controller.ClassifyAllPortfolios();

            // Assert
            var okResult = Assert.IsType<ActionResult<object>>(result);
            var statusCodeResult = Assert.IsType<ObjectResult>(okResult.Result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        [Fact]
        public async Task ClassifyPortfolio_ServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var portfolioInput = new PortfolioInputDto
            {
                AccountId = 1,
                PortfolioId = "test-portfolio",
                PortfolioProfile = "Moderado",
                Assets = new List<AssetInputDto>
                {
                    new AssetInputDto
                    {
                        AssetId = "asset1",
                        Name = "Test Asset",
                        Type = "bond",
                        IncomeType = "Renda Fixa",
                        InvestmentProfile = "Conservador",
                        Quantity = 100,
                        Value = 1000
                    }
                }
            };

            _mockClassificationService.Setup(s => s.ClassifyPortfolioAsync(It.IsAny<PortfolioInputDto>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.ClassifyPortfolio(portfolioInput);

            // Assert
            var okResult = Assert.IsType<ActionResult<PortfolioOutputDto>>(result);
            var statusCodeResult = Assert.IsType<ObjectResult>(okResult.Result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        [Fact]
        public async Task ClassifyPortfolio_InvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            var portfolioInput = new PortfolioInputDto(); // Invalid input
            _controller.ModelState.AddModelError("AccountId", "AccountId is required");

            // Act
            var result = await _controller.ClassifyPortfolio(portfolioInput);

            // Assert
            var okResult = Assert.IsType<ActionResult<PortfolioOutputDto>>(result);
            Assert.IsType<BadRequestObjectResult>(okResult.Result);
        }

        [Fact]
        public void GetHealth_ReturnsOkResult()
        {
            // Act
            var result = _controller.GetHealth();

            // Assert
            var okResult = Assert.IsType<ActionResult<object>>(result);
            var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
            Assert.NotNull(okObjectResult.Value);
        }

        [Fact]
        public void ValidateRiskForProfile_ValidParameters_ReturnsOkResult()
        {
            // Arrange
            var profile = "Moderado";
            var risk = "Conservador";

            _mockClassificationService
                .Setup(s => s.IsRiskAllowedForProfile(profile, risk))
                .Returns(true);

            // Act
            var result = _controller.ValidateRiskForProfile(profile, risk);

            // Assert
            var okResult = Assert.IsType<ActionResult<bool>>(result);
            var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
            Assert.True((bool)okObjectResult.Value!);
        }

        [Fact]
        public void GetIdealProportions_ValidProfile_ReturnsOkResult()
        {
            // Arrange
            var profile = "Moderado";
            var expectedProportions = (FixedIncome: 0.6m, VariableIncome: 0.4m);

            _mockClassificationService
                .Setup(s => s.GetIdealProportions(profile))
                .Returns(expectedProportions);

            // Act
            var result = _controller.GetIdealProportions(profile);

            // Assert
            var okResult = Assert.IsType<ActionResult<IncomeAllocationDto>>(result);
            var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
            var returnValue = Assert.IsType<IncomeAllocationDto>(okObjectResult.Value);
            Assert.Equal(0.6m, returnValue.FixedIncome);
            Assert.Equal(0.4m, returnValue.VariableIncome);
        }

        [Fact]
        public void GetHealth_ReturnsOkResult()
        {
            // Act
            var result = _controller.GetHealth();

            // Assert
            var okResult = Assert.IsType<ActionResult<object>>(result);
            var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
            Assert.NotNull(okObjectResult.Value);
        }
    }
}
