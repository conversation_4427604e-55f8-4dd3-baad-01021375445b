using Microsoft.ML.Data;

namespace Pati.ClassificationService.Infrastructure.Models
{
    /// <summary>
    /// Classe para dados de entrada do modelo ML.NET
    /// </summary>
    public class PortfolioData
    {
        [LoadColumn(0)]
        public int ClientId { get; set; }

        [LoadColumn(1)]
        public string PortfolioProfile { get; set; } = string.Empty;

        [LoadColumn(2)]
        public float TotalValue { get; set; }

        [LoadColumn(3)]
        public float FixedIncomeRatio { get; set; }

        [LoadColumn(4)]
        public float VariableIncomeRatio { get; set; }

        [LoadColumn(5)]
        public int AssetCount { get; set; }

        [LoadColumn(6)]
        public float RiskScore { get; set; }
    }

    /// <summary>
    /// Classe para predição do perfil de risco
    /// </summary>
    public class RiskProfilePrediction
    {
        [ColumnName("PredictedLabel")]
        public string RiskProfile { get; set; } = string.Empty;

        [ColumnName("Score")]
        public float[] Score { get; set; } = Array.Empty<float>();

        public float Confidence => Score?.Max() ?? 0f;
    }

    /// <summary>
    /// Dados de um ativo para o modelo ML.NET
    /// </summary>
    public class AssetData
    {
        public string AssetId { get; set; } = string.Empty;
        public float Quantity { get; set; }
        public float Value { get; set; }
        public string Type { get; set; } = string.Empty;
        public string IncomeType { get; set; } = string.Empty;
        public string InvestmentProfile { get; set; } = string.Empty;
    }
}
