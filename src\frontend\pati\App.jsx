import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { LogBox } from 'react-native';
import { AuthProvider, useAuth } from './src/contexts/AuthContext';

// Suprimir warnings específicos
LogBox.ignoreLogs([
  'Text strings must be rendered within a <Text> component',
  'Warning: Text strings must be rendered within a <Text> component',
]);

// Ou para suprimir todos os warnings (não recomendado para produção)
// LogBox.ignoreAllLogs();
import LoginPage from './src/components/login/LoginPage';
import RegisterPage from './src/components/register/RegisterPage';
import Homepage from './src/screens/homepage';
import ClientPage from './src/screens/clientpage';
import LoadingScreen from './src/components/common/LoadingScreen';

const Stack = createStackNavigator();

function AppNavigator() {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator 
        initialRouteName={isAuthenticated ? "Homepage" : "Login"}
        screenOptions={{ headerShown: false }}
      >
        {/* All screens are always available, navigation is controlled by auth state */}
        <Stack.Screen name="Login" component={LoginPage} />
        <Stack.Screen name="Register" component={RegisterPage} />
        <Stack.Screen name="Homepage" component={Homepage} />
        <Stack.Screen name="ClientPage" component={ClientPage} />
      </Stack.Navigator>
      <StatusBar style="auto" />
    </NavigationContainer>
  );
}

export default function App() {
  return (
    <AuthProvider>
      <AppNavigator />
    </AuthProvider>
  );
}
