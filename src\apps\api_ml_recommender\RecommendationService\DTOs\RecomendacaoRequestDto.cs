using System.ComponentModel.DataAnnotations;

namespace ApiMlRecommender.DTOs
{
    public class RecomendacaoRequestDto
    {
        [Required(ErrorMessage = "CPF é obrigatório")]
        [StringLength(11, MinimumLength = 11, ErrorMessage = "CPF deve ter 11 dígitos")]
        public string Cpf { get; set; } = string.Empty;

        [Required(ErrorMessage = "Nome é obrigatório")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Nome deve ter entre 2 e 100 caracteres")]
        public string Nome { get; set; } = string.Empty;

        [Required(ErrorMessage = "Perfil de risco é obrigatório")]
        public string PerfilRisco { get; set; } = string.Empty;

        [Required(ErrorMessage = "Lista de investimentos é obrigatória")]
        [MinLength(1, ErrorMessage = "Cliente deve ter pelo menos 1 investimento")]
        public List<InvestimentoDto> Investimentos { get; set; } = new List<InvestimentoDto>();

        public decimal? ValorDisponivel { get; set; }

        public string? PerfilInvestimentos { get; set; }

        public string ComplianceStatus { get; set; } = string.Empty;
    }
}