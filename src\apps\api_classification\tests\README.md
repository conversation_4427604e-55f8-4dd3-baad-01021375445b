# Pati.ClassificationService - Documentação de Testes

## Visão Geral

Este diretório contém todos os testes unitários do **Pati.ClassificationService**. Os testes são organizados em 4 arquivos principais que cobrem diferentes aspectos do sistema, desde a camada de apresentação até as regras de negócio do domínio.

## Estrutura de Testes

### Métricas Gerais

- **Total de Testes**: 57 testes
- **Taxa de Sucesso**: 100%
- **Cobertura de Código**: >80%
- **Tempo de Execução**: ~2-3 segundos
- **Framework**: xUnit + Moq

## Arquivos de Teste

### 1. ClassificationControllerTests.cs

**Propósito**: Testa a camada de apresentação (Controllers) e validações de entrada da API.

**Cenários Cobertos**:
- ✅ Validação de DTOs de entrada
- ✅ Respostas HTTP corretas (200, 400, 500)
- ✅ Tratamento de erros e exceções
- ✅ Integração com serviços de aplicação
- ✅ Serialização/deserialização JSON
- ✅ Validação de Model State

**Principais Testes**:
```csharp
[Fact]
public async Task ClassifyPortfolio_ValidInput_ReturnsOkResult()

[Fact]
public async Task ClassifyPortfolio_InvalidInput_ReturnsBadRequest()

[Fact]
public async Task GetHealth_ReturnsHealthStatus()

[Fact]
public async Task RiskValidation_ValidParameters_ReturnsBoolean()
```

### 2. ClassificationServiceCoverageTests.cs

**Propósito**: Testa a lógica de negócio do serviço principal de classificação.

**Cenários Cobertos**:
- ✅ Classificação de carteiras por perfil (Conservador, Moderado, Arrojado, Sofisticado)
- ✅ Identificação de inconformidades baseada em regras CVM 175
- ✅ Cálculo de proporções de renda (Fixa vs Variável)
- ✅ Integração com modelo SVD (simulado)
- ✅ Mapeamento entre DTOs e entidades de domínio
- ✅ Validação de regras de negócio

**Principais Testes**:
```csharp
[Fact]
public async Task ClassifyPortfolio_ConservativeProfile_IdentifiesInconsistencies()

[Fact]
public async Task ClassifyPortfolio_ModerateProfile_AllowsCompatibleAssets()

[Fact]
public async Task CalculateProportions_ReturnsCorrectAllocation()

[Fact]
public async Task SVDAnalysis_IntegratesCorrectly()
```

### 3. DtoAndDomainValidationTests.cs

**Propósito**: Testa validações de DTOs e entidades de domínio.

**Cenários Cobertos**:
- ✅ Validações de campos obrigatórios
- ✅ Validações de formato e tamanho de strings
- ✅ Validações de ranges numéricos
- ✅ Regras de negócio em entidades
- ✅ Mapeamentos AutoMapper
- ✅ Serialização JSON com JsonPropertyName

**Principais Testes**:
```csharp
[Theory]
[InlineData("", false)] // AccountId obrigatório
[InlineData("12345", true)]
public void PortfolioInputDto_AccountIdValidation(string accountId, bool isValid)

[Fact]
public void AssetInputDto_RequiredFields_ValidationWorks()

[Fact]
public void AutoMapper_MappingConfiguration_IsValid()

[Fact]
public void JsonSerialization_PropertyNames_AreCorrect()
```

### 4. PortfolioTests.cs

**Propósito**: Testa a entidade Portfolio e suas regras de negócio específicas.

**Cenários Cobertos**:
- ✅ Função `risco_permitido` (implementação das regras CVM 175)
- ✅ Cálculo de proporções ideais por perfil
- ✅ Identificação de inconformidades na carteira
- ✅ Validações de perfil de carteira
- ✅ Criação e atualização de portfolios
- ✅ Cálculo de severidade de inconformidades

**Principais Testes**:
```csharp
[Theory]
[InlineData("Conservador", "Conservador", true)]
[InlineData("Conservador", "Moderado", false)]
[InlineData("Moderado", "Conservador", true)]
[InlineData("Moderado", "Moderado", true)]
[InlineData("Arrojado", "Arrojado", true)]
public void IsRiskAllowedForProfile_ValidatesCorrectly(string profile, string risk, bool expected)

[Theory]
[InlineData("Conservador", 0.8, 0.2)]
[InlineData("Moderado", 0.6, 0.4)]
[InlineData("Arrojado", 0.3, 0.7)]
public void GetIdealProportions_ReturnsCorrectValues(string profile, decimal expectedFixed, decimal expectedVariable)

[Fact]
public void IdentifyInconsistencies_DetectsProfileMismatch()

[Fact]
public void CalculateSeverity_ReturnsCorrectLevel()
```

## Configuração de Testes

### Dependências

```xml
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
<PackageReference Include="xunit" Version="2.6.1" />
<PackageReference Include="xunit.runner.visualstudio" Version="2.5.3" />
<PackageReference Include="Moq" Version="4.20.69" />
<PackageReference Include="AutoMapper" Version="12.0.1" />
<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.0" />
```

### Mocking e Fixtures

Os testes utilizam **Moq** para criar mocks de dependências:

```csharp
// Mock do IClassificationService
var mockClassificationService = new Mock<IClassificationService>();
mockClassificationService
    .Setup(s => s.ClassifyPortfolioAsync(It.IsAny<PortfolioInputDto>()))
    .ReturnsAsync(expectedResult);

// Mock do ISVDModelService
var mockSVDService = new Mock<ISVDModelService>();
mockSVDService
    .Setup(s => s.IsModelLoaded)
    .Returns(true);

// Mock do ILogger
var mockLogger = new Mock<ILogger<ClassificationController>>();
```

### Dados de Teste

Os testes utilizam dados baseados no arquivo `inconformidades.csv`:

```csharp
public static PortfolioInputDto CreateValidPortfolioInput()
{
    return new PortfolioInputDto
    {
        AccountId = 27,
        PortfolioId = "portfolio_27",
        PortfolioProfile = "Conservador",
        Assets = new List<AssetInputDto>
        {
            new AssetInputDto
            {
                AssetId = "IGTI11_001",
                Name = "IGTI11",
                Type = "ACOES BRASIL",
                IncomeType = "Renda Variável",
                InvestmentProfile = "Arrojado",
                Quantity = 6800,
                Value = 132396
            }
        }
    };
}
```

## Execução de Testes

### Comandos Básicos

```bash
# Executar todos os testes
dotnet test

# Executar testes com output detalhado
dotnet test --logger "console;verbosity=detailed"

# Executar testes específicos
dotnet test --filter "ClassificationServiceTests"
dotnet test --filter "PortfolioTests"

# Executar com cobertura de código
dotnet test --collect:"XPlat Code Coverage"
```

### Execução por Categoria

```bash
# Testes de Controller
dotnet test --filter "ClassName~ClassificationControllerTests"

# Testes de Serviço
dotnet test --filter "ClassName~ClassificationServiceCoverageTests"

# Testes de Domínio
dotnet test --filter "ClassName~PortfolioTests"

# Testes de Validação
dotnet test --filter "ClassName~DtoAndDomainValidationTests"
```

## Padrões de Teste

### Nomenclatura

Os testes seguem o padrão **AAA (Arrange, Act, Assert)**:

```csharp
[Fact]
public async Task MethodName_Scenario_ExpectedResult()
{
    // Arrange
    var input = CreateTestInput();
    var expectedResult = CreateExpectedResult();
    
    // Act
    var result = await _service.MethodName(input);
    
    // Assert
    Assert.Equal(expectedResult, result);
}
```

### Testes Parametrizados

Uso extensivo de `[Theory]` e `[InlineData]` para testes parametrizados:

```csharp
[Theory]
[InlineData("Conservador", "Conservador", true)]
[InlineData("Conservador", "Moderado", false)]
[InlineData("Moderado", "Conservador", true)]
public void TestMethod_WithDifferentInputs(string input1, string input2, bool expected)
{
    // Test implementation
}
```

## Adicionando Novos Testes

### Estrutura Recomendada

1. **Organize por funcionalidade**: Agrupe testes relacionados
2. **Use nomes descritivos**: `MethodName_Scenario_ExpectedResult`
3. **Implemente AAA pattern**: Arrange, Act, Assert
4. **Mock dependências**: Use Moq para isolar unidades de teste
5. **Teste casos extremos**: Valores nulos, vazios, inválidos

### Exemplo de Novo Teste

```csharp
[Fact]
public async Task NewMethod_ValidInput_ReturnsExpectedResult()
{
    // Arrange
    var mockService = new Mock<IDependencyService>();
    mockService.Setup(s => s.Method()).ReturnsAsync(expectedValue);
    
    var service = new ServiceUnderTest(mockService.Object);
    var input = CreateValidInput();
    
    // Act
    var result = await service.NewMethod(input);
    
    // Assert
    Assert.NotNull(result);
    Assert.Equal(expectedValue, result.Property);
    mockService.Verify(s => s.Method(), Times.Once);
}
```

## Troubleshooting

### Problemas Comuns

1. **Testes falhando por dependências**: Verifique se todos os mocks estão configurados
2. **Problemas de serialização**: Verifique JsonPropertyName nos DTOs
3. **Testes lentos**: Considere usar mocks em vez de dependências reais
4. **Falhas intermitentes**: Verifique se há dependências de estado compartilhado

### Logs de Teste

Para debug, use `ITestOutputHelper`:

```csharp
public class TestClass
{
    private readonly ITestOutputHelper _output;
    
    public TestClass(ITestOutputHelper output)
    {
        _output = output;
    }
    
    [Fact]
    public void TestMethod()
    {
        _output.WriteLine("Debug information");
        // Test implementation
    }
}
```

## Contribuição

Ao adicionar novos testes:

1. Mantenha a cobertura acima de 80%
2. Siga os padrões de nomenclatura estabelecidos
3. Documente cenários complexos
4. Atualize este README se necessário
5. Execute todos os testes antes de fazer commit
