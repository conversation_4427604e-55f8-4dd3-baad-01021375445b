<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="./docs/assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>

# Nome do Projeto: Pati (Plataforma de Adequação de Tipo de Investidor)

## Sumário
- [🚀 Quick Start](#-quick-start)
- [👥 Equipe](#-equipe)
- [📝 Descrição](#-descrição)
- [👨‍🏫 Professores](#professores)
- [📁 Estrutura de Pastas](#-estrutura-de-pastas)
- [💻 Configuração para Desenvolvimento](#-configuração-para-desenvolvimento)
- [📋 Documentação](#-documentação)
- [🗃 Histórico de Lançamentos](#-histórico-de-lançamentos)
- [📋 Licença](#-licença)

## 🚀 Quick Start

```bash
# Clone o repositório
git clone https://github.com/Inteli-College/2025-1B-T13-ES06-G02.git
cd 2025-1B-T13-ES06-G02

# Configure as variáveis de ambiente
cp .env.example .env
# Edite o arquivo .env com suas configurações

# Execute o ambiente completo
cd src
docker-compose up -d

# Verifique se está funcionando
curl http://localhost:3000/health
```

## 👥 Equipe

### Nome do Grupo: MMs (Meninas Malvadas)

### Integrantes:

<div align="center">
<table>
  <tr>
    <td align="center">
      <a href="https://www.linkedin.com/in/larissa-temoteo/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQE-yMLLUD04Qg/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1730056625827?e=**********&v=beta&t=Vxukmk-nWK9EjbZGD4zQ0IIi5se0JwECJLmyqZ-2mrg" width="100px;" alt="Foto de Larissa Temoteo" style="border-radius:50%"/>
        <br />
        <b>Larissa Temoteo</b>
      </a>
      <br />
      <a href="https://github.com/larissatemoteo">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/larissa-temoteo/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/lucas-nunes-matheus/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHN4SR2WsAIdA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1710163486566?e=**********&v=beta&t=o-9q_kscwkEexlcm92Cobx197j0MsiztrpiTQgiJ9Kg" width="100px;" alt="Foto de Lucas Matheus Nunes" style="border-radius:50%"/>
        <br />
        <b>Lucas Matheus Nunes</b>
      </a>
      <br />
      <a href="https://github.com/lucas-nunes-matheus">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/lucas-nunes-matheus/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/rafael-furtado-b30715265/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQGfsjSmMmtAsw/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1709258479259?e=**********&v=beta&t=oIehlqkG2dGqrV8ya_JukCvuBgTEs-q7i32Oen49fdQ" width="100px;" alt="Foto de Rafael Furtado" style="border-radius:50%"/>
        <br />
        <b>Rafael Furtado</b>
      </a>
      <br />
      <a href="https://github.com/Rafaelfurtadovs">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/rafael-furtado-b30715265/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
  <tr>
    <td align="center">
      <a href="https://www.linkedin.com/in/ryan-botelho-gartlan/">
        <img src="https://media.licdn.com/dms/image/v2/D5603AQGy5KTEKUM2pA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1728396516687?e=**********&v=beta&t=LjSCsFve87n2F4J7v-LzbwHHytG4SJnTxTigdBhVlUU" width="100px;" alt="Foto de Ryan Gartlan" style="border-radius:50%"/>
        <br />
        <b>Ryan Gartlan</b>
      </a>
      <br />
      <a href="https://github.com/ryanbotgar">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/ryan-botelho-gartlan/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/tainacortez/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHV4IOZvu7n3A/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1713277689597?e=**********&v=beta&t=fgvCFEght43z8dmT0PhJgj1Lg5VtXoCjZie0pNPujxA" width="100px;" alt="Foto de Tainá Cortez" style="border-radius:50%"/>
        <br />
        <b>Tainá Cortez</b>
      </a>
      <br />
      <a href="https://github.com/taicortezz">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/tainacortez/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/thiagogomesalmeida/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHh3rHCD36uKA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1711828725384?e=**********&v=beta&t=Kggq5QNqIQ66GqL7_dT37fq5YO3NQAGBwX9BF0Fq8oU" width="100px;" alt="Foto de Thiago Gomes" style="border-radius:50%"/>
        <br />
        <b>Thiago Gomes</b>
      </a>
      <br />
      <a href="https://github.com/thiagomes07">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/thiagogomesalmeida/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
  <tr>
    <td colspan="3" align="center">
      <a href="https://www.linkedin.com/in/viniciussavian/">
        <img src="https://media.licdn.com/dms/image/v2/D4E03AQFsD6PLB2Du0w/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1710417108475?e=**********&v=beta&t=Qbzx-PMJMrTRJlEtE_NYRwVfMfOyY7Nf-duVxKugTmk" width="100px;" alt="Foto de Vinicius Savian" style="border-radius:50%"/>
        <br />
        <b>Vinicius Savian</b>
      </a>
      <br />
      <a href="https://github.com/ViniciusSavian">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/viniciussavian/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
</table>
</div>

## Professores:

### Orientador(a)

- <a href="https://www.linkedin.com/in/vanunes/">Vanessa Nunes</a>

### Instrutores

- <a href="https://www.linkedin.com/in/ovidio-netto/">Computação - Ovidio Netto</a>
- <a href="https://www.linkedin.com/in/afonsolelis/">Computação - Afonso Brandão</a>
- <a href="https://www.linkedin.com/in/luciano-galdino-26191b36/">Matemática e Física - Luciano Galdino</a>
- <a href="https://www.linkedin.com/in/rafael-jacomossi-6135b0a1/">Negócios - Rafael Jacomossi</a>
- <a href="https://www.linkedin.com/in/bruna-mayer/">Design - Bruna Mayer</a>
- <a href="https://www.linkedin.com/in/filipe-gonçalves-08a55015b/">Liderança - Filipe Gonçalves</a>

## 📝 Descrição

O projeto PATI (Plataforma de Adequação de Tipo de Investidor) é uma solução desenvolvida para o BTG Pactual que automatiza a verificação de conformidade entre o perfil de risco declarado pelos clientes e suas carteiras de investimento, conforme a Resolução CVM 175. A plataforma oferece aos assessores de investimento uma ferramenta para identificar rapidamente clientes em inconformidade e gerar recomendações personalizadas de adequação de carteira.

## 📋 Documentação

- [ Manual de Instalação](/docs/installation_manual.md) - Instruções detalhadas para configurar e executar o projeto
- [ Documentação Geral](/docs/index.md) - Visão geral da documentação do projeto
- [ Documentação do Banco de Dados](/src/database/index.md) - Dicionário de dados e procedimentos
- [ Padrões de Desenvolvimento](/docs/standards/development.md) - Guia de boas práticas e padrões

## 📁 Estrutura de pastas

|--> documentos<br>
&emsp;| --> outros <br>
&emsp;| documento<br>
&emsp;| documento<br>
|--> imagens<br>
|--> src<br>
&emsp;|--> dir<br>
&emsp;|--> dir<br>
&emsp;|--> dir<br>
&emsp;|--> dir<br>
&emsp;|--> dir<br>
&emsp;|--> dir<br>
| readme.md<br>

Dentre os arquivos presentes na raiz do projeto, definem-se:

- <b>readme.md</b>: arquivo que serve como guia e explicação geral sobre o projeto (o mesmo que você está lendo agora).

- <b>documentos</b>: aqui estarão todos os documentos do projeto. Há também uma pasta denominada <b>outros</b> onde podem estar presentes documentos complementares à documentação principal.

- <b>imagens</b>: imagens relacionadas ao projeto como um todo (por exemplo imagens do sistema, do grupo, logotipos e afins).

- <b>src</b>: nesta pasta encontra-se todo o código fonte do sistema (circuito e eventuais sistemas complementares).

## 💻 Configuração para desenvolvimento

Para configurar o desenvolvimento da aplicação, siga os passos abaixo:

### Pré-requisitos
- [Git](https://git-scm.com/downloads)
- [Docker](https://www.docker.com/get-started)
- [PostgreSQL 16](https://www.postgresql.org/download/) (ou Docker)
- [.NET 8.0 SDK](https://dotnet.microsoft.com/download)
- [Node.js 18+](https://nodejs.org/) (para o frontend)

### Clonagem do Repositório
```bash
git clone https://github.com/Inteli-College/2025-1B-T13-ES06-G02.git
cd 2025-1B-T13-ES06-G02
```

### Configuração do Banco de Dados

#### Opção 1: Usando Docker (Recomendado)
```bash
# Navegar para o diretório src
cd src

# Executar o ambiente completo com Docker Compose
docker-compose up -d

# O banco será criado automaticamente com os dados iniciais
```

#### Opção 2: PostgreSQL Local
```bash
# 1. Criar o banco de dados
createdb pati_db

# 2. Executar scripts de criação
psql -d pati_db -f src/database/01_create_database.sql

# 3. Inserir dados iniciais
psql -d pati_db -f src/database/02_initial_data.sql

# 4. Verificar instalação
psql -d pati_db -c "SELECT COUNT(*) FROM client;"
```

### Configuração das Variáveis de Ambiente
```bash
# Copiar arquivo de exemplo
cp .env.example .env

# Editar variáveis conforme seu ambiente
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=pati_db
# DB_USER=postgres
# DB_PASS=sua_senha
```

### Fluxo de Execução dos Scripts de Banco
1. **01_create_database.sql**: Cria estrutura de tabelas, índices e funções
2. **02_initial_data.sql**: Popula tabelas com dados de teste
3. **Validação**: Executa queries de verificação de integridade

### Dependências de Infraestrutura
- **PostgreSQL 16**: Banco de dados principal
- **Docker**: Containerização dos serviços
- **pgAdmin** (opcional): Interface gráfica para administração do banco

Para instruções detalhadas, consulte nosso <a href="/docs/installation_manual.md">manual de instalação</a>.

## 🗃 Histórico de lançamentos

- 0.1.0 - DD/MM/2024
  - Artefato
  - Artefato
- 0.2.0 - DD/MM/2024
  - Artefato
  - Artefato
- 0.3.0 - DD/MM/2024
  - Artefato
  - Artefato
- 0.4.0 - DD/MM/2024
  - Artefato
  - Artefato
- 0.5.0 - DD/MM/2024
  - Artefato
  - Artefato

## 📋 Licença/License

<p xmlns:cc="http://creativecommons.org/ns#" xmlns:dct="http://purl.org/dc/terms/"><a property="dct:title" rel="cc:attributionURL" href="https://github.com/Inteli-College/2025-1B-T13-ES06-G02">Pati (Plataforma de Adequação de Tipo de Investidor)</a> by <a rel="cc:attributionURL dct:creator" property="cc:attributionName" href="https://github.com/Inteli-College/2025-1B-T13-ES06-G02">Inteli, Larissa dos Santos Temoteo, Lucas Matheus Nunes, Rafael Furtado Victor dos Santos, Ryan Botelho Gartlan, Tainá de Paiva Cortez, Thiago Gomes de Almeida e Vinicius dos Reis Savian</a> is licensed under <a href="https://creativecommons.org/licenses/by/4.0/?ref=chooser-v1" target="_blank" rel="license noopener noreferrer" style="display:inline-block;">Creative Commons Attribution 4.0 International<img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/cc.svg?ref=chooser-v1" alt=""><img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/by.svg?ref=chooser-v1" alt=""></a></p>
