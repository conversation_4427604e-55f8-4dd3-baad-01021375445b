import { registerRootComponent } from 'expo';
import { LogBox } from 'react-native';

// Suprimir warnings específicos globalmente
LogBox.ignoreLogs([
  'Text strings must be rendered within a <Text> component',
  'Warning: Text strings must be rendered within a <Text> component',
]);

// Suprimir warnings do console durante desenvolvimento
if (__DEV__) {
  const originalWarn = console.warn;
  console.warn = (...args) => {
    if (
      args[0] &&
      typeof args[0] === 'string' &&
      args[0].includes('Text strings must be rendered within a <Text> component')
    ) {
      return;
    }
    originalWarn(...args);
  };
}

import App from './App';
//
// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
