using System.Net.Http;
using System.Threading.Tasks;
using TechTalk.SpecFlow;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Newtonsoft.Json;
using System.Text;
using System.Collections.Generic;

[Binding]
public class RecommendationSteps
{
    private readonly CustomWebApplicationFactory _factory;
    private HttpClient _client;
    private HttpResponseMessage _response;
    private string _cpf;
    private string _nome;
    private string _perfilRisco;
    private List<object> _investimentos;

    public RecommendationSteps()
    {
        _factory = new CustomWebApplicationFactory();
        _client = _factory.CreateClient();
    }

    [Given(@"o banco de dados está limpo e contém um cliente inconforme com CPF ""(.*)"" e investimentos válidos")]
    public async Task GivenBancoLimpoComClienteInconforme(string cpf)
    {
        _cpf = cpf;
        _nome = "Cliente Teste";
        _perfilRisco = "MODERADO";
        _investimentos = new List<object>
        {
            new {
                Codigo = "INV001",
                Nome = "CDB Banco X",
                Categoria = "RENDA FIXA",
                Subcategoria = "CDB",
                TipoAtivo = "CDB",
                NivelRisco = 3,
                ValorAtual = 5000.00M,
                PercentualCarteira = 50.0M,
                RentabilidadeEsperada = 0.12M
            },
            new {
                Codigo = "INV002",
                Nome = "Ação Y",
                Categoria = "RENDA VARIÁVEL",
                Subcategoria = "Ação",
                TipoAtivo = "Ação",
                NivelRisco = 4,
                ValorAtual = 5000.00M,
                PercentualCarteira = 50.0M,
                RentabilidadeEsperada = 0.18M
            }
        };
        // Aqui você pode garantir que o banco está no estado esperado, se necessário.
        await Task.CompletedTask;
    }

    [When(@"eu solicito a geração de recomendação para o cliente com CPF ""(.*)""")]
    public async Task WhenSolicitoGeracaoRecomendacao(string cpf)
    {
        var request = new {
            Cpf = _cpf,
            Nome = _nome,
            PerfilRisco = _perfilRisco,
            Investimentos = _investimentos
        };
        var content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        _response = await _client.PostAsync("/api/recomendacao/gerar", content);
    }

    [Then(@"a recomendação deve ser retornada com sucesso e conter sugestões baseadas nos dados do banco")]
    public async Task ThenRecomendacaoRetornadaComSucesso()
    {
        _response.EnsureSuccessStatusCode();
        var responseString = await _response.Content.ReadAsStringAsync();
        responseString.Should().Contain(_cpf);
        responseString.Should().Contain(_nome);
        responseString.Should().Contain(_perfilRisco);
    }

    [Then(@"nenhuma falha de conexão com o banco deve ocorrer")]
    public void ThenNenhumaFalhaConexaoBanco()
    {
        _response.IsSuccessStatusCode.Should().BeTrue();
    }
}