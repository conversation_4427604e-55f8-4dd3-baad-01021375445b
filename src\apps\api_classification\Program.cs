using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Pati.ClassificationService.Application.Mappings;
using Pati.ClassificationService.Application.Services;
using Pati.ClassificationService.Infrastructure.Jobs;
using Pati.ClassificationService.Infrastructure.Services;
using System.Text.Json;

var builder = WebApplication.CreateBuilder(args);

// Configure URLs explicitly
var urls = builder.Configuration.GetValue<string>("Urls") ?? "http://localhost:8080";
builder.WebHost.UseUrls(urls);

// Add services to the container.
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.WriteIndented = true;
    });

// Database configuration for PostgreSQL
builder.Services.AddDbContext<Pati.ClassificationService.Infrastructure.Data.ClassificationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("PostgreSQL")));

// AutoMapper configuration
builder.Services.AddAutoMapper(typeof(MappingProfile));

// Application services
builder.Services.AddScoped<IClassificationService, Pati.ClassificationService.Infrastructure.Services.ClassificationService>();

// SVD Model service
builder.Services.Configure<Pati.ClassificationService.Infrastructure.Models.SVDModelOptions>(
    builder.Configuration.GetSection(Pati.ClassificationService.Infrastructure.Models.SVDModelOptions.SectionName));
builder.Services.AddSingleton<ISVDModelService, Pati.ClassificationService.Infrastructure.Services.SVDModelService>();

// HTTP Client for RecommendationService
builder.Services.AddHttpClient("RecommendationService", client =>
{
    client.BaseAddress = new Uri(builder.Configuration.GetValue<string>("RecommendationService:BaseUrl") ?? "http://localhost:5001");
    client.Timeout = TimeSpan.FromSeconds(builder.Configuration.GetValue<int>("RecommendationService:TimeoutSeconds", 30));
});

// RecommendationService client
builder.Services.Configure<Pati.ClassificationService.Infrastructure.Services.RecommendationServiceOptions>(
    builder.Configuration.GetSection(Pati.ClassificationService.Infrastructure.Services.RecommendationServiceOptions.SectionName));
builder.Services.AddScoped<IRecommendationServiceClient, Pati.ClassificationService.Infrastructure.Services.RecommendationServiceClient>();

// Simple Timer-based job configuration (mais simples que Quartz.NET)
builder.Services.Configure<ClassificationJobOptions>(
    builder.Configuration.GetSection(ClassificationJobOptions.SectionName));

builder.Services.AddHostedService<SimpleClassificationJob>();

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "Pati.ClassificationService API",
        Version = "v1.0.0",
        Description = "API for portfolio classification and compliance analysis based on CVM 175 regulations and SVD model integration",
        Contact = new Microsoft.OpenApi.Models.OpenApiContact
        {
            Name = "Pati Development Team",
            Email = "<EMAIL>"
        },
        License = new Microsoft.OpenApi.Models.OpenApiLicense
        {
            Name = "MIT License"
        }
    });

    c.EnableAnnotations();

    // Add XML comments if available
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }

    // Add server information
    c.AddServer(new Microsoft.OpenApi.Models.OpenApiServer
    {
        Url = "http://localhost:8080",
        Description = "Local Development Server"
    });

    c.AddServer(new Microsoft.OpenApi.Models.OpenApiServer
    {
        Url = "http://classification-service:8080",
        Description = "Docker Internal Network"
    });
});

var app = builder.Build();

// Initialize SVD Model
using (var scope = app.Services.CreateScope())
{
    var svdService = scope.ServiceProvider.GetRequiredService<ISVDModelService>();
    try
    {
        await svdService.LoadModelAsync();
    }
    catch (Exception ex)
    {
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
        logger.LogWarning(ex, "Failed to load SVD model. Service will continue without SVD analysis.");
    }
}

// Configure the HTTP request pipeline.
// Enable Swagger in all environments for API documentation
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "ClassificationService API V1");
    c.RoutePrefix = "swagger"; // Acessar via /swagger
    c.DocumentTitle = "API Classification Service";
    c.DefaultModelsExpandDepth(-1); // Collapse models by default
    c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None); // Collapse operations by default

    // Additional configuration for production
    if (!app.Environment.IsDevelopment())
    {
        c.SupportedSubmitMethods(); // Disable "Try it out" in production
    }
});

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

// Health check endpoint
app.MapGet("/health", (IConfiguration config, IWebHostEnvironment env) => Results.Ok(new
{
    status = "healthy",
    timestamp = DateTime.UtcNow,
    service = "Pati.ClassificationService",
    version = "1.0.0",
    environment = env.EnvironmentName,
    baseUrl = config.GetValue<string>("ServiceConfiguration:BaseUrl") ?? "http://localhost:8080",
    urls = config.GetValue<string>("Urls") ?? "http://localhost:8080",
    swagger = new
    {
        enabled = true,
        url = $"{config.GetValue<string>("ServiceConfiguration:BaseUrl") ?? "http://localhost:8080"}/swagger",
        jsonUrl = $"{config.GetValue<string>("ServiceConfiguration:BaseUrl") ?? "http://localhost:8080"}/swagger/v1/swagger.json"
    },
    endpoints = new[]
    {
        "POST /internal/v1/portfolios/classify",
        "POST /internal/v1/portfolios/classify-all",
        "GET /internal/v1/portfolios/{portfolioId}/status",
        "GET /internal/v1/portfolios/{portfolioId}/details",
        "GET /internal/v1/portfolios/non-compliant-clients",
        "GET /internal/v1/portfolios/dashboard",
        "GET /internal/v1/portfolios/health",
        "GET /health",
        "GET /swagger"
    }
}));

app.Run();