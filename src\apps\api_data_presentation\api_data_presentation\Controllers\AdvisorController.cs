using Microsoft.AspNetCore.Mvc;
using api_data_presentation.Models.QueryParameters;
using api_data_presentation.Models.DTOs;
using api_data_presentation.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace api_data_presentation.Controllers
{
    [ApiController]
    [Route("api/advisor")]
    public class AdvisorController : ControllerBase
    {
        private readonly IAdvisorService _advisorService;
        private readonly ILogger<AdvisorController> _logger;

        public AdvisorController(IAdvisorService advisorService, ILogger<AdvisorController> logger)
        {
            _advisorService = advisorService;
            _logger = logger;
        }

        [HttpGet("{advisorId}/compliance-stats")]
        public async Task<IActionResult> GetComplianceStats(int advisorId)
        {
            try
            {
                var stats = await _advisorService.GetComplianceStatsAsync(advisorId);
                return Ok(stats);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception)
            {
                return StatusCode(500, "Erro interno do servidor.");
            }
        }

        [HttpGet("{advisorId}/clients")]
        public async Task<IActionResult> GetClients(
            int advisorId,
            [FromQuery] ClientListQueryParams queryParams)
        {
            try
            {
                var clients = await _advisorService.GetClientsAsync(advisorId, queryParams);
                return Ok(clients);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception)
            {
                return StatusCode(500, "Erro interno do servidor.");
            }
        }

        [HttpGet("clients/{clientId}/investments")]
        public async Task<IActionResult> GetClientInvestments(
            int clientId,
            [FromQuery] InvestmentQueryParams queryParams)
        {
            try
            {
                var investments = await _advisorService.GetClientInvestmentsAsync(clientId, queryParams);
                return Ok(investments);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception)
            {
                return StatusCode(500, "Erro interno do servidor.");
            }
        }

        [HttpGet("clients/{clientId}/recommendations")]
        public async Task<IActionResult> GetClientRecommendations(int clientId)
        {
            try
            {
                var recommendations = await _advisorService.GetClientRecommendationsAsync(clientId);

                if (recommendations == null)
                    return NotFound("Recomendações não encontradas para o cliente especificado.");

                return Ok(recommendations);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar recomendações para o cliente {ClientId}: {Message}", clientId, ex.Message);
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        [HttpPost("auth")]
        public async Task<IActionResult> AuthenticateOrCreateAdvisor([FromBody] AdvisorAuthRequestDto request)
        {
            try
            {
                var response = await _advisorService.AuthenticateOrCreateAsync(request?.Uid);
                return Ok(response);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception)
            {
                return StatusCode(500, "Erro interno do servidor.");
            }
        }
    }
}