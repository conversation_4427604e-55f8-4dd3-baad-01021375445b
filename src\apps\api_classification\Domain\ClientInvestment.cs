using System;

namespace Pati.ClassificationService.Domain
{
    public class ClientInvestment
    {
        public int ClientId { get; set; }
        public int InvestmentId { get; set; }
        public Investment Investment { get; set; } = null!;
        public int Quantity { get; set; }
        public DateTime InvestmentDate { get; set; }
        public decimal InvestedAmount { get; set; }

        public decimal CalculateTotalValue()
        {
            // Valor total = valor investido (sem preço atual pois não está no banco)
            return InvestedAmount;
        }

        public string GetRiskContribution()
        {
            // Exemplo: retorna o risco do investimento
            return Investment?.Risk ?? "Desconhecido";
        }

        public bool IsCompliantWithProfile(string clientProfile)
        {
            // Verifica se o risco do investimento está de acordo com o perfil do cliente
            if (Investment == null || string.IsNullOrEmpty(clientProfile)) return false;
            // Exemplo de regra: Conservador só pode ter investimentos conservadores, Moderado pode ter conservador/moderado, Arrojado pode ter todos
            if (clientProfile.Equals("Conservador", StringComparison.OrdinalIgnoreCase))
                return Investment.Risk.Equals("Conservador", StringComparison.OrdinalIgnoreCase);
            if (clientProfile.Equals("Moderado", StringComparison.OrdinalIgnoreCase))
                return Investment.Risk.Equals("Conservador", StringComparison.OrdinalIgnoreCase) || Investment.Risk.Equals("Moderado", StringComparison.OrdinalIgnoreCase);
            // Arrojado/Sofisticado pode ter qualquer risco
            return true;
        }

        public decimal GetAllocationPercentage(decimal totalPortfolioValue)
        {
            // Retorna a porcentagem desse investimento na carteira
            if (totalPortfolioValue <= 0) return 0;
            return CalculateTotalValue() / totalPortfolioValue * 100;
        }

        public string GetInvestmentSummary()
        {
            // Retorna um resumo textual do investimento
            return $"{Investment?.Name ?? "Desconhecido"} - Quantidade: {Quantity}, Valor Atual: R$ {CalculateTotalValue():N2}, Lucro/Prejuízo: R$ {GetProfitOrLoss():N2}";
        }

        public void UpdateInvestedAmount(decimal newAmount)
        {
            // Atualiza o valor investido (ex: após novo aporte ou resgate)
            InvestedAmount = newAmount;
        }

        public decimal GetProfitOrLoss()
        {
            // Exemplo: calcula lucro/prejuízo considerando valor investido e valor atual
            return CalculateTotalValue() - InvestedAmount;
        }
    }
}