-- Inserção de dados na tabela ADVISOR
-- Define os assessores disponíveis no sistema.
INSERT INTO ADVISOR (advisor_id, uid) VALUES
(1, 'auth0|advisor1'),
(2, 'auth0|advisor2'),
(3, 'auth0|advisor3');

-- Inserção de dados na tabela CLIENT
-- Define os clientes iniciais do sistema, baseados em dados reais.
-- IDs específicos são usados para manter a referência dos dados originais.
INSERT INTO CLIENT (client_id, name, email, phone_number, risk_profile_wallet, risk_profile_form, compliance, advisor_id) VALUES
(27, 'Cliente 27', '<EMAIL>', '+5511999999927', 'Moderado', 'Conservador', TRUE, 1),
(44, 'Cliente 44', '<EMAIL>', '+5511999999944', 'Sofisticado', 'Conservador', TRUE, 2),
-- IDs 55 e 89 foram identificados nos CSVs, assumindo detalhes adicionais.
(55, 'Cliente 55', '<EMAIL>', '+5511999999955', 'Moderado', '<PERSON>rado', <PERSON>LSE, 3),
(89, 'Cliente 89', '<EMAIL>', '+5511999999989', 'Arrojado', 'Arrojado', FALSE, 1),
(103, 'Cliente 103', '<EMAIL>', '+5511999999103', 'Moderado', 'Conservador', TRUE, 2);

-- Inserção de dados na tabela INVESTMENT (Bloco Único)
-- Define os tipos de investimentos disponíveis, incluindo exemplos de diversas categorias.
INSERT INTO INVESTMENT (name, risk, type) VALUES
-- Amostra de RF Privado e Debêntures (baseado nos CSVs)
('CRI CDIE', 'Baixo', 'TITULOS RF PRIVADOS BRASIL'),
('CRA CDIE', 'Baixo', 'TITULOS RF PRIVADOS BRASIL'),
('CRA IPCA', 'Baixo', 'TITULOS RF PRIVADOS BRASIL'),
('RRRP13 IPCA', 'Médio', 'DEBENTURES BRASIL'),
('EEELA1 IPCA', 'Médio', 'DEBENTURES BRASIL'),
('TRRG12 IPCA', 'Médio', 'DEBENTURES BRASIL'),
('PEJA11 IPCA', 'Médio', 'DEBENTURES BRASIL'),
('CRI IPCA', 'Baixo', 'TITULOS RF PRIVADOS BRASIL'),
('CEED12 IPCA', 'Médio', 'DEBENTURES BRASIL'),
('CGASA2 IPCA', 'Médio', 'DEBENTURES BRASIL'),
('CBAN12 IPCA', 'Médio', 'DEBENTURES BRASIL'),
('TAEEC2 IPCA', 'Médio', 'DEBENTURES BRASIL'),
-- Exemplos de Títulos Públicos
('Tesouro Selic 2027', 'Baixo', 'Títulos RF Público Brasil'),
('Tesouro IPCA+ 2045', 'Baixo', 'Títulos RF Público Brasil'),
-- Exemplos de Ações Brasil
('PETR4', 'Arrojado', 'Ações Brasil'),
('VALE3', 'Arrojado', 'Ações Brasil'),
('MGLU3', 'Arrojado', 'Ações Brasil'),
-- Exemplos de Ações Internacionais
('AAPL', 'Alto', 'Ações Internacionais'),
('MSFT', 'Alto', 'Ações Internacionais'),
('AMZN', 'Alto', 'Ações Internacionais');

-- Inserção de dados na tabela CLIENT_INVESTMENT
-- Associa clientes a seus investimentos, incluindo data, quantidade e valor.
-- Utiliza uma combinação de dados reais (amostra dos CSVs) e exemplos para diversificar as carteiras.
INSERT INTO CLIENT_INVESTMENT (client_id, investment_id, investment_date, quantity, invested_amount) VALUES
-- Carteira Cliente 89
(89, (SELECT investment_id FROM INVESTMENT WHERE name = 'PETR4' LIMIT 1), '2025-04-02', 100, 102000.00), -- Usando um Debenture existente como exemplo
(89, (SELECT investment_id FROM INVESTMENT WHERE name = 'VALE3' LIMIT 1), '2025-04-12', 50, 50500.00), -- Usando um RF Privado existente como exemplo
(89, (SELECT investment_id FROM INVESTMENT WHERE name = 'MGLU3' LIMIT 1), '2025-05-18', 1500, 3750.00);


-- Mensagem final de confirmação (visível no psql)
\echo 'Script 02_initial_data.sql executado. Dados iniciais inseridos/atualizados.'