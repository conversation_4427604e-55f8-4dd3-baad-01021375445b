using api_data_presentation.Models.Entities;
using api_data_presentation.Models.QueryParameters;

public interface IClientRepository
{
    Task<(int Conform, int Inconform)> GetComplianceStatsByAdvisorIdAsync(int advisorId);
    Task<List<(Client Client, decimal TotalInvested)>> GetClientsByAdvisorIdAsync(int advisorId, ClientListQueryParams queryParams);
    Task<Advisor?> GetAdvisorByUidAsync(string uid);
    Task<int> CreateAdvisorAsync(string uid);
    Task<List<ClientInvestment>> GetClientInvestmentsAsync(int clientId, InvestmentQueryParams queryParams);
    Task<Client?> GetClientByIdAsync(int clientId);
    Task<Investment?> GetInvestmentByIdAsync(int investmentId);
}