<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="../../docs/assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>
<br/>

# Nome do Projeto: Pati (Plataforma de Adequação de Tipo de Investidor)

## Nome do Grupo: MMs (Meninas Malvadas)

## Visão Geral

&emsp; Este diretório contém os scripts SQL para criação e inserção de dados em um banco de dados PostgreSQL, voltado à solução PATI (Plataforma de Adequação de Tipo de Investidor). O banco de dados foi modelado com foco em normalização, integridade referencial e conformidade com a Resolução CVM 175.

> Novos scripts poderão ser adicionados futuramente nesta pasta `~/src/database`.

## Dicionário de Dados (v1.1)

### Tabela: CLIENT
| Campo | Tipo | Tamanho | Nulo | Chave | Descrição |
|-------|------|---------|------|-------|-----------|
| id | INTEGER | - | NÃO | PK | Identificador único do cliente no sistema PATI |
| name | VARCHAR | 255 | NÃO | - | Nome completo do cliente conforme cadastro oficial |
| email | VARCHAR | 255 | NÃO | UNIQUE | Email único do cliente para autenticação e comunicação |
| phone_number | VARCHAR | 20 | SIM | - | Telefone de contato do cliente (formato: +55 11 99999-9999, permite nulos para clientes sem telefone cadastrado) |
| risk_profile_form | VARCHAR | 20 | NÃO | - | Perfil de risco declarado pelo cliente no formulário de suitability ('Conservador', 'Moderado', 'Arrojado') conforme CVM 175 |
| risk_profile_wallet | VARCHAR | 20 | SIM | - | Perfil de risco calculado automaticamente baseado na composição atual da carteira de investimentos |
| compliance | BOOLEAN | NÃO | - | Indica conformidade entre perfis de risco (false=conforme, true=não conforme/divergência detectada) |
| created_at | TIMESTAMP | - | NÃO | - | Data e hora de criação do registro no sistema (formato UTC) |
| updated_at | TIMESTAMP | - | NÃO | - | Data e hora da última atualização dos dados do cliente (formato UTC) |

### Tabela: INVESTMENT
| Campo | Tipo | Tamanho | Nulo | Chave | Descrição |
|-------|------|---------|------|-------|-----------|
| id | INTEGER | - | NÃO | PK | Identificador único do produto de investimento no catálogo BTG |
| name | VARCHAR | 255 | NÃO | - | Nome oficial do ativo/investimento conforme CVM (ex: 'Tesouro IPCA+ 2029', 'PETR4') |
| type | VARCHAR | 50 | NÃO | - | Categoria do investimento ('Renda Fixa', 'Ações', 'Fundos', 'Derivativos', 'Multimercado') |
| risk_level | VARCHAR | 20 | NÃO | - | Classificação de risco do investimento ('Conservador', 'Moderado', 'Arrojado') conforme metodologia BTG |
| income_type | VARCHAR | 50 | SIM | - | Tipo de rendimento do ativo ('Prefixado', 'Pós-fixado', 'IPCA+', 'Dividendos', 'Juros sobre Capital') |
| created_at | TIMESTAMP | - | NÃO | - | Data de inclusão do produto no catálogo de investimentos (formato UTC) |

### Tabela: CLIENT_INVESTMENT
| Campo | Tipo | Tamanho | Nulo | Chave | Descrição |
|-------|------|---------|------|-------|-----------|
| id | INTEGER | - | NÃO | PK | Identificador único da posição do cliente no investimento |
| client_id | INTEGER | - | NÃO | FK | Referência ao cliente proprietário da posição (FK para CLIENT.id) |
| investment_id | INTEGER | - | NÃO | FK | Referência ao produto de investimento (FK para INVESTMENT.id) |
| quantity | DECIMAL | 10,2 | NÃO | - | Quantidade de cotas/ações/títulos detidos pelo cliente (precisão de 2 casas decimais) |
| value | DECIMAL | 15,2 | NÃO | - | Valor total da posição em reais (quantidade × preço unitário, precisão de 2 casas decimais) |
| purchase_date | DATE | - | NÃO | - | Data da aquisição inicial da posição (formato YYYY-MM-DD) |
| created_at | TIMESTAMP | - | NÃO | - | Data de criação do registro da posição no sistema (formato UTC) |

### Tabela: COMPLIANCE_HISTORY
| Campo | Tipo | Tamanho | Nulo | Chave | Descrição |
|-------|------|---------|------|-------|-----------|
| history_id | INTEGER | - | NÃO | PK | Identificador único do registro de auditoria de compliance |
| client_id | INTEGER | - | NÃO | FK | Referência ao cliente auditado (FK para CLIENT.id) |
| previous_status | BOOLEAN | - | NÃO | - | Status anterior de compliance (false=conforme, true=não conforme) |
| new_status | BOOLEAN | - | NÃO | - | Novo status de compliance após reavaliação (false=conforme, true=não conforme) |
| change_date | TIMESTAMP | - | NÃO | - | Data e hora da mudança de status de compliance (formato UTC, para auditoria CVM) |

### Funções do Banco de Dados
- **calculate_wallet_risk_profile()**: Calcula o perfil de risco baseado na composição da carteira
- **is_investment_compatible_with_profile()**: Verifica compatibilidade entre investimento e perfil

### Índices Criados
- `idx_client_email`: Índice único no email do cliente
- `idx_client_compliance`: Índice no campo compliance para consultas rápidas
- `idx_client_investment_client`: Índice na chave estrangeira client_id
- `idx_client_investment_investment`: Índice na chave estrangeira investment_id

## Diagrama ERD (Entidade-Relacionamento)

```
┌─────────────────────┐       ┌─────────────────────┐       ┌─────────────────────┐
│       CLIENT        │       │  CLIENT_INVESTMENT  │       │     INVESTMENT      │
├─────────────────────┤       ├─────────────────────┤       ├─────────────────────┤
│ id (PK)            │◄──────┤ client_id (FK)     │       │ id (PK)            │
│ name               │       │ investment_id (FK) ├──────►│ name               │
│ email (UNIQUE)     │       │ quantity           │       │ type               │
│ phone_number       │       │ value              │       │ risk_level         │
│ risk_profile_form  │       │ purchase_date      │       │ income_type        │
│ risk_profile_wallet│       │ created_at         │       │ created_at         │
│ compliance         │       └─────────────────────┘       └─────────────────────┘
│ created_at         │
│ updated_at         │
└─────────────────────┘
         │
         │ 1:N
         ▼
┌─────────────────────┐
│ COMPLIANCE_HISTORY  │
├─────────────────────┤
│ history_id (PK)    │
│ client_id (FK)     │
│ previous_status    │
│ new_status         │
│ change_date        │
└─────────────────────┘

Relacionamentos:
- CLIENT 1:N CLIENT_INVESTMENT (Um cliente pode ter múltiplos investimentos)
- INVESTMENT 1:N CLIENT_INVESTMENT (Um investimento pode pertencer a múltiplos clientes)
- CLIENT 1:N COMPLIANCE_HISTORY (Um cliente pode ter múltiplos registros de histórico)
```

## Estrutura de Diretórios

```text
src/
└── database/
    ├── 01_create_database.sql   # Criação de tabelas e estrutura do banco
    ├── 02_initial_data.sql      # Dados fictícios para testes e desenvolvimento
    ├── Tipo_ativos_com_risco.csv # Dados de tipos de ativos e seus riscos
    ├── inconformidades.csv      # Dados de clientes em inconformidade
    └── index.md                 # Documentação e instruções de uso
```

## Implantação do Banco de Dados (v1.1)

### Controle de Versão
- **Versão Atual**: 1.1
- **Data de Release**: 2025-01-24
- **Versão Anterior**: 1.0 (2025-01-20)
- **Migration Scripts**: Localizados em `migrations/01_to_02.sql`
- **Changelog**:
  - v1.1: Descrições detalhadas de campos, melhorias na documentação
  - v1.0: Estrutura inicial do banco de dados

### Versionamento de Scripts
```sql
-- Exemplo de cabeçalho de versionamento
-- Script: 01_create_database.sql
-- Versão: 1.0
-- Data: 2025-01-24
-- Descrição: Criação inicial das tabelas do sistema PATI
-- Dependências: PostgreSQL 16+
```

### Processo de Deploy
1. **Backup**: Sempre realizar backup antes de aplicar mudanças
2. **Validação**: Executar scripts em ambiente de teste
3. **Aplicação**: Executar scripts em ordem sequencial
4. **Verificação**: Validar integridade dos dados pós-deploy
5. **Rollback**: Procedimento de reversão em caso de problemas

## Como Executar os Scripts

### Pré-requisitos
- PostgreSQL instalado ou acesso a um serviço compatível (local ou remoto)
- Ambiente de execução SQL como:
  - Terminal PostgreSQL (`psql`)
  - Leitores/clients de SGDB como [DBeaver](https://dbeaver.io/)
  - Serviços de nuvem como [Amazon RDS](https://aws.amazon.com/rds/), [Supabase](https://supabase.com/), entre outros

### Passos para execução

1. **Backup do banco existente** (se aplicável):
   ```bash
   pg_dump pati_db > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Criação do banco de dados**:
   ```sql
   CREATE DATABASE pati_db;
   ```

3. **Acesse o banco de dados criado**:
   - Pelo terminal:
     ```bash
     psql -d pati_db
     ```
   - Ou configure sua conexão em seu client/serviço favorito

4. **Execute o script de criação de estrutura**:
   ```sql
   \i src/database/01_create_database.sql
   ```

5. **Insira os dados iniciais**:
   ```sql
   \i src/database/02_initial_data.sql
   ```

6. **Validação pós-instalação**:
   ```sql
   -- Verificar criação das tabelas
   \dt

   -- Verificar dados inseridos
   SELECT COUNT(*) FROM client;
   SELECT COUNT(*) FROM investment;
   SELECT COUNT(*) FROM client_investment;

   -- Testar função de cálculo de perfil
   SELECT calculate_wallet_risk_profile(1);
   ```

## Procedimentos de Rollback

### Rollback Completo
Para reverter completamente a instalação do banco:

```bash
# 1. Fazer backup do estado atual (se necessário)
pg_dump pati_db > backup_before_rollback_$(date +%Y%m%d_%H%M%S).sql

# 2. Remover banco atual
dropdb pati_db

# 3. Restaurar backup anterior
createdb pati_db
pg_restore --dbname=pati_db backup_v1.sql
```

### Rollback de Migração Específica
Para reverter mudanças específicas:

```sql
-- Exemplo: Reverter criação de índices
DROP INDEX IF EXISTS idx_client_profile;
DROP INDEX IF EXISTS idx_client_compliance;

-- Exemplo: Reverter criação de tabela
DROP TABLE IF EXISTS compliance_history CASCADE;

-- Exemplo: Reverter alteração de coluna
ALTER TABLE client DROP COLUMN IF EXISTS risk_profile_wallet;
```

### Scripts de Rollback por Versão

#### Rollback v1.1 → v1.0
```sql
-- Reverter melhorias de documentação (sem impacto estrutural)
-- Nenhuma alteração estrutural necessária
-- Apenas atualizar versão na documentação
```

#### Rollback v1.0 → v0.9
```sql
-- Remover funções adicionadas
DROP FUNCTION IF EXISTS calculate_wallet_risk_profile();
DROP FUNCTION IF EXISTS is_investment_compatible_with_profile();

-- Remover tabelas na ordem inversa (devido às FKs)
DROP TABLE IF EXISTS compliance_history;
DROP TABLE IF EXISTS client_investment;
DROP TABLE IF EXISTS investment;
DROP TABLE IF EXISTS client;
```

### Validação Pós-Rollback
```sql
-- Verificar se rollback foi bem-sucedido
\dt  -- Listar tabelas restantes
\df  -- Listar funções restantes

-- Verificar integridade dos dados (se aplicável)
SELECT COUNT(*) FROM client;
SELECT COUNT(*) FROM investment;
```

## Observações Importantes

- **Backup Obrigatório**: Sempre realize backup antes de qualquer operação de rollback
- **Ordem de Execução**: Os scripts devem ser executados na ordem correta devido às dependências
- **Ambiente de Teste**: Teste todos os procedimentos em ambiente de desenvolvimento primeiro
- **Validação**: Sempre valide a integridade dos dados após operações de rollback
- **Documentação**: Documente todas as operações realizadas para auditoria

### Dependências de Infraestrutura
- **PostgreSQL**: Versão 16 ou superior
- **Docker**: Para containerização (opcional)
- **pgAdmin**: Para visualização e administração (opcional)
- **Backup Storage**: Espaço adequado para armazenamento de backups
- **Monitoramento**: Ferramentas para acompanhar performance do banco