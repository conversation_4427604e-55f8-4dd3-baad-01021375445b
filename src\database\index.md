<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="../../docs/assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>
<br/>

# Nome do Projeto: Pati (Plataforma de Adequação de Tipo de Investidor)

## Nome do Grupo: MMs (Meninas Malvadas)

## Visão Geral

&emsp; Este diretório contém os scripts SQL para criação e inserção de dados em um banco de dados PostgreSQL, voltado à solução que gerencia usuários administrativos, registros de uso, profissionais, atendimentos e tecnologias assistivas. O banco de dados foi modelado com foco em normalização e integridade referencial.

> Novos scripts poderão ser adicionados futuramente nesta pasta `~/src/database`.

## Estrutura de Diretórios

```text
src/
└── database/
    ├── 01_create_database.sql   # Criação de tabelas e estrutura do banco
    ├── 02_initial_data.sql      # Dados fictícios para testes e desenvolvimento
    └── index.md                 # Documentação e instruções de uso
```

## Como Executar os Scripts

### Pré-requisitos
- PostgreSQL instalado ou acesso a um serviço compatível (local ou remoto)
- Ambiente de execução SQL como:
  - Terminal PostgreSQL (`psql`)
  - Leitores/clients de SGDB como [DBeaver](https://dbeaver.io/)
  - Serviços de nuvem como [Amazon RDS](https://aws.amazon.com/rds/), [Supabase](https://supabase.com/), entre outros

### Passos para execução

1. Criação do banco de dados:
   ```sql
   CREATE DATABASE gallaudet_db;
   ```

2. Acesse o banco de dados criado:
   - Pelo terminal:
     ```sh
     psql -d gallaudet_db
     ```
   - Ou configure sua conexão em seu client/serviço favorito

3. Execute o script de criação de estrutura:
   ```sql
   \i src/database/01_create_database.sql
   ```

4. Insira os dados iniciais:
   ```sql
   \i src/database/02_initial_data.sql
   ```

## Observações Importantes

- Os scripts funcionam tanto em ambientes locais quanto em serviços de banco de dados hospedados em nuvem.
- O script `01_create_database.sql` deve ser executado **antes** do `02_initial_data.sql`.
- Os dados inseridos são fictícios, podendo ser adaptados conforme a necessidade do projeto.