<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="../../docs/assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>
<br/>

# Nome do Projeto: Pati (Plataforma de Adequação de Tipo de Investidor)

## Nome do Grupo: MMs (Meninas Malvadas)

## Visão Geral

&emsp; Este diretório contém os scripts SQL para criação e inserção de dados em um banco de dados PostgreSQL, voltado à solução PATI (Plataforma de Adequação de Tipo de Investidor). O banco de dados foi modelado com foco em normalização, integridade referencial e conformidade com a Resolução CVM 175.

> Novos scripts poderão ser adicionados futuramente nesta pasta `~/src/database`.

## Dicionário de Dados (v1.0)

### Tabela: CLIENT
| Campo | Tipo | Tamanho | Nulo | Chave | Descrição |
|-------|------|---------|------|-------|-----------|
| id | INTEGER | - | NÃO | PK | Identificador único do cliente |
| name | VARCHAR | 255 | NÃO | - | Nome completo do cliente |
| email | VARCHAR | 255 | NÃO | UNIQUE | Email único do cliente |
| phone_number | VARCHAR | 20 | SIM | - | Telefone do cliente (permite nulos) |
| risk_profile_form | VARCHAR | 20 | NÃO | - | Perfil declarado no formulário (conservador, moderado, arrojado) |
| risk_profile_wallet | VARCHAR | 20 | SIM | - | Perfil calculado baseado na carteira |
| compliance | BOOLEAN | NÃO | - | Indica divergência entre perfis de risco (true=não conforme, false=conforme) |
| created_at | TIMESTAMP | - | NÃO | - | Data de criação do registro |
| updated_at | TIMESTAMP | - | NÃO | - | Data da última atualização |

### Tabela: INVESTMENT
| Campo | Tipo | Tamanho | Nulo | Chave | Descrição |
|-------|------|---------|------|-------|-----------|
| id | INTEGER | - | NÃO | PK | Identificador único do investimento |
| name | VARCHAR | 255 | NÃO | - | Nome do ativo/investimento |
| type | VARCHAR | 50 | NÃO | - | Tipo do investimento (ação, renda fixa, etc.) |
| risk_level | VARCHAR | 20 | NÃO | - | Nível de risco do investimento |
| income_type | VARCHAR | 50 | SIM | - | Tipo de rendimento |
| created_at | TIMESTAMP | - | NÃO | - | Data de criação do registro |

### Tabela: CLIENT_INVESTMENT
| Campo | Tipo | Tamanho | Nulo | Chave | Descrição |
|-------|------|---------|------|-------|-----------|
| id | INTEGER | - | NÃO | PK | Identificador único da relação |
| client_id | INTEGER | - | NÃO | FK | Referência ao cliente |
| investment_id | INTEGER | - | NÃO | FK | Referência ao investimento |
| quantity | DECIMAL | 10,2 | NÃO | - | Quantidade do investimento |
| value | DECIMAL | 15,2 | NÃO | - | Valor total investido |
| purchase_date | DATE | - | NÃO | - | Data da compra |
| created_at | TIMESTAMP | - | NÃO | - | Data de criação do registro |

### Tabela: COMPLIANCE_HISTORY
| Campo | Tipo | Tamanho | Nulo | Chave | Descrição |
|-------|------|---------|------|-------|-----------|
| history_id | INTEGER | - | NÃO | PK | Identificador único do histórico |
| client_id | INTEGER | - | NÃO | FK | Referência ao cliente |
| previous_status | BOOLEAN | NÃO | - | Status anterior de compliance |
| new_status | BOOLEAN | NÃO | - | Novo status de compliance |
| change_date | TIMESTAMP | - | NÃO | - | Data da mudança de status |

### Funções do Banco de Dados
- **calculate_wallet_risk_profile()**: Calcula o perfil de risco baseado na composição da carteira
- **is_investment_compatible_with_profile()**: Verifica compatibilidade entre investimento e perfil

### Índices Criados
- `idx_client_email`: Índice único no email do cliente
- `idx_client_compliance`: Índice no campo compliance para consultas rápidas
- `idx_client_investment_client`: Índice na chave estrangeira client_id
- `idx_client_investment_investment`: Índice na chave estrangeira investment_id

## Diagrama ERD (Entidade-Relacionamento)

```
┌─────────────────────┐       ┌─────────────────────┐       ┌─────────────────────┐
│       CLIENT        │       │  CLIENT_INVESTMENT  │       │     INVESTMENT      │
├─────────────────────┤       ├─────────────────────┤       ├─────────────────────┤
│ id (PK)            │◄──────┤ client_id (FK)     │       │ id (PK)            │
│ name               │       │ investment_id (FK) ├──────►│ name               │
│ email (UNIQUE)     │       │ quantity           │       │ type               │
│ phone_number       │       │ value              │       │ risk_level         │
│ risk_profile_form  │       │ purchase_date      │       │ income_type        │
│ risk_profile_wallet│       │ created_at         │       │ created_at         │
│ compliance         │       └─────────────────────┘       └─────────────────────┘
│ created_at         │
│ updated_at         │
└─────────────────────┘
         │
         │ 1:N
         ▼
┌─────────────────────┐
│ COMPLIANCE_HISTORY  │
├─────────────────────┤
│ history_id (PK)    │
│ client_id (FK)     │
│ previous_status    │
│ new_status         │
│ change_date        │
└─────────────────────┘

Relacionamentos:
- CLIENT 1:N CLIENT_INVESTMENT (Um cliente pode ter múltiplos investimentos)
- INVESTMENT 1:N CLIENT_INVESTMENT (Um investimento pode pertencer a múltiplos clientes)
- CLIENT 1:N COMPLIANCE_HISTORY (Um cliente pode ter múltiplos registros de histórico)
```

## Estrutura de Diretórios

```text
src/
└── database/
    ├── 01_create_database.sql   # Criação de tabelas e estrutura do banco
    ├── 02_initial_data.sql      # Dados fictícios para testes e desenvolvimento
    ├── Tipo_ativos_com_risco.csv # Dados de tipos de ativos e seus riscos
    ├── inconformidades.csv      # Dados de clientes em inconformidade
    └── index.md                 # Documentação e instruções de uso
```

## Implantação do Banco de Dados (v1.0)

### Controle de Versão
- **Versão Atual**: 1.0
- **Data de Release**: 2025-01-24
- **Migration Scripts**: Localizados em `migrations/` (quando aplicável)
- **Changelog**: Documentado em cada script SQL

### Versionamento de Scripts
```sql
-- Exemplo de cabeçalho de versionamento
-- Script: 01_create_database.sql
-- Versão: 1.0
-- Data: 2025-01-24
-- Descrição: Criação inicial das tabelas do sistema PATI
-- Dependências: PostgreSQL 16+
```

### Processo de Deploy
1. **Backup**: Sempre realizar backup antes de aplicar mudanças
2. **Validação**: Executar scripts em ambiente de teste
3. **Aplicação**: Executar scripts em ordem sequencial
4. **Verificação**: Validar integridade dos dados pós-deploy
5. **Rollback**: Procedimento de reversão em caso de problemas

## Como Executar os Scripts

### Pré-requisitos
- PostgreSQL instalado ou acesso a um serviço compatível (local ou remoto)
- Ambiente de execução SQL como:
  - Terminal PostgreSQL (`psql`)
  - Leitores/clients de SGDB como [DBeaver](https://dbeaver.io/)
  - Serviços de nuvem como [Amazon RDS](https://aws.amazon.com/rds/), [Supabase](https://supabase.com/), entre outros

### Passos para execução

1. **Backup do banco existente** (se aplicável):
   ```bash
   pg_dump pati_db > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Criação do banco de dados**:
   ```sql
   CREATE DATABASE pati_db;
   ```

3. **Acesse o banco de dados criado**:
   - Pelo terminal:
     ```bash
     psql -d pati_db
     ```
   - Ou configure sua conexão em seu client/serviço favorito

4. **Execute o script de criação de estrutura**:
   ```sql
   \i src/database/01_create_database.sql
   ```

5. **Insira os dados iniciais**:
   ```sql
   \i src/database/02_initial_data.sql
   ```

6. **Validação pós-instalação**:
   ```sql
   -- Verificar criação das tabelas
   \dt

   -- Verificar dados inseridos
   SELECT COUNT(*) FROM client;
   SELECT COUNT(*) FROM investment;
   SELECT COUNT(*) FROM client_investment;

   -- Testar função de cálculo de perfil
   SELECT calculate_wallet_risk_profile(1);
   ```

## Procedimentos de Rollback

### Rollback Completo
Para reverter completamente a instalação do banco:

```bash
# 1. Fazer backup do estado atual (se necessário)
pg_dump pati_db > backup_before_rollback_$(date +%Y%m%d_%H%M%S).sql

# 2. Remover banco atual
dropdb pati_db

# 3. Restaurar backup anterior
createdb pati_db
pg_restore --dbname=pati_db backup_v1.sql
```

### Rollback de Migração Específica
Para reverter mudanças específicas:

```sql
-- Exemplo: Reverter criação de índices
DROP INDEX IF EXISTS idx_client_profile;
DROP INDEX IF EXISTS idx_client_compliance;

-- Exemplo: Reverter criação de tabela
DROP TABLE IF EXISTS compliance_history CASCADE;

-- Exemplo: Reverter alteração de coluna
ALTER TABLE client DROP COLUMN IF EXISTS risk_profile_wallet;
```

### Scripts de Rollback por Versão

#### Rollback v1.0 → v0.9
```sql
-- Remover funções adicionadas
DROP FUNCTION IF EXISTS calculate_wallet_risk_profile();
DROP FUNCTION IF EXISTS is_investment_compatible_with_profile();

-- Remover tabelas na ordem inversa (devido às FKs)
DROP TABLE IF EXISTS compliance_history;
DROP TABLE IF EXISTS client_investment;
DROP TABLE IF EXISTS investment;
DROP TABLE IF EXISTS client;
```

### Validação Pós-Rollback
```sql
-- Verificar se rollback foi bem-sucedido
\dt  -- Listar tabelas restantes
\df  -- Listar funções restantes

-- Verificar integridade dos dados (se aplicável)
SELECT COUNT(*) FROM client;
SELECT COUNT(*) FROM investment;
```

## Observações Importantes

- **Backup Obrigatório**: Sempre realize backup antes de qualquer operação de rollback
- **Ordem de Execução**: Os scripts devem ser executados na ordem correta devido às dependências
- **Ambiente de Teste**: Teste todos os procedimentos em ambiente de desenvolvimento primeiro
- **Validação**: Sempre valide a integridade dos dados após operações de rollback
- **Documentação**: Documente todas as operações realizadas para auditoria

### Dependências de Infraestrutura
- **PostgreSQL**: Versão 16 ou superior
- **Docker**: Para containerização (opcional)
- **pgAdmin**: Para visualização e administração (opcional)
- **Backup Storage**: Espaço adequado para armazenamento de backups
- **Monitoramento**: Ferramentas para acompanhar performance do banco