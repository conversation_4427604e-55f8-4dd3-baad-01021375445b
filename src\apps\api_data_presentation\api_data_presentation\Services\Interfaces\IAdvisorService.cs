using api_data_presentation.Models.DTOs;
using api_data_presentation.Models.QueryParameters;

namespace api_data_presentation.Services.Interfaces
{
    public interface IAdvisorService
    {
        Task<ComplianceStatsDto> GetComplianceStatsAsync(int advisorId);
        Task<List<ClientListDto>> GetClientsAsync(int advisorId, ClientListQueryParams queryParams);
        Task<AdvisorAuthResponseDto> AuthenticateOrCreateAsync(string uid);
        Task<List<ClientInvestmentDto>> GetClientInvestmentsAsync(int clientId, InvestmentQueryParams queryParams);
        Task<ClientRecommendationsDto?> GetClientRecommendationsAsync(int clientId);
    }
}