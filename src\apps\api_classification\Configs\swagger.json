{"openapi": "3.0.1", "info": {"title": "Pati.ClassificationService API", "description": "API para classificação de carteiras de investimento baseado nas regras CVM 175 e modelo SVD", "version": "v1", "contact": {"name": "Pati Team", "email": "<EMAIL>"}}, "servers": [{"url": "https://localhost:7001", "description": "Development server"}], "paths": {"/internal/v1/portfolios/classify": {"post": {"tags": ["Classification"], "summary": "Classifica uma carteira identificando inconformidades", "description": "Recebe dados de carteira do UserService, aplica regras CVM 175 e modelo SVD, e envia resultados ao RecommendationService", "operationId": "ClassifyPortfolio", "requestBody": {"description": "Dados da carteira a ser classificada conforme CSV inconformidades.csv", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioInputDto"}, "example": {"accountId": 27, "portfolioId": "portfolio_27", "portfolioProfile": "Conservador", "assets": [{"assetId": "IGTI11_001", "name": "IGTI11", "type": "ACOES BRASIL", "incomeType": "<PERSON><PERSON>", "investmentProfile": "<PERSON><PERSON><PERSON><PERSON>", "quantity": 6800, "value": 132396}]}}}, "required": true}, "responses": {"200": {"description": "Classificação realizada com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioOutputDto"}}}}, "400": {"description": "Dados de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Erro interno do servidor"}}}}, "/internal/v1/portfolios/risk-validation": {"get": {"tags": ["Classification"], "summary": "Verifica se um risco é permitido para um perfil específico", "description": "Implementa a função risco_permitido do notebook SVD", "operationId": "ValidateRiskForProfile", "parameters": [{"name": "profile", "in": "query", "description": "<PERSON><PERSON><PERSON> da carteira (Conservador, Moderado, Arrojado, Sofisticado)", "required": true, "schema": {"type": "string", "enum": ["Conservador", "Moderado", "<PERSON><PERSON><PERSON><PERSON>", "Sofisticado"]}}, {"name": "risk", "in": "query", "description": "Nível de risco do ativo", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Verificação realizada com sucesso", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "400": {"description": "Parâmet<PERSON>"}}}}, "/internal/v1/portfolios/ideal-proportions": {"get": {"tags": ["Classification"], "summary": "Obtém as proporções ideais de Renda Fixa e Renda Variável para um perfil", "description": "Retorna as proporções baseadas no notebook SVD", "operationId": "GetIdealProportions", "parameters": [{"name": "profile", "in": "query", "description": "<PERSON><PERSON><PERSON> da carteira (Conservador, Moderado, Arrojado, Sofisticado)", "required": true, "schema": {"type": "string", "enum": ["Conservador", "Moderado", "<PERSON><PERSON><PERSON><PERSON>", "Sofisticado"]}}], "responses": {"200": {"description": "Proporções obtidas com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IncomeAllocationDto"}}}}, "400": {"description": "<PERSON><PERSON><PERSON>"}}}}, "/internal/v1/portfolios/health": {"get": {"tags": ["Health"], "summary": "Verifica o status de saúde do serviço de classificação", "description": "Retorna informações sobre o status do serviço e endpoints disponíveis", "operationId": "GetHealth", "responses": {"200": {"description": "Serviço funcionando corretamente", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "healthy"}, "timestamp": {"type": "string", "format": "date-time"}, "service": {"type": "string", "example": "Pati.ClassificationService"}, "version": {"type": "string", "example": "1.0.0"}, "endpoints": {"type": "array", "items": {"type": "string"}}}}}}}}}}}, "components": {"schemas": {"PortfolioInputDto": {"type": "object", "properties": {"accountId": {"type": "integer", "format": "int32", "description": "Identific<PERSON> da conta (ex.: 27)"}, "portfolioId": {"type": "string", "description": "Identificador único da carteira"}, "assets": {"type": "array", "items": {"$ref": "#/components/schemas/AssetInputDto"}, "description": "Lista de ativos na carteira"}, "portfolioProfile": {"type": "string", "enum": ["Conservador", "Moderado", "<PERSON><PERSON><PERSON><PERSON>", "Sofisticado"], "description": "<PERSON><PERSON><PERSON> da carteira"}}, "required": ["accountId", "portfolioId", "assets", "portfolioProfile"]}, "AssetInputDto": {"type": "object", "properties": {"assetId": {"type": "string", "description": "Identificador único do ativo (ex.: CRI CDIE_001)"}, "name": {"type": "string", "description": "Nome do ativo (ex.: CRI CDIE)"}, "type": {"type": "string", "description": "Tipo do ativo (ex.: TITULOS RF PRIVADOS BRASIL)"}, "incomeType": {"type": "string", "enum": ["Renda Fixa", "<PERSON><PERSON>"], "description": "<PERSON><PERSON><PERSON> de <PERSON>nda"}, "investmentProfile": {"type": "string", "enum": ["Conservador", "Moderado", "<PERSON><PERSON><PERSON><PERSON>", "Sofisticado"], "description": "Perfil de investimento"}, "quantity": {"type": "number", "format": "decimal", "description": "Quantidade do ativo"}, "value": {"type": "number", "format": "decimal", "description": "<PERSON>or financeiro"}}, "required": ["assetId", "name", "type", "incomeType", "investmentProfile", "quantity", "value"]}, "PortfolioOutputDto": {"type": "object", "properties": {"accountId": {"type": "integer", "format": "int32", "description": "Identificador da conta"}, "portfolioId": {"type": "string", "description": "Identificador único da carteira"}, "assets": {"type": "array", "items": {"$ref": "#/components/schemas/AssetOutputDto"}, "description": "Lista de ativos na carteira"}, "inconsistencies": {"type": "array", "items": {"$ref": "#/components/schemas/InconsistencyDto"}, "description": "Lista de inconformidades identificadas"}, "portfolioProfile": {"type": "string", "description": "<PERSON><PERSON><PERSON> da carteira"}, "incomeAllocation": {"$ref": "#/components/schemas/IncomeAllocationDto", "description": "Alocação de renda (proporções de Renda Fixa e Renda Variável)"}}}, "AssetOutputDto": {"type": "object", "properties": {"assetId": {"type": "string", "description": "Identificador do ativo"}, "name": {"type": "string", "description": "Nome do ativo"}, "type": {"type": "string", "description": "Tipo do ativo"}, "incomeType": {"type": "string", "description": "<PERSON><PERSON><PERSON> de <PERSON>nda"}, "investmentProfile": {"type": "string", "description": "Perfil de investimento"}, "quantity": {"type": "number", "format": "decimal", "description": "Quantidade do ativo"}, "value": {"type": "number", "format": "decimal", "description": "<PERSON>or financeiro"}}}, "InconsistencyDto": {"type": "object", "properties": {"assetId": {"type": "string", "description": "Identificador do ativo com inconformidade"}, "name": {"type": "string", "description": "Nome do ativo"}, "description": {"type": "string", "description": "Descrição da inconformidade"}, "severity": {"type": "string", "enum": ["low", "medium", "high"], "description": "Severidade da inconformidade"}}}, "IncomeAllocationDto": {"type": "object", "properties": {"fixedIncome": {"type": "number", "format": "decimal", "description": "Proporção de Renda Fixa (ex.: 0.8)"}, "variableIncome": {"type": "number", "format": "decimal", "description": "Proporção de Renda Variável (ex.: 0.2)"}}}, "ValidationProblemDetails": {"type": "object", "properties": {"type": {"type": "string"}, "title": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "detail": {"type": "string"}, "instance": {"type": "string"}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}, "tags": [{"name": "Classification", "description": "Endpoints para classificação de carteiras e validação de conformidade"}, {"name": "Health", "description": "Endpoints para monitoramento de saúde do serviço"}]}