using System;
using System.Collections.Generic;
using System.Linq;

namespace Pati.ClassificationService.Domain
{
    public class Client
    {
        public int ClientId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; } // Nullable conforme esquema do banco
        public string Email { get; set; } = string.Empty;
        public string RiskProfileWallet { get; set; } = string.Empty; // Calculated from investments
        public string RiskProfileForm { get; set; } = string.Empty;   // Declared by form
        public bool NonCompliance { get; set; }
        public int AdvisorId { get; set; }
        public List<ClientInvestment> Investments { get; set; } = new List<ClientInvestment>();
        public List<ClientRecommendation> Recommendations { get; set; } = new List<ClientRecommendation>();

        public string GetCalculatedRiskProfile()
        {
            // Calcula o perfil de risco predominante dos investimentos do cliente
            if (Investments == null || !Investments.Any())
                return "Desconhecido";
            var riskGroups = Investments.GroupBy(i => i.Investment.Risk)
                .Select(g => new { Risk = g.Key, Total = g.Sum(i => i.InvestedAmount) })
                .OrderByDescending(g => g.Total);
            return riskGroups.First().Risk;
        }

        public bool IsCompliant()
        {
            // Cliente está conforme se o perfil da carteira for igual ao do formulário
            return string.Equals(GetCalculatedRiskProfile(), RiskProfileForm, StringComparison.OrdinalIgnoreCase);
        }

        public void UpdateCompliance()
        {
            NonCompliance = !IsCompliant();
        }

        public IEnumerable<ClientInvestment> GetInvestments()
        {
            return Investments;
        }

        public void AddInvestment(ClientInvestment investment)
        {
            if (investment == null)
                throw new ArgumentNullException(nameof(investment));
            if (!Investments.Any(i => i.InvestmentId == investment.InvestmentId))
                Investments.Add(investment);
        }

        public void RemoveInvestment(int investmentId)
        {
            var investment = Investments.FirstOrDefault(i => i.InvestmentId == investmentId);
            if (investment != null)
                Investments.Remove(investment);
        }

        public void AddRecommendation(ClientRecommendation recommendation)
        {
            if (recommendation == null)
                throw new ArgumentNullException(nameof(recommendation));
            Recommendations.Add(recommendation);
        }

        public IEnumerable<ClientRecommendation> GetRecommendations(bool onlyAccepted = false)
        {
            return onlyAccepted ? Recommendations.Where(r => r.Accepted) : Recommendations;
        }

        public decimal GetTotalInvestedAmount()
        {
            return Investments.Sum(i => i.InvestedAmount);
        }

        public void UpdateRiskProfileWallet()
        {
            RiskProfileWallet = GetCalculatedRiskProfile();
        }

        public void SyncCompliance()
        {
            UpdateRiskProfileWallet();
            UpdateCompliance();
        }

        public object GetAnalysisResult()
        {
            // Retorna um objeto detalhado com os dados da carteira, status e inconformidades específicas
            var inconformidades = new List<string>();
            foreach (var inv in Investments)
            {
                if (!inv.IsCompliantWithProfile(RiskProfileForm))
                {
                    inconformidades.Add($"Asset '{inv.Investment?.Name ?? inv.InvestmentId.ToString()}' not recommended for profile '{RiskProfileForm}'.");
                }
            }

            return new
            {
                ClientId = ClientId,
                Name = Name,
                Email = Email,
                PhoneNumber = PhoneNumber,
                RiskProfileForm = RiskProfileForm,
                RiskProfileWallet = RiskProfileWallet,
                NonCompliance = NonCompliance,
                Investments = Investments.Select(inv => new
                {
                    InvestmentId = inv.InvestmentId,
                    InvestmentName = inv.Investment?.Name,
                    InvestmentType = inv.Investment?.Type,
                    InvestmentRisk = inv.Investment?.Risk,
                    Quantity = inv.Quantity,
                    InvestedAmount = inv.InvestedAmount,
                    CurrentPrice = 0, // Price não está disponível no modelo atual
                    TotalValue = inv.CalculateTotalValue(),
                    IsCompliant = inv.IsCompliantWithProfile(RiskProfileForm),
                    InvestmentDate = inv.InvestmentDate
                }).ToList(),
                Inconformidades = string.Join("; ", inconformidades)
            };
        }
    }
}