using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Pati.ClassificationService.Application.Services;
using Pati.ClassificationService.DTOs;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Pati.ClassificationService.Infrastructure.Services
{
    public class RecommendationServiceClient : IRecommendationServiceClient
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<RecommendationServiceClient> _logger;
        private readonly RecommendationServiceOptions _options;

        public RecommendationServiceClient(
            HttpClient httpClient,
            ILogger<RecommendationServiceClient> logger,
            IOptions<RecommendationServiceOptions> options)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        }

        public async Task<bool> SendPortfolioAnalysisAsync(PortfolioOutputDto portfolioOutput)
        {
            try
            {
                if (portfolioOutput == null)
                {
                    _logger.LogWarning("Portfolio output is null, cannot send to RecommendationService");
                    return false;
                }

                var json = JsonSerializer.Serialize(portfolioOutput, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _logger.LogInformation("Sending portfolio analysis to RecommendationService for portfolio {PortfolioId}", 
                    portfolioOutput.PortfolioId);

                var response = await _httpClient.PostAsync(_options.AnalysisEndpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Successfully sent portfolio analysis to RecommendationService for portfolio {PortfolioId}", 
                        portfolioOutput.PortfolioId);
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to send portfolio analysis to RecommendationService. Status: {StatusCode}, Content: {Content}", 
                        response.StatusCode, errorContent);
                    return false;
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP error occurred while sending portfolio analysis to RecommendationService");
                return false;
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogError(ex, "Timeout occurred while sending portfolio analysis to RecommendationService");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error occurred while sending portfolio analysis to RecommendationService");
                return false;
            }
        }

        public async Task<bool> IsServiceAvailableAsync()
        {
            try
            {
                _logger.LogDebug("Checking RecommendationService availability");

                var response = await _httpClient.GetAsync(_options.HealthCheckEndpoint);
                
                var isAvailable = response.IsSuccessStatusCode;
                
                _logger.LogDebug("RecommendationService availability check result: {IsAvailable}", isAvailable);
                
                return isAvailable;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "RecommendationService is not available");
                return false;
            }
        }
    }

    /// <summary>
    /// Configurações para o RecommendationService
    /// </summary>
    public class RecommendationServiceOptions
    {
        public const string SectionName = "RecommendationService";

        public string BaseUrl { get; set; } = "http://localhost:5002";
        public string AnalysisEndpoint { get; set; } = "/api/v1/recommendations";
        public string HealthCheckEndpoint { get; set; } = "/health";
        public int TimeoutSeconds { get; set; } = 30;
    }
}
