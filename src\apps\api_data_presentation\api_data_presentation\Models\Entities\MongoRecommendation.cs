using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace api_data_presentation.Models.Entities
{
    public class MongoRecommendation
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string? Id { get; set; }

        [BsonElement("client_id")]
        public string ClientId { get; set; } = string.Empty;

        [BsonElement("investments")]
        public List<RecommendedInvestment> Investments { get; set; } = new();

        [BsonElement("recommendation_date")]
        public DateTime RecommendationDate { get; set; }
    }

    public class RecommendedInvestment
    {
        [BsonElement("investment_id")]
        public string InvestmentId { get; set; } = string.Empty;

        [BsonElement("score")]
        public double Score { get; set; }
    }
}