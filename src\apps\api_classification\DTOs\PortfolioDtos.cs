using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Pati.ClassificationService.DTOs
{
    /// <summary>
    /// DTO para receber dados de carteiras do UserService baseado no CSV inconformidades.csv
    /// </summary>
    public class PortfolioInputDto : IValidatableObject
    {
        /// <summary>
        /// Identificador do usuário
        /// </summary>
        [JsonPropertyName("userId")]
        public int? UserId { get; set; }

        /// <summary>
        /// Identificador do cliente
        /// </summary>
        [JsonPropertyName("clientId")]
        public int? ClientId { get; set; }

        /// <summary>
        /// Identificador da conta (ex.: 27)
        /// </summary>
        [Required]
        [JsonPropertyName("accountId")]
        public int AccountId { get; set; }

        /// <summary>
        /// Identificador único da carteira
        /// </summary>
        [Required]
        [JsonPropertyName("portfolioId")]
        public string PortfolioId { get; set; } = string.Empty;

        /// <summary>
        /// Nome do cliente
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Email do cliente
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }

        /// <summary>
        /// Telefone do cliente
        /// </summary>
        [JsonPropertyName("phoneNumber")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Perfil de risco do formulário
        /// </summary>
        [JsonPropertyName("riskProfileForm")]
        public string? RiskProfileForm { get; set; }

        /// <summary>
        /// Identificador do consultor
        /// </summary>
        [JsonPropertyName("advisorId")]
        public int? AdvisorId { get; set; }

        /// <summary>
        /// Lista de ativos na carteira
        /// </summary>
        [Required, MinLength(1)]
        [JsonPropertyName("assets")]
        public List<AssetInputDto> Assets { get; set; } = new();

        /// <summary>
        /// Lista de investimentos do portfólio
        /// </summary>
        [JsonPropertyName("investments")]
        public List<PortfolioInvestmentDto>? Investments { get; set; }

        /// <summary>
        /// Perfil da carteira (ex.: Moderado, Sofisticado, Conservador)
        /// </summary>
        [Required, StringLength(30)]
        [JsonPropertyName("portfolioProfile")]
        public string PortfolioProfile { get; set; } = string.Empty;

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (AccountId <= 0)
                yield return new ValidationResult("AccountId deve ser maior que zero.", new[] { nameof(AccountId) });

            if (string.IsNullOrWhiteSpace(PortfolioId))
                yield return new ValidationResult("PortfolioId é obrigatório.", new[] { nameof(PortfolioId) });

            if (Assets == null || Assets.Count == 0)
                yield return new ValidationResult("A carteira deve conter ao menos um ativo.", new[] { nameof(Assets) });

            if (string.IsNullOrWhiteSpace(PortfolioProfile))
                yield return new ValidationResult("Perfil da carteira é obrigatório.", new[] { nameof(PortfolioProfile) });

            var validProfiles = new[] { "Conservador", "Moderado", "Arrojado", "Sofisticado" };
            if (!validProfiles.Contains(PortfolioProfile))
                yield return new ValidationResult("Perfil da carteira deve ser: Conservador, Moderado, Arrojado ou Sofisticado.", new[] { nameof(PortfolioProfile) });
        }
    }

    /// <summary>
    /// DTO para representar um ativo na carteira baseado no CSV inconformidades.csv
    /// </summary>
    public class AssetInputDto : IValidatableObject
    {
        /// <summary>
        /// Identificador único do ativo (ex.: CRI CDIE_001)
        /// </summary>
        [Required]
        [JsonPropertyName("assetId")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Nome do ativo (ex.: CRI CDIE)
        /// </summary>
        [Required, StringLength(100)]
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Tipo do ativo (ex.: TITULOS RF PRIVADOS BRASIL)
        /// </summary>
        [Required, StringLength(100)]
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Tipo de renda (ex.: Renda Fixa, Renda Variável)
        /// </summary>
        [Required, StringLength(50)]
        [JsonPropertyName("incomeType")]
        public string IncomeType { get; set; } = string.Empty;

        /// <summary>
        /// Perfil de investimento (ex.: Conservador, Moderado, Arrojado)
        /// </summary>
        [Required, StringLength(30)]
        [JsonPropertyName("investmentProfile")]
        public string InvestmentProfile { get; set; } = string.Empty;

        /// <summary>
        /// Quantidade do ativo (ex.: 16.0)
        /// </summary>
        [JsonPropertyName("quantity")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// Valor financeiro (ex.: 16278.264089)
        /// </summary>
        [JsonPropertyName("value")]
        public decimal Value { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (string.IsNullOrWhiteSpace(AssetId))
                yield return new ValidationResult("AssetId is required.", new[] { nameof(AssetId) });

            if (string.IsNullOrWhiteSpace(Name))
                yield return new ValidationResult("Asset name is required.", new[] { nameof(Name) });

            if (string.IsNullOrWhiteSpace(Type))
                yield return new ValidationResult("Asset type is required.", new[] { nameof(Type) });

            if (string.IsNullOrWhiteSpace(IncomeType))
                yield return new ValidationResult("Income type is required.", new[] { nameof(IncomeType) });

            if (string.IsNullOrWhiteSpace(InvestmentProfile))
                yield return new ValidationResult("Investment profile is required.", new[] { nameof(InvestmentProfile) });

            var validIncomeTypes = new[] { "Renda Fixa", "Renda Variável" };
            if (!validIncomeTypes.Contains(IncomeType))
                yield return new ValidationResult("Income type must be: Renda Fixa or Renda Variável.", new[] { nameof(IncomeType) });

            var validInvestmentProfiles = new[] { "Conservador", "Moderado", "Arrojado", "Sofisticado" };
            if (!validInvestmentProfiles.Contains(InvestmentProfile))
                yield return new ValidationResult("Investment profile must be: Conservador, Moderado, Arrojado or Sofisticado.", new[] { nameof(InvestmentProfile) });
        }
    }

    /// <summary>
    /// DTO para enviar resultado da análise para RecommendationService conforme especificação JSON
    /// </summary>
    public class PortfolioOutputDto
    {
        /// <summary>
        /// Identificador do usuário
        /// </summary>
        [JsonPropertyName("userId")]
        public int? UserId { get; set; }

        /// <summary>
        /// Identificador do cliente
        /// </summary>
        [JsonPropertyName("clientId")]
        public int? ClientId { get; set; }

        /// <summary>
        /// Identificador da conta
        /// </summary>
        [JsonPropertyName("accountId")]
        public int AccountId { get; set; }

        /// <summary>
        /// Identificador único da carteira
        /// </summary>
        [JsonPropertyName("portfolioId")]
        public string PortfolioId { get; set; } = string.Empty;

        /// <summary>
        /// Nome do cliente
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Email do cliente
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }

        /// <summary>
        /// Telefone do cliente
        /// </summary>
        [JsonPropertyName("phoneNumber")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Perfil de risco do formulário
        /// </summary>
        [JsonPropertyName("riskProfileForm")]
        public string? RiskProfileForm { get; set; }

        /// <summary>
        /// Identificador do consultor
        /// </summary>
        [JsonPropertyName("advisorId")]
        public int? AdvisorId { get; set; }

        /// <summary>
        /// Lista de ativos na carteira
        /// </summary>
        [JsonPropertyName("assets")]
        public List<AssetOutputDto> Assets { get; set; } = new();

        /// <summary>
        /// Lista de investimentos do portfólio
        /// </summary>
        [JsonPropertyName("investments")]
        public List<PortfolioInvestmentDto>? Investments { get; set; }

        /// <summary>
        /// Lista de inconformidades identificadas
        /// </summary>
        [JsonPropertyName("inconsistencies")]
        public List<InconsistencyDto> Inconsistencies { get; set; } = new();

        /// <summary>
        /// Perfil da carteira (ex.: Moderado, Sofisticado, Conservador)
        /// </summary>
        [JsonPropertyName("portfolioProfile")]
        public string PortfolioProfile { get; set; } = string.Empty;

        /// <summary>
        /// Alocação de renda (proporções de Renda Fixa e Renda Variável)
        /// </summary>
        [JsonPropertyName("incomeAllocation")]
        public IncomeAllocationDto IncomeAllocation { get; set; } = new();
    }

    /// <summary>
    /// DTO para representar um ativo na saída conforme especificação JSON
    /// </summary>
    public class AssetOutputDto
    {
        /// <summary>
        /// Identificador do ativo
        /// </summary>
        [JsonPropertyName("assetId")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Nome do ativo
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Tipo do ativo
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Tipo de renda
        /// </summary>
        [JsonPropertyName("incomeType")]
        public string IncomeType { get; set; } = string.Empty;

        /// <summary>
        /// Perfil de investimento
        /// </summary>
        [JsonPropertyName("investmentProfile")]
        public string InvestmentProfile { get; set; } = string.Empty;

        /// <summary>
        /// Quantidade do ativo
        /// </summary>
        [JsonPropertyName("quantity")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// Valor financeiro
        /// </summary>
        [JsonPropertyName("value")]
        public decimal Value { get; set; }
    }

    /// <summary>
    /// DTO para representar uma inconformidade conforme especificação JSON
    /// </summary>
    public class InconsistencyDto
    {
        /// <summary>
        /// Identificador do ativo com inconformidade
        /// </summary>
        [JsonPropertyName("assetId")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Nome do ativo
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Descrição da inconformidade
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Severidade (ex: low, medium, high)
        /// </summary>
        [JsonPropertyName("severity")]
        public string Severity { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO para representar a alocação de renda (proporções de Renda Fixa e Renda Variável)
    /// </summary>
    public class IncomeAllocationDto
    {
        /// <summary>
        /// Proporção de Renda Fixa (ex.: 0.8)
        /// </summary>
        [JsonPropertyName("fixedIncome")]
        public decimal FixedIncome { get; set; }

        /// <summary>
        /// Proporção de Renda Variável (ex.: 0.2)
        /// </summary>
        [JsonPropertyName("variableIncome")]
        public decimal VariableIncome { get; set; }
    }

    /// <summary>
    /// DTO para representar um investimento no portfólio
    /// </summary>
    public class PortfolioInvestmentDto
    {
        /// <summary>
        /// Identificador do investimento
        /// </summary>
        [JsonPropertyName("investmentId")]
        public int InvestmentId { get; set; }

        /// <summary>
        /// Valor do investimento
        /// </summary>
        [JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Tipo do investimento
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Nome do investimento
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Descrição do investimento
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }
    }
}
