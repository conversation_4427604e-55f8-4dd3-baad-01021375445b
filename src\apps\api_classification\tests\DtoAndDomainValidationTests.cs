using Pati.ClassificationService.Domain;
using Pati.ClassificationService.DTOs;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Xunit;

namespace Pati.ClassificationService.Tests
{
    public class DtoAndDomainValidationTests
    {
        [Fact]
        public void PortfolioInputDto_ValidData_PassesValidation()
        {
            // Arrange
            var dto = new PortfolioInputDto
            {
                AccountId = 1,
                PortfolioId = "test-portfolio",
                PortfolioProfile = "Moderado",
                Assets = new List<AssetInputDto>
                {
                    new AssetInputDto
                    {
                        AssetId = "asset1",
                        Name = "Test Asset",
                        Type = "Test Type",
                        IncomeType = "Renda Fixa",
                        InvestmentProfile = "Conservador",
                        Quantity = 100,
                        Value = 1000
                    }
                }
            };

            // Act
            var validationResults = ValidateDto(dto);

            // Assert
            Assert.Empty(validationResults);
        }

        [Fact]
        public void PortfolioInputDto_InvalidAccountId_FailsValidation()
        {
            // Arrange
            var dto = new PortfolioInputDto
            {
                AccountId = 0, // Invalid
                PortfolioId = "test-portfolio",
                PortfolioProfile = "Moderado",
                Assets = new List<AssetInputDto>
                {
                    new AssetInputDto
                    {
                        AssetId = "asset1",
                        Name = "Test Asset",
                        Type = "Test Type",
                        IncomeType = "Renda Fixa",
                        InvestmentProfile = "Conservador"
                    }
                }
            };

            // Act
            var validationResults = ValidateDto(dto);

            // Assert
            Assert.NotEmpty(validationResults);
            Assert.Contains(validationResults, v => v.ErrorMessage != null && v.ErrorMessage.Contains("AccountId"));
        }

        [Fact]
        public void PortfolioInputDto_EmptyAssets_FailsValidation()
        {
            // Arrange
            var dto = new PortfolioInputDto
            {
                AccountId = 1,
                PortfolioId = "test-portfolio",
                PortfolioProfile = "Moderado",
                Assets = new List<AssetInputDto>() // Empty list
            };

            // Act
            var validationResults = ValidateDto(dto);

            // Assert
            Assert.NotEmpty(validationResults);
            Assert.Contains(validationResults, v => v.ErrorMessage != null && v.ErrorMessage.Contains("minimum length"));
        }

        [Fact]
        public void AssetInputDto_ValidData_PassesValidation()
        {
            // Arrange
            var dto = new AssetInputDto
            {
                AssetId = "asset1",
                Name = "Test Asset",
                Type = "Test Type",
                IncomeType = "Renda Fixa",
                InvestmentProfile = "Conservador",
                Quantity = 100,
                Value = 1000
            };

            // Act
            var validationResults = ValidateDto(dto);

            // Assert
            Assert.Empty(validationResults);
        }

        [Fact]
        public void AssetInputDto_InvalidIncomeType_FailsValidation()
        {
            // Arrange
            var dto = new AssetInputDto
            {
                AssetId = "asset1",
                Name = "Test Asset",
                Type = "Test Type",
                IncomeType = "Invalid Type", // Invalid
                InvestmentProfile = "Conservador",
                Quantity = 100,
                Value = 1000
            };

            // Act
            var validationResults = ValidateDto(dto);

            // Assert
            Assert.NotEmpty(validationResults);
            Assert.Contains(validationResults, v => v.ErrorMessage != null && v.ErrorMessage.Contains("Income type must be"));
        }

        [Fact]
        public void Portfolio_ValidData_CreatesSuccessfully()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Test Asset", "Test Type", "Renda Fixa", "Conservador", 100, 1000)
            };

            // Act
            var portfolio = new Portfolio(1, "test-portfolio", "Moderado", assets);

            // Assert
            Assert.Equal(1, portfolio.AccountId);
            Assert.Equal("test-portfolio", portfolio.PortfolioId);
            Assert.Equal("Moderado", portfolio.PortfolioProfile);
            Assert.Single(portfolio.Assets);
        }

        [Fact]
        public void Portfolio_IdentifyInconsistencies_ConservadorWithArrojado_ReturnsInconsistency()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Test Asset", "Test Type", "Renda Variável", "Arrojado", 100, 1000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Conservador", assets);

            // Act
            var inconsistencies = portfolio.IdentifyInconsistencies();

            // Assert
            Assert.NotEmpty(inconsistencies);
            Assert.Contains(inconsistencies, i => i.Description.Contains("incompatível"));
        }

        [Fact]
        public void Portfolio_CalculateIncomeProportions_MixedAssets_ReturnsCorrectProportions()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("asset1", "Fixed Asset", "Type1", "Renda Fixa", "Conservador", 100, 6000),
                new Asset("asset2", "Variable Asset", "Type2", "Renda Variável", "Moderado", 50, 4000)
            };
            var portfolio = new Portfolio(1, "test-portfolio", "Moderado", assets);

            // Act
            var proportions = portfolio.CalculateIncomeProportions();

            // Assert
            Assert.Equal(0.6m, proportions.FixedIncome);
            Assert.Equal(0.4m, proportions.VariableIncome);
        }

        [Fact]
        public void Asset_ValidData_CreatesSuccessfully()
        {
            // Act
            var asset = new Asset("asset1", "Test Asset", "Test Type", "Renda Fixa", "Conservador", 100, 1000);

            // Assert
            Assert.Equal("asset1", asset.AssetId);
            Assert.Equal("Test Asset", asset.Name);
            Assert.Equal("Renda Fixa", asset.IncomeType);
            Assert.Equal("Conservador", asset.InvestmentProfile);
            Assert.Equal(100, asset.Quantity);
            Assert.Equal(1000, asset.Value);
        }

        [Fact]
        public void Inconsistency_ValidData_CreatesSuccessfully()
        {
            // Act
            var inconsistency = new Inconsistency("asset1", "Test Asset", "Test description", "high");

            // Assert
            Assert.Equal("asset1", inconsistency.AssetId);
            Assert.Equal("Test Asset", inconsistency.Name);
            Assert.Equal("Test description", inconsistency.Description);
            Assert.Equal("high", inconsistency.Severity);
        }

        [Fact]
        public void ClientRecommendation_AcceptRecommendation_SetsAcceptedTrue()
        {
            // Arrange
            var recommendation = new ClientRecommendation
            {
                RecommendationId = 1,
                ClientId = 1,
                InvestmentId = 1,
                RecommendationScore = 0.8,
                Description = "Test recommendation"
            };

            // Act
            recommendation.Accept("Accepted by client");

            // Assert
            Assert.True(recommendation.Accepted);
            Assert.NotNull(recommendation.ResponseDate);
            Assert.Equal("Accepted by client", recommendation.ResponseJustification);
        }

        [Fact]
        public void ClientRecommendation_RejectRecommendation_SetsAcceptedFalse()
        {
            // Arrange
            var recommendation = new ClientRecommendation
            {
                RecommendationId = 1,
                ClientId = 1,
                InvestmentId = 1,
                RecommendationScore = 0.8,
                Description = "Test recommendation"
            };

            // Act
            recommendation.Reject("Rejected by client");

            // Assert
            Assert.False(recommendation.Accepted);
            Assert.NotNull(recommendation.ResponseDate);
            Assert.Equal("Rejected by client", recommendation.ResponseJustification);
        }

        private static List<ValidationResult> ValidateDto(object dto)
        {
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(dto);
            Validator.TryValidateObject(dto, validationContext, validationResults, true);
            return validationResults;
        }
    }
}
