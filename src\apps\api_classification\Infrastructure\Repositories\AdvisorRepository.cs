using Microsoft.EntityFrameworkCore;
using Pati.ClassificationService.Application.Repositories;
using Pati.ClassificationService.Domain;
using Pati.ClassificationService.Infrastructure.Data;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pati.ClassificationService.Infrastructure.Repositories
{
    public class AdvisorRepository : IAdvisorRepository
    {
        private readonly ClassificationDbContext _context;

        public AdvisorRepository(ClassificationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<Advisor?> GetByIdAsync(int advisorId)
        {
            return await _context.Advisors
                .FirstOrDefaultAsync(a => a.AdvisorId == advisorId);
        }

        public async Task<IEnumerable<Advisor>> GetAllAsync()
        {
            return await _context.Advisors.ToListAsync();
        }

        public async Task AddAsync(Advisor advisor)
        {
            if (advisor == null)
                throw new ArgumentNullException(nameof(advisor));

            await _context.Advisors.AddAsync(advisor);
        }

        public async Task UpdateAsync(Advisor advisor)
        {
            if (advisor == null)
                throw new ArgumentNullException(nameof(advisor));

            _context.Advisors.Update(advisor);
            await Task.CompletedTask;
        }

        public async Task DeleteAsync(int advisorId)
        {
            var advisor = await _context.Advisors.FindAsync(advisorId);
            if (advisor != null)
            {
                _context.Advisors.Remove(advisor);
            }
        }

        public async Task<bool> ExistsAsync(int advisorId)
        {
            return await _context.Advisors.AnyAsync(a => a.AdvisorId == advisorId);
        }
    }
}
