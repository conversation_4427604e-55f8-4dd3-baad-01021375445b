using System.Collections.Generic;
using System.Linq;

namespace Pati.ClassificationService.Domain
{
    public class Advisor
    {
        public int AdvisorId { get; set; }
        public string Uid { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public List<Client> Clients { get; set; } = new List<Client>();

        public Advisor(int advisorId, string uid, string name, string email, string phoneNumber)
        {
            AdvisorId = advisorId;
            Uid = uid;
            Name = name;
            Email = email;
            PhoneNumber = phoneNumber;
            Clients = new List<Client>();
        }

        public void AddClient(Client client)
        {
            if (client == null)
                throw new System.ArgumentNullException(nameof(client));
            if (!Clients.Any(c => c.ClientId == client.ClientId))
                Clients.Add(client);
        }

        public void RemoveClient(int clientId)
        {
            var client = Clients.FirstOrDefault(c => c.ClientId == clientId);
            if (client != null)
                Clients.Remove(client);
        }

        public Client? FindClientById(int clientId)
        {
            return Clients.FirstOrDefault(c => c.ClientId == clientId);
        }

        public bool HasNonCompliantClients()
        {
            return Clients.Any(c => c.NonCompliance);
        }

        public double GetNonCompliantPercentage()
        {
            if (Clients.Count == 0) return 0;
            return (double)Clients.Count(c => c.NonCompliance) / Clients.Count * 100;
        }

        public IEnumerable<Client> GetClientsByRiskProfile(RiskProfile profile)
        {
            return Clients.Where(c => c.RiskProfileForm.ToRiskProfile() == profile);
        }

        public IEnumerable<Client> GetClientList()
        {
            return Clients;
        }

        public IEnumerable<Client> GetNonCompliantClients()
        {
            return Clients.Where(c => c.NonCompliance);
        }

        public object GetDashboardData()
        {
            //Retorna dados resumidos para dashboard
            return new {
                TotalClients = Clients.Count,
                NonCompliantCount = Clients.Count(c => c.NonCompliance),
                NonCompliantPercent = Clients.Count == 0 ? 0 : (double)Clients.Count(c => c.NonCompliance) / Clients.Count * 100
            };
        }
    }
}