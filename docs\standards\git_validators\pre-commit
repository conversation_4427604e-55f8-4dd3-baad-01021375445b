#!/bin/sh

# Obtém o nome da branch atual
BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD)

# Define as branches protegidas
PROTECTED_BRANCHES="main develop"

# Verifica se o commit está sendo feito em uma branch protegida
if echo "$PROTECTED_BRANCHES" | grep -w "$BRANCH_NAME" > /dev/null; then
  echo "⛔ Commit direto na branch '$BRANCH_NAME' não é permitido. Crie uma branch de feature, bugfix ou hotfix."
  exit 1
fi
