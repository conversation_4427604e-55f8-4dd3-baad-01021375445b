using Microsoft.AspNetCore.Mvc;
using ApiMlRecommender.Services;
using ApiMlRecommender.DTOs;
using System.Threading.Tasks;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;

using Microsoft.Extensions.Configuration;
using MongoDB.Bson;
using MongoDB.Driver;

namespace ApiMlRecommender.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class RecomendacaoController : ControllerBase
    {
        private readonly IRecomendacaoService _recomendacaoService;
        private readonly MongoDbService _mongoDbService;
        private readonly PostgreSqlService _postgreSqlService;
        private readonly ILogger<RecomendacaoController> _logger;

        public RecomendacaoController(
            IRecomendacaoService recomendacaoService,
            MongoDbService mongoDbService,
            PostgreSqlService postgreSqlService,
            ILogger<RecomendacaoController> logger)
        {
            _recomendacaoService = recomendacaoService;
            _mongoDbService = mongoDbService;
            _postgreSqlService = postgreSqlService;
            _logger = logger;
        }

        [HttpGet("test-mongo")]
        public async Task<IActionResult> TestMongo([FromServices] IConfiguration config)
        {
            var connStr = config.GetConnectionString("MongoDB");

            try
            {
                var client = new MongoClient(connStr);
                var database = client.GetDatabase("test_db"); // Pode usar qualquer nome
                var collection = database.GetCollection<BsonDocument>("test_collection");

                var testDoc = new BsonDocument
            {
                { "timestamp", BsonDateTime.Create(DateTime.UtcNow) },
                { "message", "ping" }
            };

                await collection.InsertOneAsync(testDoc);

                var result = await collection.Find(new BsonDocument()).FirstOrDefaultAsync();

                return Ok(new
                {
                    status = "ok",
                    inserted = BsonTypeMapper.MapToDotNetValue(testDoc),
                    retrieved = BsonTypeMapper.MapToDotNetValue(result)
                });

            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost("gerar")]
        [ProducesResponseType(typeof(RecomendacaoResponseDto), 200)]
        [ProducesResponseType(typeof(string), 400)]
        [ProducesResponseType(typeof(string), 500)]
        public async Task<IActionResult> GerarRecomendacao([FromBody] RecomendacaoRequestDto request)
        {
            try
            {
                _logger.LogInformation("Gerando recomendação para cliente: {Cpf}", request?.Cpf ?? "CPF não informado");

                if (request == null || string.IsNullOrWhiteSpace(request.Cpf) || request.Investimentos == null || !request.Investimentos.Any())
                {
                    _logger.LogWarning("Requisição inválida para cliente: {Cpf}", request?.Cpf ?? "desconhecido");
                    return BadRequest("Dados da requisição são obrigatórios.");
                }

                var carteiras = await _postgreSqlService.GetCarteirasInconformesAsync(request.Cpf);
                var carteiraCliente = carteiras.FirstOrDefault();
                if (carteiraCliente == null)
                {
                    _logger.LogWarning("Carteira não inconforme para cliente: {Cpf}", request.Cpf);
                    return BadRequest("Recomendações só para carteiras inconformes.");
                }

                request.ComplianceStatus = carteiraCliente.ComplianceStatus;

                var recomendacao = await _recomendacaoService.GerarRecomendacaoAsync(request);
                _logger.LogInformation("Recomendação gerada para cliente: {Cpf}", request.Cpf);
                return Ok(recomendacao);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Erro de validação ao gerar recomendação");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro interno ao gerar recomendação para cliente: {Cpf}", request?.Cpf ?? "desconhecido");
                return StatusCode(500, "Erro interno do servidor.");
            }
        }

        [HttpPost("score")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(typeof(string), 400)]
        [ProducesResponseType(typeof(string), 500)]
        public IActionResult CalcularScore([FromBody] ScoreAdequacaoRequestDto request)
        {
            try
            {
                _logger.LogInformation("Calculando score para perfil: {Perfil}", request?.PerfilRisco ?? "não informado");

                if (request == null || string.IsNullOrWhiteSpace(request.PerfilRisco) || request.Investimentos == null || !request.Investimentos.Any())
                {
                    return BadRequest("Dados da requisição são obrigatórios.");
                }

                var score = _recomendacaoService.CalcularScoreAdequacao(request.Investimentos, request.PerfilRisco);
                _logger.LogInformation("Score calculado: {Score}", score);

                return Ok(new
                {
                    Score = score,
                    PerfilRisco = request.PerfilRisco,
                    TotalInvestimentos = request.Investimentos.Count,
                    DataCalculo = DateTime.Now
                });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Erro de validação ao calcular score");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro interno ao calcular score");
                return StatusCode(500, "Erro interno do servidor.");
            }
        }

        [HttpPost("iniciar")]
        [ProducesResponseType(typeof(object), 202)]
        [ProducesResponseType(typeof(string), 500)]
        public async Task<IActionResult> IniciarRecomendacao()
        {
            try
            {
                _logger.LogInformation("Iniciando geração de recomendações para carteiras inconformes");
                await _recomendacaoService.IniciarRecomendacoesAsync();
                _logger.LogInformation("Processamento de recomendações concluído");
                return Accepted(new
                {
                    Status = "Iniciado",
                    Message = "Geração de recomendações iniciada",
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao iniciar geração de recomendações");
                return StatusCode(500, "Erro interno do servidor.");
            }
        }

        [HttpGet("teste")]
        [ProducesResponseType(typeof(object), 200)]
        public IActionResult Teste()
        {
            _logger.LogInformation("Endpoint de teste acessado");
            return Ok(new
            {
                Mensagem = "API de Recomendação funcionando!",
                Timestamp = DateTime.Now,
                Versao = "1.0.0"
            });
        }

        [HttpGet("cliente/{clientId}")]
        [ProducesResponseType(typeof(RecomendacaoInvestimento), 200)]
        [ProducesResponseType(typeof(string), 404)]
        public async Task<IActionResult> BuscarRecomendacaoCliente(string clientId)
        {
            try
            {
                var recomendacao = await _mongoDbService.GetRecomendacaoClienteAsync(clientId);
                if (recomendacao == null)
                    return NotFound("Nenhuma recomendação encontrada.");
                return Ok(recomendacao);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar recomendação do cliente: {ClientId}", clientId);
                return StatusCode(500, "Erro interno do servidor.");
            }
        }
    }

    public class ScoreAdequacaoRequestDto
    {
        public List<InvestimentoDto> Investimentos { get; set; } = new List<InvestimentoDto>();
        public string PerfilRisco { get; set; } = string.Empty;
    }
}