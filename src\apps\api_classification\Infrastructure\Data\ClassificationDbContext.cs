using Microsoft.EntityFrameworkCore;
using Pati.ClassificationService.Domain;

namespace Pati.ClassificationService.Infrastructure.Data
{
    public class ClassificationDbContext : DbContext
    {
        public ClassificationDbContext(DbContextOptions<ClassificationDbContext> options) : base(options)
        {
        }

        public DbSet<Client> Clients { get; set; }
        public DbSet<Investment> Investments { get; set; }
        public DbSet<ClientInvestment> ClientInvestments { get; set; }
        public DbSet<Advisor> Advisors { get; set; }
        public DbSet<ClientRecommendation> ClientRecommendations { get; set; }
        public DbSet<ClassificationRule> ClassificationRules { get; set; }
        public DbSet<ComplianceHistory> ComplianceHistory { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configuração da entidade Client
            modelBuilder.Entity<Client>(entity =>
            {
                entity.ToTable("client"); // Mapear para a tabela CLIENT no PostgreSQL
                entity.HasKey(e => e.ClientId);
                entity.Property(e => e.ClientId).HasColumnName("client_id");
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100).HasColumnName("name");
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100).HasColumnName("email");
                entity.Property(e => e.PhoneNumber).HasMaxLength(20).HasColumnName("phone_number").IsRequired(false);
                entity.Property(e => e.RiskProfileForm).HasMaxLength(50).HasColumnName("risk_profile_form");
                entity.Property(e => e.RiskProfileWallet).HasMaxLength(50).HasColumnName("risk_profile_wallet");
                entity.Property(e => e.NonCompliance)
                      .HasColumnName("compliance")
                      .HasConversion(
                          v => !v, // NonCompliance true -> compliance false
                          v => !v  // compliance false -> NonCompliance true
                      );
                entity.Property(e => e.AdvisorId).HasColumnName("advisor_id");

                // Relacionamento com Advisor (simplificado para evitar conflitos)
                // entity.HasOne<Advisor>()
                //       .WithMany()
                //       .HasForeignKey(e => e.AdvisorId)
                //       .OnDelete(DeleteBehavior.Restrict);
            });

            // Configuração da entidade Investment
            modelBuilder.Entity<Investment>(entity =>
            {
                entity.ToTable("investment"); // Mapear para a tabela investment no PostgreSQL
                entity.HasKey(e => e.InvestmentId);
                entity.Property(e => e.InvestmentId).HasColumnName("investment_id");
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100).HasColumnName("name");
                entity.Property(e => e.Type).IsRequired().HasMaxLength(50).HasColumnName("type");
                entity.Property(e => e.Risk).IsRequired().HasMaxLength(50).HasColumnName("risk");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            });

            // Configuração da entidade ClientInvestment
            modelBuilder.Entity<ClientInvestment>(entity =>
            {
                entity.ToTable("client_investment"); // Mapear para a tabela client_investment no PostgreSQL
                entity.HasKey(e => new { e.ClientId, e.InvestmentId });
                entity.Property(e => e.ClientId).HasColumnName("client_id");
                entity.Property(e => e.InvestmentId).HasColumnName("investment_id");
                entity.Property(e => e.Quantity).IsRequired().HasColumnName("quantity");
                entity.Property(e => e.InvestedAmount).HasColumnType("decimal(25,2)").HasColumnName("invested_amount");
                entity.Property(e => e.InvestmentDate).IsRequired().HasColumnName("investment_date");

                // Relacionamentos (simplificados para evitar conflitos)
                // entity.HasOne<Client>()
                //       .WithMany(c => c.Investments)
                //       .HasForeignKey(e => e.ClientId)
                //       .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Investment)
                      .WithMany()
                      .HasForeignKey(e => e.InvestmentId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configuração da entidade Advisor
            modelBuilder.Entity<Advisor>(entity =>
            {
                entity.ToTable("advisor"); // Mapear para a tabela advisor no PostgreSQL
                entity.HasKey(e => e.AdvisorId);
                entity.Property(e => e.AdvisorId).HasColumnName("advisor_id");
                entity.Property(e => e.Uid).IsRequired().HasMaxLength(128).HasColumnName("uid");
                entity.Property(e => e.Name).HasMaxLength(100).HasColumnName("name");
                entity.Property(e => e.Email).HasMaxLength(100).HasColumnName("email");
                entity.Property(e => e.PhoneNumber).HasMaxLength(20).HasColumnName("phone_number");
            });

            // Configuração da entidade ClientRecommendation
            modelBuilder.Entity<ClientRecommendation>(entity =>
            {
                entity.HasKey(e => e.RecommendationId);
                entity.Property(e => e.RecommendationType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Justification).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).IsRequired();

                // Relacionamento com Client
                entity.HasOne<Client>()
                      .WithMany(c => c.Recommendations)
                      .HasForeignKey(e => e.ClientId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configuração da entidade ClassificationRule
            modelBuilder.Entity<ClassificationRule>(entity =>
            {
                entity.HasKey(e => e.RuleId);
                entity.Property(e => e.RiskProfile).IsRequired().HasMaxLength(30);
                entity.Property(e => e.AllowedAssetTypes).IsRequired().HasMaxLength(200);
                entity.Property(e => e.MaxAllocationPercentage).HasColumnType("decimal(5,2)");
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.IsActive).IsRequired();
                entity.Property(e => e.CreatedAt).IsRequired();
            });

            // Configuração da entidade ComplianceHistory - apenas campos que existem no banco
            modelBuilder.Entity<ComplianceHistory>(entity =>
            {
                entity.ToTable("compliance_history"); // Mapear para a tabela compliance_history no PostgreSQL
                entity.HasKey(e => e.HistoryId);
                entity.Property(e => e.HistoryId).HasColumnName("history_id");
                entity.Property(e => e.ClientId).IsRequired().HasColumnName("client_id");
                entity.Property(e => e.PreviousStatus).IsRequired().HasColumnName("previous_status");
                entity.Property(e => e.NewStatus).IsRequired().HasColumnName("new_status");
                entity.Property(e => e.ChangeDate).IsRequired().HasColumnName("change_date");
                // Campos removidos: reason, previous_risk_profile, new_risk_profile (não existem no banco)

                // Relacionamento com Client
                entity.HasOne<Client>()
                      .WithMany()
                      .HasForeignKey(e => e.ClientId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Seed data removido - usar dados do PostgreSQL
        }

        /// <summary>
        /// Usa a função PostgreSQL calculate_wallet_risk_profile existente no banco
        /// Esta função já está implementada no esquema do banco
        /// </summary>
        public async Task<string?> GetWalletRiskProfileAsync(int clientId)
        {
            try
            {
                // Usar a função PostgreSQL existente no banco
                var result = await Database.SqlQueryRaw<string>(
                    "SELECT calculate_wallet_risk_profile(@p0)",
                    clientId).FirstOrDefaultAsync();
                return result;
            }
            catch (Exception)
            {
                // Se a função PostgreSQL falhar, retornar null
                return null;
            }
        }

        /// <summary>
        /// Chama a função PostgreSQL is_investment_compatible_with_profile
        /// </summary>
        public async Task<bool> ValidateInvestmentCompatibilityAsync(string clientProfile, string investmentRisk)
        {
            // Usar lógica local diretamente para evitar problemas com função PostgreSQL
            return await Task.FromResult(IsCompatibleLocally(clientProfile, investmentRisk));
        }

        /// <summary>
        /// Lógica local de compatibilidade como fallback
        /// </summary>
        private bool IsCompatibleLocally(string clientProfile, string investmentRisk)
        {
            var compatibilityMatrix = new Dictionary<string, List<string>>
            {
                ["Conservador"] = new List<string> { "Conservador" },
                ["Moderado"] = new List<string> { "Conservador", "Moderado" },
                ["Arrojado"] = new List<string> { "Conservador", "Moderado", "Arrojado" },
                ["Sofisticado"] = new List<string> { "Conservador", "Moderado", "Arrojado", "Sofisticado" }
            };

            return compatibilityMatrix.ContainsKey(clientProfile) &&
                   compatibilityMatrix[clientProfile].Contains(investmentRisk);
        }


    }
}
