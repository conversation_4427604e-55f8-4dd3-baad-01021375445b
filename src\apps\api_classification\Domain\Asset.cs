using System;
using System.ComponentModel.DataAnnotations;

namespace Pati.ClassificationService.Domain
{
    /// <summary>
    /// Entidade que representa um ativo financeiro baseado no CSV inconformidades.csv
    /// </summary>
    public class Asset
    {
        public string AssetId { get; private set; } = string.Empty;
        public string Name { get; private set; } = string.Empty;
        public string Type { get; private set; } = string.Empty;
        public string IncomeType { get; private set; } = string.Empty;
        public string InvestmentProfile { get; private set; } = string.Empty;
        public decimal Quantity { get; private set; }
        public decimal Value { get; private set; }
        public DateTime CreatedAt { get; private set; }
        public DateTime? UpdatedAt { get; private set; }

        // Construtor padrão para Entity Framework
        protected Asset() { }

        public Asset(string assetId, string name, string type, string incomeType, string investmentProfile, decimal quantity, decimal value)
        {
            if (string.IsNullOrWhiteSpace(assetId))
                throw new ArgumentException("Asset ID is required.", nameof(assetId));
            
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Asset name is required.", nameof(name));
            
            if (string.IsNullOrWhiteSpace(type))
                throw new ArgumentException("Asset type is required.", nameof(type));
            
            if (string.IsNullOrWhiteSpace(incomeType))
                throw new ArgumentException("Income type is required.", nameof(incomeType));
            
            if (string.IsNullOrWhiteSpace(investmentProfile))
                throw new ArgumentException("Investment profile is required.", nameof(investmentProfile));

            var validIncomeTypes = new[] { "Renda Fixa", "Renda Variável" };
            if (!validIncomeTypes.Contains(incomeType))
                throw new ArgumentException("Invalid income type.", nameof(incomeType));

            var validInvestmentProfiles = new[] { "Conservador", "Moderado", "Arrojado", "Sofisticado" };
            if (!validInvestmentProfiles.Contains(investmentProfile))
                throw new ArgumentException("Invalid investment profile.", nameof(investmentProfile));

            AssetId = assetId;
            Name = name;
            Type = type;
            IncomeType = incomeType;
            InvestmentProfile = investmentProfile;
            Quantity = quantity;
            Value = value;
            CreatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Verifica se o ativo é compatível com um perfil de carteira específico
        /// </summary>
        public bool IsCompatibleWithPortfolioProfile(string portfolioProfile)
        {
            return Portfolio.IsRiskAllowedForProfile(portfolioProfile, InvestmentProfile);
        }

        /// <summary>
        /// Calcula o valor absoluto do ativo (para lidar com valores negativos)
        /// </summary>
        public decimal GetAbsoluteValue()
        {
            return Math.Abs(Value);
        }

        /// <summary>
        /// Verifica se o ativo é de Renda Fixa
        /// </summary>
        public bool IsFixedIncome()
        {
            return IncomeType == "Renda Fixa";
        }

        /// <summary>
        /// Verifica se o ativo é de Renda Variável
        /// </summary>
        public bool IsVariableIncome()
        {
            return IncomeType == "Renda Variável";
        }

        /// <summary>
        /// Obtém o nível de risco baseado no tipo de ativo
        /// </summary>
        public string GetRiskLevel()
        {
            // Mapeamento baseado no arquivo Tipo_ativos_com_risco.csv
            var riskMapping = new Dictionary<string, string>
            {
                { "TITULOS RF PRIVADOS BRASIL", "Moderado" },
                { "DEBENTURES BRASIL", "Moderado" },
                { "ACOES BRASIL", "Sofisticado" },
                { "TITULOS RF PUBLICOS BRASIL", "Conservador" },
                { "CUPOM DE IPCA FUTURO", "Sofisticado" },
                { "CDI DE UM DIA - FUTURO", "Sofisticado" },
                { "AÇÕES INTERNACIONAIS", "Sofisticado" },
                { "TITULOS RF INTERNACIONAL", "Moderado" },
                { "TIME DEPOSIT", "Conservador" },
                { "EUROBONDS", "Moderado" },
                { "CCMF", "Conservador" }
            };

            return riskMapping.ContainsKey(Type) ? riskMapping[Type] : "Moderado";
        }

        /// <summary>
        /// Verifica se o ativo está em conformidade com as regras CVM 175
        /// </summary>
        public bool IsCompliantWithCVM175(string portfolioProfile)
        {
            // Verifica se o perfil de investimento do ativo é compatível com o perfil da carteira
            if (!IsCompatibleWithPortfolioProfile(portfolioProfile))
                return false;

            // Verifica se o tipo de renda é apropriado para o perfil
            if (portfolioProfile == "Conservador" && IsVariableIncome())
                return false;

            return true;
        }

        /// <summary>
        /// Atualiza os dados do ativo
        /// </summary>
        public void Update(string? name = null, string? type = null, string? incomeType = null, 
                          string? investmentProfile = null, decimal? quantity = null, decimal? value = null)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            if (!string.IsNullOrWhiteSpace(type))
                Type = type;

            if (!string.IsNullOrWhiteSpace(incomeType))
            {
                var validIncomeTypes = new[] { "Renda Fixa", "Renda Variável" };
                if (!validIncomeTypes.Contains(incomeType))
                    throw new ArgumentException("Invalid income type.", nameof(incomeType));
                IncomeType = incomeType;
            }

            if (!string.IsNullOrWhiteSpace(investmentProfile))
            {
                var validInvestmentProfiles = new[] { "Conservador", "Moderado", "Arrojado", "Sofisticado" };
                if (!validInvestmentProfiles.Contains(investmentProfile))
                    throw new ArgumentException("Invalid investment profile.", nameof(investmentProfile));
                InvestmentProfile = investmentProfile;
            }

            if (quantity.HasValue)
                Quantity = quantity.Value;

            if (value.HasValue)
                Value = value.Value;

            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Calcula o valor total do ativo (quantidade * valor unitário)
        /// </summary>
        public decimal CalculateTotalValue()
        {
            return Quantity * GetAbsoluteValue();
        }

        public override string ToString()
        {
            return $"Asset {AssetId} - {Name} ({Type}) - {IncomeType} - Profile: {InvestmentProfile} - Value: {Value:C}";
        }

        public override bool Equals(object? obj)
        {
            if (obj is Asset other)
                return AssetId == other.AssetId;
            return false;
        }

        public override int GetHashCode()
        {
            return AssetId.GetHashCode();
        }
    }
}
