{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information"}}, "Urls": "http://localhost:8080", "ServiceConfiguration": {"BaseUrl": "http://localhost:8080", "ServiceName": "Pati.ClassificationService", "Version": "1.0.0"}, "RecommendationService": {"BaseUrl": "http://localhost:5001", "TimeoutSeconds": 30}, "ClassificationJob": {"Enabled": true, "IntervalMinutes": 1440, "RunOnStartup": true, "TimeoutMinutes": 60, "DailyExecutionTime": "02:00"}}