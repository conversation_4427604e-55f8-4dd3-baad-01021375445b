using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Pati.ClassificationService.DTOs;
using Pati.ClassificationService.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Testcontainers.PostgreSql;
using Xunit;

namespace Pati.ClassificationService.Tests
{
    /// <summary>
    /// Testes end-to-end para o api_classification usando PostgreSQL real
    /// </summary>
    public class EndToEndTests : IAsyncLifetime
    {
        private readonly PostgreSqlContainer _postgresContainer;
        private WebApplicationFactory<Program>? _factory;
        private HttpClient? _client;

        public EndToEndTests()
        {
            _postgresContainer = new PostgreSqlBuilder()
                .WithDatabase("test_db")
                .WithUsername("test_user")
                .WithPassword("test_password")
                .Build();
        }

        public async Task InitializeAsync()
        {
            await _postgresContainer.StartAsync();

            _factory = new WebApplicationFactory<Program>()
                .WithWebHostBuilder(builder =>
                {
                    builder.ConfigureServices(services =>
                    {
                        // Remove the existing DbContext registration
                        var descriptor = services.BuildServiceProvider()
                            .GetService<DbContextOptions<ClassificationDbContext>>();
                        if (descriptor != null)
                        {
                            services.Remove(new ServiceDescriptor(typeof(DbContextOptions<ClassificationDbContext>), descriptor));
                        }

                        // Add PostgreSQL database for testing
                        services.AddDbContext<ClassificationDbContext>(options =>
                        {
                            options.UseNpgsql(_postgresContainer.GetConnectionString());
                        });

                        // Build the service provider
                        var sp = services.BuildServiceProvider();

                        // Create a scope to obtain a reference to the database context
                        using var scope = sp.CreateScope();
                        var scopedServices = scope.ServiceProvider;
                        var db = scopedServices.GetRequiredService<ClassificationDbContext>();
                        var logger = scopedServices.GetRequiredService<ILogger<EndToEndTests>>();

                        try
                        {
                            // Ensure the database is created and seeded
                            db.Database.EnsureCreated();
                            SeedDatabase(db);
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, "An error occurred setting up the test database. Error: {Message}", ex.Message);
                        }
                    });
                });

            _client = _factory.CreateClient();
        }

        public async Task DisposeAsync()
        {
            _client?.Dispose();
            _factory?.Dispose();
            await _postgresContainer.DisposeAsync();
        }

        [Fact]
        public async Task ClassifyPortfolio_EndToEnd_WithPostgreSQL()
        {
            // Arrange
            var portfolioInput = new PortfolioInputDto
            {
                AccountId = 1,
                PortfolioId = "e2e-portfolio-001",
                PortfolioProfile = "Moderado",
                Assets = new List<AssetInputDto>
                {
                    new AssetInputDto
                    {
                        AssetId = "E2E_ASSET_001",
                        Name = "End-to-End Test Asset",
                        Type = "bond",
                        IncomeType = "Renda Fixa",
                        InvestmentProfile = "Conservador",
                        Quantity = 50,
                        Value = 5000
                    },
                    new AssetInputDto
                    {
                        AssetId = "E2E_ASSET_002",
                        Name = "End-to-End Test Stock",
                        Type = "stock",
                        IncomeType = "Renda Variável",
                        InvestmentProfile = "Arrojado",
                        Quantity = 20,
                        Value = 2000
                    }
                }
            };

            var json = JsonSerializer.Serialize(portfolioInput, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client!.PostAsync("/internal/v1/portfolios/classify", content);

            // Assert
            Assert.True(response.IsSuccessStatusCode);
            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<PortfolioOutputDto>(responseContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            Assert.NotNull(result);
            Assert.Equal(portfolioInput.AccountId, result.AccountId);
            Assert.Equal(portfolioInput.PortfolioId, result.PortfolioId);
            Assert.NotNull(result.IncomeAllocation);
            Assert.True(result.IncomeAllocation.FixedIncome > 0);
            Assert.True(result.IncomeAllocation.VariableIncome > 0);
        }

        [Fact]
        public async Task ClassifyAllPortfolios_EndToEnd_ProcessesAllClients()
        {
            // Act
            var response = await _client!.PostAsync("/internal/v1/portfolios/classify-all", null);

            // Assert
            Assert.True(response.IsSuccessStatusCode);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            var result = JsonSerializer.Deserialize<JsonElement>(responseContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            Assert.True(result.TryGetProperty("message", out var messageProperty));
            Assert.Contains("Batch classification completed successfully", messageProperty.GetString());
        }

        [Fact]
        public async Task HealthCheck_EndToEnd_ReturnsHealthy()
        {
            // Act
            var response = await _client!.GetAsync("/health");

            // Assert
            Assert.True(response.IsSuccessStatusCode);
            var responseContent = await response.Content.ReadAsStringAsync();
            Assert.Contains("healthy", responseContent);
        }

        [Fact]
        public async Task SwaggerEndpoint_EndToEnd_ReturnsSwaggerJson()
        {
            // Act
            var response = await _client!.GetAsync("/swagger/v1/swagger.json");

            // Assert
            Assert.True(response.IsSuccessStatusCode);
            var responseContent = await response.Content.ReadAsStringAsync();
            Assert.Contains("Pati.ClassificationService API", responseContent);
        }

        [Fact]
        public async Task CompleteWorkflow_EndToEnd_ClassifyAndVerifyDatabase()
        {
            // Arrange
            var portfolioInput = new PortfolioInputDto
            {
                AccountId = 2, // Use different client for this test
                PortfolioId = "workflow-portfolio-001",
                PortfolioProfile = "Conservador",
                Assets = new List<AssetInputDto>
                {
                    new AssetInputDto
                    {
                        AssetId = "WORKFLOW_ASSET_001",
                        Name = "Workflow Test Bond",
                        Type = "bond",
                        IncomeType = "Renda Fixa",
                        InvestmentProfile = "Conservador",
                        Quantity = 100,
                        Value = 10000
                    }
                }
            };

            var json = JsonSerializer.Serialize(portfolioInput, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act - Classify portfolio
            var classifyResponse = await _client!.PostAsync("/internal/v1/portfolios/classify", content);

            // Assert - Verify classification response
            Assert.True(classifyResponse.IsSuccessStatusCode);
            var classifyContent = await classifyResponse.Content.ReadAsStringAsync();
            var classifyResult = JsonSerializer.Deserialize<PortfolioOutputDto>(classifyContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            Assert.NotNull(classifyResult);
            Assert.Equal(portfolioInput.AccountId, classifyResult.AccountId);

            // Verify database was updated (would need direct database access in real scenario)
            // For now, we verify the response contains expected data
            Assert.NotNull(classifyResult.IncomeAllocation);
            Assert.Equal(1.0m, classifyResult.IncomeAllocation.FixedIncome); // 100% fixed income for conservative portfolio
        }

        private static void SeedDatabase(ClassificationDbContext context)
        {
            // Add test advisors
            var advisor1 = new Domain.Advisor(1, "advisor_e2e_1", "E2E Test Advisor 1", "<EMAIL>", "*********");
            var advisor2 = new Domain.Advisor(2, "advisor_e2e_2", "E2E Test Advisor 2", "<EMAIL>", "*********");
            context.Advisors.AddRange(advisor1, advisor2);

            // Add test clients
            var client1 = new Domain.Client
            {
                ClientId = 1,
                Name = "E2E Test Client 1",
                Email = "<EMAIL>",
                PhoneNumber = "*********",
                RiskProfileForm = "Moderado",
                RiskProfileWallet = "Moderado",
                AdvisorId = 1,
                NonCompliance = false // false significa que está em compliance
            };

            var client2 = new Domain.Client
            {
                ClientId = 2,
                Name = "E2E Test Client 2",
                Email = "<EMAIL>",
                PhoneNumber = "*********",
                RiskProfileForm = "Conservador",
                RiskProfileWallet = "Conservador",
                AdvisorId = 2,
                NonCompliance = false // false significa que está em compliance
            };

            context.Clients.AddRange(client1, client2);

            // Add test investments
            var investment1 = new Domain.Investment
            {
                InvestmentId = 1,
                Name = "E2E Test Bond",
                Type = "bond",
                Risk = "Conservador",
                Price = 100m,
                LastPriceUpdate = DateTime.UtcNow
            };

            var investment2 = new Domain.Investment
            {
                InvestmentId = 2,
                Name = "E2E Test Stock",
                Type = "stock",
                Risk = "Arrojado",
                Price = 50m,
                LastPriceUpdate = DateTime.UtcNow
            };

            context.Investments.AddRange(investment1, investment2);

            // Add client investments
            var clientInvestment1 = new Domain.ClientInvestment
            {
                ClientId = 1,
                InvestmentId = 1,
                Quantity = 10,
                InvestedAmount = 1000m,
                InvestmentDate = DateTime.UtcNow
            };

            var clientInvestment2 = new Domain.ClientInvestment
            {
                ClientId = 2,
                InvestmentId = 2,
                Quantity = 20,
                InvestedAmount = 1000m,
                InvestmentDate = DateTime.UtcNow
            };

            context.ClientInvestments.AddRange(clientInvestment1, clientInvestment2);

            context.SaveChanges();
        }
    }
}
