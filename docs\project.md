<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="./assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>
<br/>

# Nome do Projeto: Pati (Plataforma de Adequação de Tipo de Investidor)

## Nome do Grupo: MMs (Meninas Malvadas)

## Integrantes:

<div align="center">
<table>
  <tr>
    <td align="center">
      <a href="https://www.linkedin.com/in/larissa-temoteo/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQE-yMLLUD04Qg/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1730056625827?e=1750896000&v=beta&t=Vxukmk-nWK9EjbZGD4zQ0IIi5se0JwECJLmyqZ-2mrg" width="100px;" alt="Foto de Larissa Temoteo" style="border-radius:50%"/>
        <br />
        <b>Larissa Temoteo</b>
      </a>
      <br />
      <a href="https://github.com/larissatemoteo">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/larissa-temoteo/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/lucas-nunes-matheus/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHN4SR2WsAIdA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1710163486566?e=1750896000&v=beta&t=o-9q_kscwkEexlcm92Cobx197j0MsiztrpiTQgiJ9Kg" width="100px;" alt="Foto de Lucas Matheus Nunes" style="border-radius:50%"/>
        <br />
        <b>Lucas Matheus Nunes</b>
      </a>
      <br />
      <a href="https://github.com/lucas-nunes-matheus">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/lucas-nunes-matheus/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/rafael-furtado-b30715265/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQGfsjSmMmtAsw/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1709258479259?e=1750896000&v=beta&t=oIehlqkG2dGqrV8ya_JukCvuBgTEs-q7i32Oen49fdQ" width="100px;" alt="Foto de Rafael Furtado" style="border-radius:50%"/>
        <br />
        <b>Rafael Furtado</b>
      </a>
      <br />
      <a href="https://github.com/Rafaelfurtadovs">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/rafael-furtado-b30715265/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
  <tr>
    <td align="center">
      <a href="https://www.linkedin.com/in/ryan-botelho-gartlan/">
        <img src="https://media.licdn.com/dms/image/v2/D5603AQGy5KTEKUM2pA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1728396516687?e=1750896000&v=beta&t=LjSCsFve87n2F4J7v-LzbwHHytG4SJnTxTigdBhVlUU" width="100px;" alt="Foto de Ryan Gartlan" style="border-radius:50%"/>
        <br />
        <b>Ryan Gartlan</b>
      </a>
      <br />
      <a href="https://github.com/ryanbotgar">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/ryan-botelho-gartlan/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/tainacortez/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHV4IOZvu7n3A/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1713277689597?e=1750896000&v=beta&t=fgvCFEght43z8dmT0PhJgj1Lg5VtXoCjZie0pNPujxA" width="100px;" alt="Foto de Tainá Cortez" style="border-radius:50%"/>
        <br />
        <b>Tainá Cortez</b>
      </a>
      <br />
      <a href="https://github.com/taicortezz">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/tainacortez/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/thiagogomesalmeida/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHh3rHCD36uKA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1711828725384?e=1750896000&v=beta&t=Kggq5QNqIQ66GqL7_dT37fq5YO3NQAGBwX9BF0Fq8oU" width="100px;" alt="Foto de Thiago Gomes" style="border-radius:50%"/>
        <br />
        <b>Thiago Gomes</b>
      </a>
      <br />
      <a href="https://github.com/thiagomes07">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/thiagogomesalmeida/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
  <tr>
    <td colspan="3" align="center">
      <a href="https://www.linkedin.com/in/viniciussavian/">
        <img src="https://media.licdn.com/dms/image/v2/D4E03AQFsD6PLB2Du0w/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1710417108475?e=1750896000&v=beta&t=Qbzx-PMJMrTRJlEtE_NYRwVfMfOyY7Nf-duVxKugTmk" width="100px;" alt="Foto de Vinicius Savian" style="border-radius:50%"/>
        <br />
        <b>Vinicius Savian</b>
      </a>
      <br />
      <a href="https://github.com/ViniciusSavian">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/viniciussavian/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
</table>
</div>

# Sumário

- [1. Introdução](#1-introdução)
  - [1.1 Termos e Abreviações](#11-termos-e-abreviações)
  - [1.2 Objetivo do Documento](#12-objetivo-do-documento)
- [2. Entendimento do Projeto e do Negócio](#2-entendimento-do-projeto-e-do-negócio)
  - [2.1 Contexto da Indústria do Parceiro](#21-contexto-da-indústria-do-parceiro)
  - [2.2 Problema](#22-problema)
  - [2.3 Visão do Projeto e do Produto](#23-visão-do-projeto-e-do-produto)
  - [2.4 Personas e Jornada do Usuário](#24-personas-e-jornada-do-usuário)
  - [2.5 Modelagem do Fluxo de Negócio](#25-modelagem-do-fluxo-de-negócio)
    - [2.5.1 Cadeia de Valor](#251-cadeia-de-valor)
    - [2.5.2 Fluxo de Negócio Proposto (AS-IS e TO-BE)](#252-fluxo-de-negócio-proposto-as-is-e-to-be)
  - [2.6 Matriz de Risco do Projeto](#26-matriz-de-risco-do-projeto)
  - [2.7 Ideação](#27-ideação)
  - [2.8 Canvas do Projeto](#28-canvas-do-projeto)
- [3. Requisitos do Projeto](#3-requisitos-do-projeto)
  - [3.1 Requisitos Funcionais (RFs)](#31-requisitos-funcionais-rfs)
  - [3.2 Requisitos Não Funcionais (RNFs)](#32-requisitos-não-funcionais-rnfs)
  - [3.3 Correlação RFs e RNFs](#33-correlação-rfs-e-rnfs)
  - [3.4 Casos de Uso](#34-casos-de-uso)
  - [3.5 Casos de Uso x Requisitos Funcionais](#35-casos-de-uso-x-requisitos-funcionais)
- [4. Modelagem de Dados](#4-modelagem-de-dados)
  - [4.1 Modelo Conceitual de Dados](#41-modelo-conceitual-de-dados)
  - [4.2 Dicionário de Dados](#42-dicionário-de-dados)
  - [4.3 Modelo Lógico de Dados](#43-modelo-lógico-de-dados)
  - [4.4 Modelo Físico de Dados](#44-modelo-físico-de-dados)
- [5. Solução Técnica (Design)](#5-solução-técnica-design)
  - [5.1 Diagrama de Casos de Uso](#51-diagrama-de-casos-de-uso)
  - [5.2 Diagrama de Classes](#52-diagrama-de-classes)
    - [5.2.1 Diagrama de Sequência](#521-diagrama-de-sequência)
  - [5.3 Diagrama de Componentes](#53-diagrama-de-componentes)
  - [5.4 Linguagens e Tecnologias Utilizadas](#54-linguagens-e-tecnologias-utilizadas)
- [6. Mapeamento Técnico de Infraestrutura e Implantação](#6-mapeamento-técnico-de-infraestrutura-e-implantação)
  - [6.1 Diagrama de Implantação da UML](#61-diagrama-de-implantação-da-uml)
  - [6.2 Justificativa das Escolhas de Implantação](#62-justificativa-das-escolhas-de-implantação)
  - [6.3 Considerações sobre Desempenho e Segurança](#63-considerações-sobre-desempenho-e-segurança)
  - [6.4 Estimativas de Custo do Projeto](#64-estimativas-de-custo-do-projeto)
    - [6.4.1 Investimento em Pessoas](#641-investimentos-em-pessoas)
    - [6.4.2 Investimento em Software](#642-investimentos-em-software)
    - [6.4.3 Investimento em Infraestrutura](#643-investimentos-em-infraestrutura)
    - [6.4.4 Investimento Consolidado](#644-investimento-consolidado)
- [7. Projeto Visual da Solução](#7-projeto-visual-da-solução)
  - [7.1 Desenvolvimento de Wireframes](#71-desenvolvimento-de-wireframes)
  - [7.2 Desenvolvimento de Mockups](#72-desenvolvimento-de-mockups)
  - [7.3 Guia Visual](#73-guia-visual)
- [8. Desenvolvimento do Projeto](#8-desenvolvimento-do-projeto)
  - [8.1 Arquitetura de Codificação e Estrutura de Diretórios](#81-arquitetura-de-codificação-e-estrutura-de-diretórios)
  - [8.2 Modelo de Recomendação](#82-modelo-de-recomendação)
  - [8.3 Desenvolvimento de Features](#83-desenvolvimento-de-features)
    - [8.3.1 Sprint 3](#831-sprint-3)
    - [8.3.2 Sprint 4](#832-sprint-4)
    - [8.3.3 Sprint 5](#833-sprint-5)
  - [8.4 Testes Unitários e de Integração](#84-testes-unitários-e-de-integração)
    - [8.4.1 Planejamento de Testes de Integração](#841-planejamento-de-testes-de-integração)
  - [8.5 Documentações automáticas](#85-documentações-automáticas)
- [9. Planejamento e Execução de Testes](#9-planejamento-e-execução-de-testes)
  - [9.1 Testes Funcionais](#91-testes-funcionais)
    - [9.1.1 Planejamento](#911-planejamento)
    - [9.1.2 Resultados](#912-resultados)
  - [9.2 Testes de RNFs](#92-testes-de-rnfs)
    - [9.2.1 Planejamento](#921-planejamento)
    - [9.2.2 Resultados](#922-resultados)
  - [9.3 Testes de Usabilidade](#93-testes-de-usabilidade)
    - [9.3.1 Planejamento](#931-planejamento)
    - [9.3.2 Resultados](#932-resultados)
  - [9.4 Testes de APIs Externas](#94-testes-de-apis-externas)
    - [9.4.1 Planejamento](#941-planejamento)
    - [9.4.2 Resultados](#942-resultados)
- [10. Procedimentos de Implantação](#10-procedimentos-de-implantação)
  - [10.1 Implantação e Configuração do Banco de Dados](#101-implantação-e-configuração-do-banco-de-dados)
  - [10.2 Implantação do Protótipo para uso por equipe de desenvolvimento](#102-implantação-do-protótipo-para-uso-por-equipe-de-desenvolvimento)
- [Referências](#referências)

# 1. Introdução

_conteúdo_

## 1.1 Termos e Abreviações

_conteúdo_

## 1.2 Objetivo do Documento

_conteúdo_

# 2. Entendimento do Projeto e do Negócio

&emsp; Esta seção tem como objetivo contextualizar o projeto dentro do cenário do BTG Pactual e do setor financeiro, explicando os desafios enfrentados pelo banco em relação à adequação do perfil de risco dos clientes às suas carteiras de investimento. Ela detalha o problema atual, apresenta a visão do produto proposto e descreve como ele poderá auxiliar os assessores na tomada de decisões mais assertivas e em conformidade com a regulamentação. A seção também mapeia as personas envolvidas, modela o fluxo de negócios atual e desejado, identifica riscos do projeto e traça os fundamentos que guiam o desenvolvimento da solução. 


## 2.1 Contexto da Indústria do Parceiro

&emsp; O BTG Pactual é o maior banco de investimentos da América Latina, oferecendo gestão de recursos, corretagem e consultoria para clientes institucionais e de alta renda. Nos últimos anos, a migração para plataformas digitais reduziu erros e custos operacionais e, além disso, facilitou o cumprimento de regras como a Resolução CVM 175. Por exemplo, hoje é mais rápido alinhar cada produto ao perfil de risco do cliente, o que evita multas e protege a reputação da instituição.

&emsp; Ao mesmo tempo, a base de investidores pessoa física cresceu graças ao acesso a conteúdos de educação financeira e a apps de investimento. Como a taxa de juros e a inflação variam com frequência, e eventos globais (como decisões de bancos centrais estrangeiros) afetam o mercado, as carteiras precisam ser reequilibradas de forma veloz. Sem ferramentas adequadas, o banco corre o risco de perder retornos e oferecer produtos que não correspondem ao apetite de risco de seus clientes.

&emsp; No front competitivo, o BTG Pactual concorre com Itaú BBA, Bradesco BBI e Santander CIB, além de corretoras digitais em expansão, como XP, NuInvest e Modal. Ao mesmo tempo, existem parcerias em pesquisa de mercado e desenvolvimento de tecnologia, o que eleva o padrão de todo o setor. Por fim, a pressão por critérios ESG e mais transparência contábil atrai investidores estrangeiros e orienta as escolhas do banco sobre novos produtos e investimentos em tecnologia. 

## 2.2 Problema

&emsp;O BTG Pactual, maior banco de investimentos da América Latina, enfrenta o desafio de garantir que todas as aplicações de seus clientes estejam compatíveis com seus perfis de risco cadastrados, conforme previsto na Resolução CVM 175.

&emsp;Atualmente, essa verificação é realizada manualmente pelos assessores, exigindo análise minuciosa de cada carteira e comparação com o perfil informado, o que consome tempo operacional significativo e aumenta a probabilidade de inconsistências.

&emsp;Além do esforço manual elevado, a falta de um procedimento sistemático dificulta a escalabilidade da atividade e compromete a consistência na manutenção de conformidade.

&emsp;O problema central, portanto, é a ausência de um método padronizado e automatizado que assegure a compatibilidade entre o perfil de risco cadastrado dos investidores e a composição de suas carteiras de ativos, reduzindo o esforço manual, minimizando riscos de descumprimento regulatório e viabilizando auditorias eficientes.  

## 2.3 Visão do Projeto e do Produto

### 2.3.1 Objetivos do produto

&emsp; O objetivo do produto é capacitar os assessores do BTG Pactual a oferecerem um atendimento mais preciso e personalizado para cada cliente, reunindo em um único ambiente as informações do teste de "suitability", ou seja, do teste de adequação de perfil de investidor, e da carteira de investimentos desses clientes do banco, de modo que os assessores possam identificar rapidamente quais clientes estão desalinhados de seus perfis de investidor e recomendar ativos que façam a adequação do cliente ao seu perfil de risco ou, até mesmo, orientar o preenchimento do teste de perfil novamente. Desse modo, espera-se fortalecer a confiança do cliente em seu assessor de investimentos, além da eficiência operacional no processo de adequação de carteiras para conformidade regulatória.

### 2.3.2 O que o produto faz/ não faz

#### O que o produto faz

- **Autenticação e acesso personalizado**: Permite que assessores de investimentos acessem a plataforma através de credenciais seguras, visualizando apenas os clientes sob sua responsabilidade.

- **Dashboard analítico**: Apresenta um painel de controle intuitivo que exibe métricas críticas, incluindo:
  - Número total de clientes gerenciados pelo assessor
  - Quantidade e percentual de clientes com inconformidades de perfil
  - Classificação dos clientes por nível de prioridade de intervenção

- **Identificação proativa de inconformidades**: Analisa automaticamente as carteiras dos clientes e compara com seus perfis de investidor declarados (conservador, moderado, arrojado), sinalizando discrepâncias.

- **Visualização detalhada por cliente**: Oferece uma interface para análise individual de cada cliente em inconformidade, apresentando:
  - Perfil declarado vs. perfil real identificado pelo sistema
  - Composição atual da carteira com destaque para os ativos problemáticos

- **Recomendações personalizadas**: Gera sugestões específicas para realinhamento da carteira, incluindo:
  - Ativos que podem ser mantidos
  - Ativos recomendados para venda
  - Novas oportunidades de investimento alinhadas ao perfil do cliente

#### O que o produto não faz

- **Execução de operações**: Não realiza compras, vendas ou qualquer alteração automática nas carteiras dos clientes, funcionando apenas como ferramenta consultiva.

- **Interface para cliente final**: Não possui um portal de acesso para o investidor final, sendo destinado exclusivamente ao uso por assessores profissionais.

- **Validação jurídica**: Não substitui a análise de compliance ou adequação às normas da CVM, servindo como ferramenta de apoio e não como substituto para avaliações regulatórias.

- **Integração com plataformas de execução**: Na fase MVP, não se conecta diretamente com plataformas de corretoras ou distribuidoras para execução de ordens.

- **Análises subjetivas**: Não considera fatores qualitativos como eventos de vida do cliente, necessidades específicas de liquidez ou preferências pessoais que não estejam expressas em dados.

- **Gestão de relacionamento com cliente**: Não gerencia agendamentos, comunicações ou histórico de interações entre assessor e cliente.

- **Controle de metas comerciais**: Não monitora ou estabelece metas de venda de produtos financeiros para os assessores.

## 2.4 Personas e Jornada do Usuário

&emsp; Esta seção apresenta as duas principais personas (Carlos Silva, assessor de investimentos, e Mariana Souza, cliente investidora) e suas respectivas jornadas de usuário antes e depois da implantação da solução. Esses mapas de jornada foram elaborados para revelar as necessidades, pensamentos e pontos de atrito em cada etapa do processo de verificação de adequação entre perfil de risco e carteira e assim orientar o desenvolvimento das telas, dos fluxos e das funcionalidades do app, garantindo que tanto o assessor quanto o cliente tenham uma experiência fluida, com mais eficiência, clareza e confiança.

&emsp; Abaixo a imagem da persona **Mariana Souza** (Cliente do banco BTG):

<div align="center">
  <sub>Figura X - Persona 1 - Mariana Souza (Cliente do banco BTG)</sub> <br>

  <img src="./assets/2_entendimento_do_projeto_e_do_negocio/2.4_personas_e_jornada_do_usuario/persona/customer.png" alt="Persona 1 - Mariana Souza (Cliente do banco BTG)" style="max-width: 700px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

&emsp; Abaixo as imagens das jornadas da persona **Mariana Souza** (Cliente do banco BTG):

<div align="center">
  <sub>Figura X – Jornada de Usuário Antes da Solução (Mariana Souza, Cliente BTG)</sub> <br>

  <img src="./assets/2_entendimento_do_projeto_e_do_negocio/2.4_personas_e_jornada_do_usuario/user_journey/mariana_before.png" alt="Jornada de Usuário Antes da Solução (Mariana Souza, Cliente BTG)" style="max-width: 600px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

<div align="center">
  <sub>Figura X – Jornada de Usuário Depois da Solução (Mariana Souza, Cliente BTG)</sub> <br>

  <img src="./assets/2_entendimento_do_projeto_e_do_negocio/2.4_personas_e_jornada_do_usuario/user_journey/mariana_after.png" alt="Jornada de Usuário Depois da Solução (Mariana Souza, Cliente BTG)" style="max-width: 600px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

&emsp; Abaixo a imagem da persona **Carlos Silva** (Assessor de investimentos do BTG):

<div align="center">
  <sub>Figura X - Persona 2 - Carlos Silva (Assessor de investimentos do banco BTG)</sub> <br>

  <img src="./assets/2_entendimento_do_projeto_e_do_negocio/2.4_personas_e_jornada_do_usuario/persona/advisor.png" alt="Persona 2 - Carlos Silva (Assessor de investimentos do banco BTG)" style="max-width: 700px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

&emsp; Abaixo as imagens das jornadas da persona Carlos Silva (Assessor de investimentos do BTG):

<div align="center">
  <sub>Figura X – Jornada de Usuário Antes da Solução (Carlos Silva, Assessor BTG Pactual)</sub> <br>

  <img src="./assets/2_entendimento_do_projeto_e_do_negocio/2.4_personas_e_jornada_do_usuario/user_journey/carlos_before.png" alt="Jornada de Usuário Antes da Solução (Carlos Silva, Assessor BTG Pactual)" style="max-width: 600px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

<div align="center">
  <sub>Figura X – Jornada de Usuário Depois da Solução (Carlos Silva, Assessor BTG Pactual)</sub> <br>

  <img src="./assets/2_entendimento_do_projeto_e_do_negocio/2.4_personas_e_jornada_do_usuario/user_journey/carlos_after.png" alt="Jornada de Usuário Depois da Solução (Carlos Silva, Assessor BTG Pactual)" style="max-width: 600px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

## 2.5 Modelagem do Fluxo de Negócio

Modelar os processos de negócio é uma prática fundamental para garantir que os fluxos operacionais de um sistema sejam compreendidos de forma clara, padronizada e acessível. Nesse contexto, o diagrama BPMN representa o processo de ajuste de carteira de clientes realizado por assessores de investimentos no BTG Pactual, com base na Resolução CVM 175, que define as diretrizes para alinhar o perfil de risco do investidor com os ativos que compõem sua carteira.

O principal objetivo desse processo é permitir que o assessor identifique possíveis divergências entre o perfil declarado do investidor e sua carteira real de investimentos. Quando necessário, ele deve sugerir os ajustes apropriados e registrar, de forma organizada, todas as decisões tomadas ao longo do processo.

<div align="center">
  <sub>Figura X - Processo de Ajuste de Carteira – BTG Pactual</sub> <br>

  <img src="./assets/2_entendimento_do_projeto_e_do_negocio/bpmn.png" alt="Processo de Ajuste de Carteira – BTG Pactual" style="max-width: 800px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

### 2.5.1 Participantes do Processo

O fluxo envolve dois participantes principais, representados nas "lanes" do diagrama:

- **Assessor de Investimentos**: responsável por analisar o perfil e a carteira do cliente, propor alterações quando necessário e registrar todas as decisões de forma adequada.
- **Cliente (Investidor)**: responsável por analisar a proposta de alocação recebida e decidir se aceita ou não a recomendação feita.

### 2.5.2 Descrição do Fluxo

O processo começa com a atividade **“Analisar perfil do cliente”**, em que o assessor revisa os dados cadastrais e as informações sobre os investimentos do cliente. Na sequência, ele executa a tarefa **“Verificar conformidade da carteira”**, comparando os ativos atuais com o perfil de risco previamente declarado.

A primeira decisão acontece na etapa **“Carteira adequada?”**  
- Se a resposta for **não**, o assessor **registra a decisão no sistema** e o processo é encerrado.  
- Se for **sim**, ele identifica os ajustes necessários, elabora uma **proposta de alocação** e a **envia ao cliente**.

Do lado do cliente, ele realiza a tarefa **“Receber proposta”**, seguida da decisão **“Aceitar proposta?”**  
- Se a resposta for **sim**, o assessor **registra o aceite** e **documenta a decisão final**.  
- Se for **não**, a recusa também é registrada, e o processo é finalizado com o devido registro no sistema.

O fluxo termina com um **evento único de encerramento**, independentemente da escolha feita pelo cliente.

Essa modelagem contribui para tornar o processo de ajuste de carteira mais transparente e padronizado dentro do BTG Pactual. Ela oferece aos assessores uma visão clara de suas responsabilidades e, aos investidores, a segurança de que seus investimentos estão compatíveis com seu perfil. Além disso, a estruturação do processo facilita auditorias e o acompanhamento das decisões, alinhando-se às exigências regulatórias e às boas práticas de governança.

## 2.5.1 Cadeia de Valor

_conteúdo_

## 2.5.2 Fluxo de Negócio Proposto (AS-IS e TO-BE)

_conteúdo_

## 2.6 Matriz de Risco do Projeto

<div align="center">
  <sub>Figura X - Matriz de Riscos e Oportunidades</sub> <br>

  <img src="./assets/2_entendimento_do_projeto_e_do_negocio/risk_matrix.png" alt="Matriz de Riscos e Oportunidades" style="max-width: 800px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

&nbsp;&nbsp;&nbsp;&nbsp; No quadro a seguir, você encontra a descrição de cada risco apresentado na imagem acima, junto com o plano de ação para cada um deles.

<div align="center">
<sub> Quadro X  </sub>

| **Risco** | **Descrição** | **Plano de Ação** |
|:---|:---|:---|
| Desempenho ruim do algoritmo de recomendação | Pode ser que os modelos de recomendação que a gente fizer não tenham uma acurácia boa, o que pode deixar as sugestões fracas ou erradas. | Testar os modelos desde o início e, se os resultados não forem bons, tentar outros modelos ou ajustar os parâmetros. |
|Vazamento ou uso indevido de dados sensíveis | Falhas de segurança no manuseio de dados sensíveis (vazamento, acesso não autorizado) |Evitar salvar dados dos clientes no front-end, proteger as rotas do back-end com autenticação e revisar os dados que estamos usando pra garantir que não tem nada sensível sendo exposto.| Seguir boas práticas de segurança na manipulação de dados, evitar armazenar dados desnecessários e revisar os pontos mais críticos com atenção. |
| Dificuldade na manipulação e limpeza dos dados de 10.000 clientes | Pode ser que os dados estejam bagunçados ou difíceis de usar, o que pode atrasar o desenvolvimento. | Analisar os dados logo no início e fazer a limpeza que for necessária antes de programar o resto. |
| Inconsistência ou má interpretação das regras da CVM 175 | Se a gente entender errado as regras, pode gerar recomendações erradas para os clientes. | Estudar bem a CVM 175 e, se possível, confirmar o entendimento com alguém que entenda mais do assunto. |
| Dependência de dados estáticos sem atualização | Como os dados não vão ser atualizados, pode acontecer do app não funcionar para novos clientes. | Deixar claro que é um MVP e que a atualização de dados não é uma prioridade. |
| Demora para gerar recomendações | Pode ser que as recomendações demorem para aparecer, seja por causa da quantidade de dados, sobrecarga no servidor ou porque o algoritmo ficou pesado. | Usar uma arquitetura assíncrona no back-end, separando a geração das recomendações do fluxo principal da interface. Assim, os dados podem ser pré-processados e atualizados periodicamente sem travar a experiência do usuário. |
| Qualidade ruim dos dados recebidos para desenvolver um bom modelo de recomendação | Pode ser que os dados não tenham informações suficientes ou estejam mal estruturados, o que dificulta a criação de bons modelos. | Avaliar bem a base de dados no começo e, se necessário, pensar em formas de complementar ou adaptar o que foi enviado. |

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&nbsp;&nbsp;&nbsp;&nbsp; A imagem acima também destaca, no lado direito, as oportunidades que envolvem o projeto. A seguir tem-se uma descriçao de cada uma dessas oportunidades.

<div align="center">
<sub> Quadro X  </sub>

| **Oportunidade**                                                                                  | **Descrição**                                                                                                                                                                                                                         |
|:-------------------------------------------------------------------------------------------------|:-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Criar um MVP funcional que pode ser expandido comercialmente depois                              | Construir a versão inicial do app com login, dashboard e recomendações de forma estável, servindo de base para evoluir o produto em algo usado de verdade pelos assessores do BTG.                                                    |
| Possibilidade de integração futura com sistemas internos do BTG                                   | Como o app já trabalha com a lógica de suitability baseada na CVM 175, ele pode conectar-se diretamente aos sistemas do BTG para atualizar carteiras e enviar recomendações de forma automática, agilizando o trabalho dos assessores. |
| Aplicação para treinamentos internos (capacitação para novos assessores)                         | Usar o app como ferramenta de apoio em treinamentos, simulando cenários de clientes fora do perfil e ensinando novos assessores a interpretar inconformidades e sugerir ajustes de carteira.                                          |

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

## 2.6.1 Matriz de Risco da Equipe

<div align="center">
  <sub>Figura X - Matriz de Riscos e Oportunidades (da equipe)</sub> <br>

  <img src="./assets/2_entendimento_do_projeto_e_do_negocio/team_risk_matrix.png" alt="Matriz de Riscos e Oportunidades (da equipe)" style="max-width: 800px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

&nbsp;&nbsp;&nbsp;&nbsp; Na tabela a seguir, você encontra a descrição de cada risco apresentado na imagem acima, junto com o plano de ação para cada um deles.

| **Risco** | **Descrição** | **Plano de Ação** |
|---|---|---|
| Descomprometimento com prazos estabelecidos no grupo | Possibilidade de atrasos na entrega das atividades, prejudicando o andamento do projeto. | Definir um bom planejamento de sprint, monitorar prazos semanalmente e redistribuir tarefas em caso de atrasos. |
| Dificuldade na adaptação a novas ferramentas | Demora na aprendizagem de novas tecnologias pode prejudicar a produtividade. | Incentivar o compartilhamento de conhecimento entre os membros. |
| Visões diferentes do projeto dentro da equipe | Divergências podem gerar retrabalho e desalinhamento de objetivos. | Realizar alinhamentos frequentes e reuniões para garantir entendimento comum do projeto. |
| Falta de clareza na definição de papéis e responsabilidades | Pode gerar sobreposição de tarefas nas atividades, resultando também em tarefas não sendo realizadas. | Definir claramente as funções de cada membro e revisar periodicamente. |
| Falta de conhecimento aprofundado sobre as regulamentações da CVM | Riscos de não conformidade e recomendações inadequadas. | Promover estudo coletivo das normas. |
| Comunicação ineficaz da equipe | Informações podem ser perdidas ou mal interpretadas, afetando entregas. | Estabelecer canais de comunicação claros e reuniões regulares para alinhamento. |
| Escopo de projeto mal definido e mudanças de requisitos | Mudanças inesperadas podem causar retrabalho e atrasos. | Documentar o escopo detalhadamente e formalizar qualquer alteração antes da implementação. |

&nbsp;&nbsp;&nbsp;&nbsp; Na tabela a seguir, você encontra a descrição de cada oportunidade apresentada na imagem acima, junto com o plano de ação para cada uma delas.

| **Oportunidade** | **Descrição** |
|---|---|
| Aprimorar capacidade de trabalhar com dados simulados e reais | Desenvolvimento de habilidades em manipulação de diferentes tipos de dados. |
| Criar soluções inovadoras para detecção e ajuste de perfis | Possibilidade de desenvolver métodos diferenciados de análise de dados. |
| Estabelecer práticas eficazes de gestão de comunicação e equipe | Melhorar a organização e produtividade do grupo. |
| Garantir que toda a equipe passe por todas as áreas de aprendizado | Promover conhecimento multidisciplinar entre os membros. |
| Aprendizado prático e aprofundado sobre regulamentação CVM | Oportunidade de adquirir conhecimento relevante para o setor financeiro. |
| Ampliação da nossa rede de contatos profissional | Interação com especialistas pode gerar novas oportunidades. |
| Experimentar práticas de testes e validação de sistemas | Aprimorar a qualidade dos sistemas desenvolvidos. |


## 2.7 Ideação

## 2.7 Ideação

Esta seção descreve as funcionalidades definidas para o MVP do PATI — nossa Plataforma de Adequação de Tipo de Investidor. Abaixo, estão detalhadas todas as features planejadas para a primeira versão do aplicativo.

### 2.7.1 Brainstorming de Features

**Funcionalidades do MVP:**

- **Login do Assessor**: o profissional entra no app usando e-mail e senha (ou SSO) para garantir que só ele acesse os dados.
- **Dashboard de Inconformidade**: ao fazer login, o assessor vê uma tela resumida com quantos dos seus clientes estão fora do perfil esperado.
- **Indicadores Visuais**: gráficos e números que mostram rapidamente a porcentagem e o total de clientes fora do perfil.
- **Lista de Clientes Fora do Perfil**: tabela simples com o nome de cada cliente que está em inconformidade.
- **Detalhes do Cliente**: ao clicar num cliente, o assessor vê o perfil de risco dele, o que ele tem hoje e onde está o desvio.
- **Comparação Automática**: o sistema compara sozinho a carteira atual do cliente com o perfil declarado (ex.: conservador, moderado ou arrojado).
- **Recomendações**: baseado nessa comparação, o app sugere:
  - **O que manter** (ativos compatíveis)
  - **O que vender** (ativos fora do perfil)
  - **Novas sugestões** (ativos que casam com o perfil)
- **Revisão e Ajuste**: o assessor pode mexer nas sugestões antes de enviar ao cliente.
- **Importação Inicial de Dados**: upload ou integração de um arquivo (CSV/XLSX) com os 10.000 clientes do BTG para começar a usar no MVP.

---

### 2.7.2 Sequenciamento/Priorização de Entregas


- **Sprint 1: Alinhamento e Wireframes**  
  Definição de requisitos, desenho rápido das telas principais (login e dashboard) e confirmação das regras de negócio.

- **Sprint 2: Autenticação e Dashboard Básico**  
  Começar a desenvolver páginas do APP, implementar o login e criar a tela de dashboard.

- **Sprint 3: Listagem e Detalhes de Clientes**  
  Mostrar a lista de clientes fora do perfil e a página de detalhes que compara carteira versus perfil.

- **Sprint 4: Motor de Recomendações**  
  Desenvolver o algoritmo que sugere o que manter, vender e novas oportunidades, e criar a interface para revisão.

- **Sprint 5: Integração de Dados**  
  Ajustes e refinamento no algoritmo e aplicativo.

## 2.8 Canvas do Projeto

&emsp;A fim de esclarecer as personas identificadas, a proposta de valor do Mínimo Produto Viável (MVP), assim como, suas principais funcionalidades, além de detalhes adicionais relacionados ao projeto, como custo e cronograma, o seguinte Canvas do Mínimo Produto Viável foi elaborado. O Canvas facilita o alinhamento entre os *stakeholders* e a equipe de desenvolvimento, além de simplificar a validação da proposta de solução e seus detalhes com o parceiro.
A seguir está disposta a figura do Canvas MVP; de forma complementar, o Canvas também pode ser visualizado na plataforma Miro <a href="https://miro.com/app/board/uXjVI8UqCBQ=/?share_link_id=75787609626">aqui</a>.

<div align="center">
  <sub>Figura X - Título Descritivo da Imagem</sub> <br>

  <img 
      src="./assets/2_entendimento_do_projeto_e_do_negocio/canvas_mvp.jpg" alt="Canvas do Mínimo Produto" style="max-width: 600px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

&emsp;Dessa forma, é possível esclarecer de forma mais profunda a quem a solução é voltada, quais jornadas ela compõe com os seus  usuários, qual é a sua proposta de valor, as suas funcionalidades principais, os custos e o cronograma exigidos, os resultados esperados e as métricas de avaliação desse resultado. Assim, a comunicação entre a equipe de desenvolvimento e as partes interessadas se torna mais simples e eficaz, a partir da solução visual do Canvas.    

# 3. Requisitos do Projeto

## 3.1 Requisitos Funcionais (RFs)

&emsp;Nessa seção consta os requisitos funcionais da solução.

| RF   | Descrição                                                                                                                                                     | User Story Adaptada para Carlos Silva (Assessor)                                                                                                                |
|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------|
| RF01 | O sistema deve permitir que o assessor de investimentos faça login com credenciais seguras.                                                                   | Como Carlos Silva, eu quero fazer login com credenciais para garantir que só eu tenha acesso aos dados dos meus clientes.                 |
| RF02 | O sistema deve garantir que o assessor visualize apenas dados dos clientes sob sua responsabilidade.                                                          | Como Carlos Silva, eu quero visualizar apenas os dados dos clientes sob minha responsabilidade para manter a confidencialidade e cumprir as normas.  |
| RF03 | O sistema deve apresentar, após login, a porcentagem de clientes com inconformidade de perfil.                                                                | Como Carlos Silva, eu quero ver, logo após o login, a porcentagem de clientes que estão com inconformidade de perfil para identificar rapidamente a situação da minha carteira.            |
| RF04 | O sistema deve listar os clientes com inconformidades e permitir filtrar por status, perfil e valor investido.                        | Como Carlos Silva, eu quero que o sistema liste os clientes com inconformidades e me permita filtrar por diferentes critérios para facilitar minha análise.            |
| RF05 | O sistema deve analisar automaticamente a carteira de cada cliente.                                                                                          | Como Carlos Silva, eu quero que o sistema analise automaticamente a carteira de cada cliente para economizar tempo em conferências manuais.          |
| RF06 | O sistema deve comparar o perfil cadastrado do cliente com o perfil real calculado com base na composição atual da carteira e sinalizar os clientes cuja carteira esteja fora do perfil declarado. | Como Carlos Silva, eu quero que o sistema compare o perfil cadastrado do cliente com o perfil real calculado pela carteira e sinalize os casos fora do perfil. |
| RF07 | O sistema deve permitir ao assessor clicar em um cliente específico para visualizar o perfil cadastrado versus perfil real.                                    | Como Carlos Silva, eu quero clicar em um cliente específico para visualizar o perfil cadastrado versus o perfil real.                                |
| RF08 | O sistema deve gerar recomendações para realinhamento da carteira, apresentando novas oportunidades de ativos alinhados ao perfil do cliente. | Como Carlos Silva, eu quero receber recomendações automáticas de realinhamento da carteira, incluindo oportunidades alinhadas ao perfil do cliente. |
| RF09 | O sistema deve permitir ao assessor visualizar a composição atual da carteira, com destaque para ativos desalinhados ao perfil.                               | Como Carlos Silva, eu quero visualizar a composição atual da carteira do cliente, com destaque para os ativos desalinhados ao perfil.                |
| RF10 | O sistema deve atualizar periodicamente os dados de clientes e carteiras.                                                                                    | Como Carlos Silva, eu quero que o sistema atualize periodicamente os dados para que eu sempre trabalhe com informações recentes.                    |
| RF11 | O sistema deve registrar logs de acesso e atividades dos assessores na plataforma.                                                                           | Como Carlos Silva, eu quero que o sistema registre todas as atividades realizadas na plataforma para fins de auditoria e segurança.                  |
| RF12 | O sistema deve permitir a importação de dados via arquivos (.csv, .xlsx) ou via API REST no esquema designado na documentação.                               | Como Carlos Silva, eu quero poder importar dados de diferentes fontes para manter o sistema atualizado com informações de outros sistemas.          |

&emsp;A seguir temos os testes que devem ser feitos para garantir que os requisitos funcionais estão implementados na solução.

| RF    | Descrição Original | Descrição dos Testes |
|-------|--------------------|-----------------------|
| RF01 | O sistema deve permitir que o assessor de investimentos faça login com credenciais seguras. | **Pré-Condição:** Tela de login implementada e assessor com credenciais válidas. **Procedimento:** Acessar a página de login, inserir credenciais e autenticar. **Resultado Esperado:** Acesso concedido. **Pós-condição:** Assessor autenticado na plataforma. |
| RF02 | O sistema deve garantir que o assessor visualize apenas dados dos clientes sob sua responsabilidade. | **Pré-Condição:** Assessor autenticado com clientes atribuídos. **Procedimento:** Navegar até a lista de clientes. **Resultado Esperado:** Exibição apenas dos clientes vinculados ao assessor. **Pós-condição:** Dados acessados com restrição adequada. |
| RF03 | O sistema deve apresentar, após login, a porcentagem de clientes com inconformidade de perfil. | **Pré-Condição:** Assessor autenticado com base de clientes cadastrada. **Procedimento:** Fazer login e acessar o dashboard inicial. **Resultado Esperado:** Porcentagem de clientes não conformes exibida corretamente. **Pós-condição:** Dados resumidos disponíveis para consulta. |
| RF04 | O sistema deve listar os clientes com inconformidades e permitir filtrar por status, perfil, valor investido e data do último contato. | **Pré-Condição:** Clientes com diferentes status, perfis, valores investidos e datas de contato. **Procedimento:** Acessar a seção de clientes e aplicar filtros. **Resultado Esperado:** Lista filtrada corretamente conforme critérios selecionados. **Pós-condição:** Dados filtrados disponíveis para análise. |
| RF05 | O sistema deve analisar automaticamente a carteira de cada cliente. | **Pré-Condição:** Clientes com carteiras cadastradas. **Procedimento:** Acessar o sistema e aguardar análise automática. **Resultado Esperado:** Sistema exibe análise para cada cliente. **Pós-condição:** Informações processadas com sucesso. |
| RF06 | O sistema deve comparar o perfil cadastrado do cliente com o perfil real calculado com base na composição atual da carteira e sinalizar os clientes cuja carteira esteja fora do perfil declarado. | **Pré-Condição:** Dados de carteira e perfil do cliente disponíveis. **Procedimento:** Executar análise do perfil. **Resultado Esperado:** Sinalização clara de incoerências. **Pós-condição:** Cliente marcado como incoerente, se aplicável. |
| RF07 | O sistema deve permitir ao assessor clicar em um cliente específico para visualizar o perfil cadastrado versus perfil real. | **Pré-Condição:** Cliente listado como gerenciado. **Procedimento:** Clicar no nome do cliente na listagem. **Resultado Esperado:** Tela exibe os dois perfis. **Pós-condição:** Assessor com acesso às informações comparativas. |
| RF08 | O sistema deve gerar recomendações para realinhamento da carteira, apresentando novas oportunidades de ativos alinhados ao perfil do cliente. | **Pré-Condição:** Cliente com carteira incoerente. **Procedimento:** Acessar cliente e verificar recomendações. **Resultado Esperado:** Sugestões de novas oportunidades de investimento exibidas. **Pós-condição:** Recomendações armazenadas na interface. |
| RF09 | O sistema deve permitir ao assessor visualizar a composição atual da carteira, com destaque para ativos desalinhados ao perfil. | **Pré-Condição:** Dados da carteira importados e associados. **Procedimento:** Acessar a tela de carteira do cliente. **Resultado Esperado:** Composição com destaque para os ativos desalinhados. **Pós-condição:** Informações disponíveis para decisão. |
| RF10 | O sistema deve atualizar periodicamente os dados de clientes e carteiras. | **Pré-Condição:** Sistema configurado para atualizações periódicas. **Procedimento:** Verificar se os dados são atualizados conforme programação. **Resultado Esperado:** Dados atualizados automaticamente. **Pós-condição:** Sistema com informações recentes. |
| RF11 | O sistema deve registrar logs de acesso e atividades dos assessores na plataforma. | **Pré-Condição:** Assessor autenticado realizando atividades. **Procedimento:** Executar ações como login, visualização e exportação. **Resultado Esperado:** Ações gravadas nos logs. **Pós-condição:** Plataforma auditável. |
| RF12 | O sistema deve permitir a importação de dados via arquivos (.csv, .xlsx) ou via API REST no esquema designado na documentação. | **Pré-Condição:** Arquivos ou endpoints válidos prontos para uso. **Procedimento:** Submeter arquivos ou realizar chamada à API. **Resultado Esperado:** Dados importados com sucesso. **Pós-condição:** Dados integrados à base do sistema. |

## 3.2 Requisitos Não Funcionais (RNFs)

&ensp;Nessa seção constam os requisitos não funcionais da solução.

| RNF  | Descrição                                                                                      | Categoria                  |
|------|------------------------------------------------------------------------------------------------|----------------------------|
| RNF01 | O sistema deve ser compatível com Android 10+ e iOS 13+.                                       | Compatibilidade            |
| RNF02 | O sistema deve ter disponibilidade mínima de 99,5% em horário comercial.                       | Confiabilidade             |
| RNF03 | O sistema deve manter no mínimo 70% de acurácia no algoritmo de recomendação.                  | Funcionalidade             |
| RNF04 | O sistema deve garantir tempo de resposta inferior a 2 segundos para consultas padrão em condições favoráveis. | Desempenho e Eficiência |
| RNF05 | O sistema deve suportar ao menos 500 usuários simultâneos sem perda perceptível de desempenho. | Escalabilidade             |
| RNF06 | O sistema deve concluir o upload de arquivos CSV ou XLSX no formato padrão com 10 mil registros em até 15 segundos. | Desempenho e Eficiência |
| RNF07 | O sistema deve realizar reanálise de recomendação em até 10 segundos.                          | Desempenho e Eficiência    |
| RNF08 | O sistema deve ter código modular para facilitar manutenção e evolução.                        | Manutenibilidade           |
| RNF09 | O sistema deve seguir padrões de codificação e documentação.                                   | Manutenibilidade           |
| RNF10 | O sistema deve ser responsivo em diversos dispositivos móveis.                                 | Usabilidade                |
| RNF11 | O sistema deve ser implantado em ambiente Docker.                                              | Portabilidade              |
| RNF12 | O sistema deve ser agnóstico de provedor (AWS, Azure, GCP).                                    | Portabilidade              |
| RNF13 | O sistema deve garantir a integridade dos dados em caso de falhas.                             | Confiabilidade             |
| RNF14 | O sistema deve implementar autenticação segura e manter sessão por no máximo 30 minutos de inatividade. | Segurança |
| RNF15 | O sistema deve bloquear acesso após 5 tentativas falhas de login.                              | Segurança                  |
| RNF16 | O sistema deve restringir acesso a dados conforme perfil do usuário.                           | Segurança                  |
| RNF17 | O sistema deve permitir que o usuário aprenda a utilizá-lo em menos de 6 minutos.              | Usabilidade                |
| RNF18 | O sistema deve fornecer mensagens de erro claras e orientadas à solução.                       | Usabilidade                |

&emsp;A seguir temos os testes que devem ser feitos para garantir que os requisitos não funcionais estão implementados na solução.

| RNF   | Descrição Original | Descrição dos Testes |
|-------|--------------------|-----------------------|
| RNF01 | O sistema deve ser compatível com Android 10+ e iOS 13+. | **Pré-Condição:** Dispositivos com Android 10+ e iOS 13+ disponíveis. **Procedimento:** Instalar e testar a aplicação em diferentes dispositivos. **Resultado Esperado:** Sistema funciona corretamente em todas as plataformas. **Pós-condição:** Aplicação validada em múltiplos dispositivos. |
| RNF02 | O sistema deve ter disponibilidade mínima de 99,5% em horário comercial. | **Pré-Condição:** Sistema em ambiente de teste. **Procedimento:** Configurar verificações de disponibilidade a cada hora durante um dia de trabalho, simular uma interrupção breve. **Resultado Esperado:** Sistema disponível em pelo menos 90% das verificações e recuperação em menos de 5 minutos após interrupção. **Pós-condição:** Registro de disponibilidade documentado. |
| RNF03 | O sistema deve manter no mínimo 70% de acurácia no algoritmo de recomendação. | **Pré-Condição:** Conjunto de dados de teste preparado. **Procedimento:** Executar algoritmo e avaliar recomendações com especialistas. **Resultado Esperado:** Acurácia de pelo menos 70%. **Pós-condição:** Algoritmo validado e documentado. |
| RNF04 | O sistema deve garantir tempo de resposta inferior a 2 segundos para consultas padrão em condições favoráveis. | **Pré-Condição:** Sistema em ambiente de teste. **Procedimento:** Executar consultas padrão e medir tempo de resposta. **Resultado Esperado:** Todas as consultas completadas em menos de 2 segundos. **Pós-condição:** Desempenho documentado. |
| RNF05 | O sistema deve suportar ao menos 500 usuários simultâneos sem perda perceptível de desempenho. | **Pré-Condição:** Ambiente de teste configurado para simulação de carga. **Procedimento:** Simular acessos simultâneos crescentes até 500 usuários. **Resultado Esperado:** Sistema mantém desempenho aceitável. **Pós-condição:** Capacidade de carga documentada. |
| RNF06 | O sistema deve concluir o upload de arquivos CSV ou XLSX no formato padrão com 10 mil registros em até 15 segundos. | **Pré-Condição:** Arquivos de teste preparados. **Procedimento:** Realizar uploads e medir tempo de processamento. **Resultado Esperado:** Upload e processamento em menos de 15 segundos. **Pós-condição:** Desempenho de upload documentado. |
| RNF07 | O sistema deve realizar reanálise de recomendação em até 10 segundos. | **Pré-Condição:** Ambiente de teste com acesso ao backend. **Procedimento:** Acionar endpoint de reanálise e medir tempo. **Resultado Esperado:** Reanálise concluída em menos de 10 segundos. **Pós-condição:** Tempos de reanálise documentados. |
| RNF08 | O sistema deve ter código modular para facilitar manutenção e evolução. | **Pré-Condição:** Código-fonte disponível para análise. **Procedimento:** Realizar análise estática e verificar separação de responsabilidades. **Resultado Esperado:** Código com alta coesão e baixo acoplamento. **Pós-condição:** Métricas de qualidade documentadas. |
| RNF09 | O sistema deve seguir padrões de codificação e documentação. | **Pré-Condição:** Padrões definidos e documentados. **Procedimento:** Executar verificação automatizada e revisar manualmente amostras de código. **Resultado Esperado:** Código segue consistentemente os padrões estabelecidos. **Pós-condição:** Relatório de conformidade gerado. |
| RNF10 | O sistema deve ser responsivo em diversos dispositivos móveis. | **Pré-Condição:** Sistema implementado com design responsivo. **Procedimento:** Testar em diferentes tamanhos de tela e orientações. **Resultado Esperado:** Interface adapta-se corretamente a todos os dispositivos. **Pós-condição:** Comportamento responsivo documentado. |
| RNF11 | O sistema deve ser implantado em ambiente Docker. | **Pré-Condição:** Docker instalado no ambiente de teste. **Procedimento:** Criar e executar contêineres Docker do sistema. **Resultado Esperado:** Sistema funciona corretamente em contêineres. **Pós-condição:** Implantação Docker validada. |
| RNF12 | O sistema deve ser agnóstico de provedor (AWS, Azure, GCP). | **Pré-Condição:** Ambientes de teste em diferentes provedores configurados. **Procedimento:** Implantar e testar sistema em cada provedor. **Resultado Esperado:** Sistema funciona consistentemente em todos os provedores. **Pós-condição:** Portabilidade entre provedores validada. |
| RNF13 | O sistema deve garantir a integridade dos dados em caso de falhas. | **Pré-Condição:** Sistema com dados de teste carregados. **Procedimento:** Simular falhas e verificar integridade dos dados. **Resultado Esperado:** Dados permanecem íntegros após recuperação. **Pós-condição:** Mecanismos de proteção validados. |
| RNF14 | O sistema deve implementar autenticação segura e manter sessão por no máximo 30 minutos de inatividade. | **Pré-Condição:** Sistema com autenticação configurada. **Procedimento:** Testar login e verificar expiração de sessão. **Resultado Esperado:** Autenticação funciona e sessão expira após 30 minutos. **Pós-condição:** Mecanismos de autenticação validados. |
| RNF15 | O sistema deve bloquear acesso após 5 tentativas falhas de login. | **Pré-Condição:** Sistema com autenticação configurada. **Procedimento:** Realizar tentativas falhas de login consecutivas. **Resultado Esperado:** Acesso bloqueado após 5 tentativas. **Pós-condição:** Mecanismo de bloqueio validado. |
| RNF16 | O sistema deve restringir acesso a dados conforme perfil do usuário. | **Pré-Condição:** Usuários com diferentes perfis cadastrados. **Procedimento:** Tentar acessar dados com cada perfil. **Resultado Esperado:** Acesso permitido apenas a dados autorizados. **Pós-condição:** Controle de acesso validado. |
| RNF17 | O sistema deve permitir que o usuário aprenda a utilizá-lo em menos de 6 minutos. | **Pré-Condição:** Usuários de teste selecionados. **Procedimento:** Cronometrar tempo de aprendizado de tarefas básicas. **Resultado Esperado:** Usuários completam aprendizado em menos de 6 minutos. **Pós-condição:** Facilidade de aprendizado validada. |
| RNF18 | O sistema deve fornecer mensagens de erro claras e orientadas à solução. | **Pré-Condição:** Sistema em ambiente de teste. **Procedimento:** Provocar erros intencionalmente e avaliar mensagens. **Resultado Esperado:** Mensagens claras e úteis exibidas. **Pós-condição:** Qualidade das mensagens de erro validada. |

## 3.3 Correlação RFs e RNFs

&emsp;A seguir a tabela que relaciona RFs e RNFs que tenham relações diretas:

| ID RF | Requisito Funcional                                | ID RNF | Requisito Não Funcional                                         | Relação                                                                 |
|-------|-----------------------------------------------------|--------|-------------------------------------------------------------------|-------------------------------------------------------------------------|
| RF01  | Login com credenciais seguras                      | RNF14  | Autenticar usuários e expirar sessão inativa                     | Garante a segurança das informações presentes na solução               |
| RF01  | Login com credenciais seguras                      | RNF15  | Registrar tentativas de acesso inválidas e aplicar bloqueio      | Garante que somente usuários com as informações corretas façam login   |
| RF02  | Visualizar apenas dados dos seus clientes          | RNF16  | Restringir acesso de dados conforme perfil do usuário            | Garante que somente o assessor tenha acesso às informações do cliente  |
| RF03  | Apresentar porcentagem de clientes com inconformidade | RNF10  | Ser responsivo em diversos dispositivos móveis                   | Visualização dos clientes e inconformidades deve funcionar bem em qualquer tela |
| RF04  | Listar clientes com inconformidades e permitir filtrar | RNF04  | Tempo de resposta inferior a 2 segundos para consultas padrão    | As consultas de listagem e filtragem devem ser rápidas                 |
| RF05  | Analisar automaticamente a carteira de cada cliente | RNF03  | Acurácia mínima de 70% no algoritmo de recomendação              | A análise precisa depende da eficácia do algoritmo                     |
| RF05  | Analisar automaticamente a carteira de cada cliente | RNF07  | Reanálise de recomendação em até 10 segundos                     | Análises automáticas devem ser executadas rapidamente                  |
| RF06  | Comparar perfil cadastrado com perfil real         | RNF03  | Acurácia mínima de 70% no algoritmo de recomendação              | A comparação de perfis depende da precisão do algoritmo                |
| RF07  | Visualizar perfil cadastrado versus perfil real    | RNF10  | Ser responsivo em diversos dispositivos móveis                   | Deve adaptar-se bem em celulares e tablets                             |
| RF08  | Gerar recomendações para realinhamento da carteira | RNF03  | Acurácia mínima de 70% no algoritmo de recomendação              | Recomendações devem ser confiáveis e fazer sentido para os clientes    |
| RF08  | Gerar recomendações para realinhamento da carteira | RNF07  | Reanálise de recomendação em até 10 segundos                     | Recomendações devem ser geradas rapidamente                            |
| RF09  | Visualizar carteira com destaque para ativos desalinhados | RNF10  | Ser responsivo em diversos dispositivos móveis                   | A visualização deve ser clara em qualquer dispositivo                  |
| RF10  | Atualizar periodicamente os dados de clientes      | RNF13  | Garantir a integridade dos dados em caso de falhas               | Atualizações não devem corromper dados existentes                      |
| RF11  | Registrar logs de acesso e atividades              | RNF14  | Implementar autenticação segura e manter sessão                  | Logs devem registrar eventos de autenticação e sessão                  |
| RF12  | Importar dados via arquivos ou API REST            | RNF06  | Upload de 10 mil registros em até 15 segundos                    | Tempo de carga deve ser eficiente                                      |
| RF12  | Importar dados via arquivos ou API REST            | RNF13  | Garantir a integridade dos dados em caso de falhas               | Importações devem preservar a integridade dos dados                    |

&ensp; Nota: Esta tabela de correlação entre requisitos funcionais e não funcionais foi atualizada na Sprint 3, refletindo as modificações realizadas nos RFs e RNFs para melhor alinhamento com as necessidades do projeto e o escopo de implementação.

## 3.4 Casos de Uso

_conteúdo_

## 3.5 Casos de Uso x Requisitos Funcionais

_conteúdo_

# 4. Modelagem de Dados

&emsp; Esta seção apresenta a modelagem de dados do sistema proposto, que tem como objetivo organizar, estruturar e representar de forma clara os dados que serão manipulados e armazenados pela aplicação. Foram desenvolvidos dois modelos: o **modelo conceitual**, que representa a estrutura de alto nível dos dados, e o **modelo lógico**, que traduz essa estrutura para um formato compatível com sistemas gerenciadores de banco de dados relacionais (SGBDs). Ambos os modelos foram desenvolvidos com o auxílio da ferramenta **BRModelo**, e seus arquivos de projeto (.brM3) estão disponíveis na pasta `docs` do repositório.

## 4.1 Modelo Conceitual de Dados

&emsp; O modelo conceitual foi construído com base na análise do domínio do problema, considerando as entidades, seus atributos e os relacionamentos entre elas. O objetivo é representar, de forma abstrata e sem se preocupar com implementações específicas, como os dados interagem dentro do sistema.

### Entidades e Atributos

* **Client** <br/>
  Representa o cliente da plataforma, que possui dados cadastrais e informações sobre seu perfil de investimento.
  Atributos:

  * `client_id`: Identificador único do cliente.
  * `name`: Nome completo do cliente.
  * `phone_number`: Número de celular.
  * `email`: Endereço de e-mail do cliente.
  * `risk_profile_wallet`: Perfil de risco do cliente baseado na carteira atual.
  * `risk_profile_form`: Perfil de risco do cliente baseado em formulário de avaliação.
  * `non_compliance`: Indica se o cliente possui alguma não conformidade.
  * `advisor_id`: Referência ao assessor responsável.

* **Advisor** <br/>
  Representa o assessor de investimentos responsável por um ou mais clientes.
  Atributos:

  * `advisor_id`: Identificador único do assessor.
  * `uid`: Identificador único universal do assessor.

* **Investment** <br/>
  Representa uma aplicação financeira disponível na plataforma.
  Atributos:

  * `investment_id`: Identificador único do investimento.
  * `name`: Nome do investimento.
  * `risk`: Nível de risco do investimento.
  * `type`: Tipo do investimento.
  * `price`: Preço do investimento.

* **Client_Investment** *(Entidade Associativa)* <br/>
  Representa os investimentos que um cliente possui atualmente.
  Atributos:

  * `client_id`: Referência ao cliente.
  * `investment_id`: Referência ao investimento.
  * `quantity`: Quantidade do investimento adquirido.
  * `investment_date`: Data de aquisição do investimento.

* **Client_Recommendation** *(Entidade Associativa)* <br/>
  Representa os investimentos recomendados ao cliente com base em um modelo de análise.
  Atributos:

  * `client_id`: Referência ao cliente.
  * `investment_id`: Referência ao investimento.
  * `recommendation_score`: Pontuação da recomendação.
  * `recommendation_date`: Data da recomendação.

### Relacionamentos

* `has_advisor` — entre `Client` e `Advisor` (1,1) para (0,n).
* `owns` — entre `Client` e `Client_Investment` (1,n) para (0,n).
* `is_owned` — entre `Investment` e `Client_Investment` (1,1) para (0,n).
* `receives` — entre `Client` e `Client_Recommendation` (1,n) para (0,n).
* `is_recommended` — entre `Investment` e `Client_Recommendation` (1,1) para (0,n).

### Diagrama Conceitual

&emsp; Abaixo está o diagrama do modelo conceitual desenvolvido no BRModelo.

<div align="center">
  <sub>Figura X - Modelo conceitual de dados</sub> <br>

  <img 
      src="./assets/4_modelagem_de_dados/conceptual_model.png" alt="Modelo conceitual de dados" 
      style="max-width: 800px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

> **Arquivo do projeto (formato `.brM3`, gerado pelo software BRModelo):** localizado em `docs\assets\4_modelagem_de_dados\conceptual_model.brM3`, a partir da raiz do repositório.

## 4.2 Dicionário de Dados

&ensp;Essa seção tem o objetivo de esclarecer o esquema do banco de dados adotado no projeto, apresentando quais tabelas estão presentes no banco de dados, as informações e os tipos de dados armazenados, assim como, os relacionamentos entre elas.<br/>
&ensp;Portanto, o quadro a seguir apresenta uma visão consolidada das entidades e seus atributos no banco de dados:

<div align="center">
<sup>Quadro X - Dicionário de dados</sup>

| Entidade                   | Atributo              | Tipo de Dado  | Restrição | Descrição                                          |
| -------------------------- | --------------------- | ------------- | --------- | -------------------------------------------------- |
| **CLIENT**                 | client\_id            | INTEGER       | PRIMARY KEY, UNIQUE, NOT NULL | Identificador único do cliente                     |
|                            | name                  | VARCHAR(100)  | NOT NULL | Nome completo do cliente                           |
|                            | phone\_number         | VARCHAR(20)   | NULLABLE | Número de celular                                  |
|                            | email                 | VARCHAR(100)  | UNIQUE, NOT NULL | Endereço de e-mail (único)                         |
|                            | risk\_profile\_wallet | VARCHAR(50)   | NOT NULL | Perfil de risco da carteira                        |
|                            | risk\_profile\_form   | VARCHAR(50)   | NOT NULL | Perfil de risco do formulário                      |
|                            | compliance            | BOOLEAN       | NOT NULL, CHECK (risk\_profile\_wallet != risk\_profile\_form) | Indica se o cliente possui alguma não conformidade |
|                            | advisor\_id           | INTEGER       | FOREIGN KEY, NOT NULL | Referência ao assessor responsável                 |
|                            | created\_at           | DATETIME      | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Data e hora padronizados da criação do registro |
|                            | updated\_at           | DATETIME      | NOT NULL, CHECK (updated\_at >= created\_at) | Data e hora padronizados da última atualização do registro |
| **ADVISOR**                | advisor\_id           | INTEGER       | PRIMARY KEY, UNIQUE, NOT NULL | Identificador único do assessor                    |
|                            | uid                   | VARCHAR(128)  | UNIQUE, NOT NULL | Identificador único universal do assessor já antevendo a utilização de serviços externos de autenticação          |
|                            | created\_at           | DATETIME      | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Data e hora padronizados da criação do registro |
|                            | updated\_at           | DATETIME      | NOT NULL, CHECK (updated\_at >= created\_at) | Data e hora padronizados da última atualização do registro |
| **INVESTMENT**             | investment\_id        | INTEGER       | PRIMARY KEY, UNIQUE, NOT NULL | Identificador do investimento                      |
|                            | name                  | VARCHAR(100)  | NOT NULL | Nome do investimento                               |
|                            | risk                  | VARCHAR(50)   | NULLABLE | Nível de risco                                     |
|                            | type                  | VARCHAR(50)   | NOT NULL | Tipo da aplicação (renda fixa, variável, etc.)     |
|                            | created\_at           | DATETIME      | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Data e hora padronizados da criação do registro |
|                            | updated\_at           | DATETIME      | NOT NULL, CHECK (updated\_at >= created\_at) | Data e hora padronizados da última atualização do registro |
| **CLIENT\_INVESTMENT**     | client\_id            | INTEGER       | FOREIGN KEY, UNIQUE, NOT NULL | Referência ao cliente                              |
|                            | investment\_id        | INTEGER       | FOREIGN KEY, UNIQUE, NOT NULL | Referência ao investimento                         |
|                            | investment\_date      | DATETIME      | NOT NULL | Data da aquisição do investimento                  |
|                            | quantity              | INTEGER       | NOT NULL, CHECK (quantity >= 0) | Quantidade de cotas de investimento adquirida do investimento               |
|                            | invested\_amount      | DECIMAL(25,2) | NOT NULL | Esse é o montante financeiro aplicado no investimento. (Conforme o parceiro de negócios, pode ser nulo ou negativo, a depender da variação de preço do ativo).                |
|                            | created\_at           | DATETIME      | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Data e hora padronizados da criação do registro |
|                            | updated\_at           | DATETIME      | NOT NULL, CHECK (updated\_at >= created\_at) | Data e hora padronizados da última atualização do registro |

<sub>Fonte: Material produzido pelos autores(2025).</sub>
</div>

Além disso, a fim de esclarecer os relacionamentos entre as entidades existentes no banco de dados, o quadro a seguir detalha as associações entre as diferentes entidades do banco de dados e, considerando a natureza dos dados, também aponta restrições:

<div align="center">
<sup>Quadro X - Relacionamento entre as entidades no banco de dados.</sup>

| Entidade Origem | Tipo de Relacionamento | Entidade Destino | Descrição | Restrição |
| :-------------- | :--------------------- | :--------------- | :-------- | :----------- |
| **CLIENT** | Um para Muitos (1:N) | **CLIENT_INVESTMENT** | Um cliente pode ter múltiplos registros de investimento associados (CLIENT_INVESTMENT). | Um registro de investimento nunca poderá existir para um cliente que não existe na tabela **CLIENT**. |
| **INVESTMENT** | Um para Muitos (1:N) | **CLIENT_INVESTMENT** | Um investimento pode estar presente em múltiplos registros de investimento de clientes (CLIENT_INVESTMENT). | Um registro de investimento nunca poderá existir para um ativo que não existe na tabela **INVESTMENT**. |
| **ADVISOR** | Um para Muitos (1:N) | **CLIENT** | Um assessor pode ter vários clientes em sua carteira, mas cada cliente é atendido por apenas um assessor. | Um assessor nunca deverá existir, a não ser que possua ao menos um cliente associado a ele. |

<sub>Fonte: Material produzido pelos autores(2025).</sub>
</div>

&ensp;Por fim, essa seção teve o objetivo de esclarecer quais dados estão sendo armazenados dentro do banco de dados e quais são suas naturezas, relacionamento e restrições.</br>

&ensp;**Observação**: Embora essa seção seja apresentada entre as etapas de modelagem conceitual e lógica do banco de dados dentro dessa documentação, na verdade, esse dicionário de dados foi construído após a confeccção e validação do modelo lógico com o parceiro de negócios e de forma concomitante à construção do modelo físico, o que significa que a ausência da tabela `CLIENT_RECOMMENDATION` é justificada nessa etapa de desenvolvimento do projeto (para mais detalhes, vide o <strong><a href="https://docs.google.com/document/d/1EZ5tCxaDQ92gZWzjVcows7-HOuk22huztZEuK7kBr1w/edit?usp=sharing">Registro de Decisão de Arquitetura 02</a></strong>).

## 4.3 Modelo Lógico de Dados

&emsp; O modelo lógico é derivado diretamente do modelo conceitual, com entidades transformadas em tabelas, atributos definidos com tipos de dados adequados e relacionamentos convertidos em chaves estrangeiras. Abaixo estão os principais aspectos do modelo lógico:

* Chaves Primárias (PK) são utilizadas em todas as entidades principais.
* Chaves Estrangeiras (FK) são usadas para manter a integridade referencial.
* Tabelas associativas são usadas para modelar relacionamentos n\:n.
* O modelo está normalizado para evitar redundância e garantir integridade dos dados.

### Diagrama Lógico

&emsp; Abaixo está o diagrama lógico gerado com o BRModelo:

<div align="center">
  <sub>Figura X - Modelo lógico de dados</sub> <br>

  <img 
      src="./assets/4_modelagem_de_dados/logical_model.png" alt="Modelo lógico de dados" 
      style="max-width: 650px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

> **Arquivo do projeto (formato `.brM3`, gerado pelo software BRModelo):** localizado em `docs\assets\4_modelagem_de_dados\logical_model.brM3`, a partir da raiz do repositório.

&emsp; Durante as discussões do grupo na Sprint 3, fizemos algumas alterações no modelo lógico, pois optamos por seguir com um banco não relacional para a tabela de recomendações para o cliente, fazendo com que cada cliente seja uma coleção. Abaixo está o diagrama lógico atualizado e um exemplo de como será o nosso banco não relacional:

<div align="center">
  <sub>Figura X - Modelo lógico de dados atualizado</sub> <br>

  <img 
      src="./assets/4_modelagem_de_dados/logical_model_sprint3.png" alt="Modelo lógico de dados Atualizado" 
      style="max-width: 650px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

<div align="center">
  <sub>Figura X - Exemplo banco de dados não relacional</sub> <br>

  <img 
      src="./assets/4_modelagem_de_dados/logical_model_nao_relacional.jpg" alt="Banco de Dados Não Relacional" 
      style="max-width: 650px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

## 4.4 Modelo Físico de Dados

_conteúdo_

**Nota:** Insira uma explicação e direcionamento para o readme.md da pasta database.

# 5. Solução Técnica (Design)

&emsp; Nesta seção, será apresentada a solução técnica para este presente projeto. A solução foi elaborada de acordo com os requisitos funcionais e não funcionais definidos no escopo do projeto. Ela abrange desde a definição dos casos de uso até a modelagem da arquitetura e dos componentes que compõem o sistema.

## 5.1 Diagrama de Casos de Uso

&emsp; O diagrama de casos de uso abaixo apresenta uma visão clara das interações entre o **Assessor de Investimentos** (ator principal) e o **Pati (Plataforma de Adequação de Tipo de Investidor)**, com suporte do **Supabase Auth** (ator secundário). O Supabase Auth é um serviço externo de autenticação, garantindo que o acesso ao sistema seja seguro e controlado. 

&emsp; É importante destacar que este diagrama foi pensado com foco apenas na principal persona da aplicação, o Assessor de Investimentos, uma vez que o sistema se trata de um MVP (Produto Mínimo Viável). Isso significa que a prioridade é gerar o máximo de valor em menor tempo. Outros atores, como administradores, foram intencionalmente deixados de lado, mas seriam imprescindíveis em uma versão completa da solução voltada ao mercado.

<div align="center">
  <sub>Figura X - Diagrama UML dos Casos de Uso</sub> <br>

<img
   src="./assets/5_solucao_tecnica/use_case_diagram.jpeg" alt="Diagrama UML dos Casos de Uso"
   style="max-width: 550px; width: 100%; height: auto;">

<sup>Fonte: Material produzido pelos autores (2025).</sup>

</div>

### Descrição dos Casos de Uso

#### 1. Autenticar no Sistema (UC01)

* **Descrição:** O assessor inicia a interação com o sistema realizando o login, com suporte do Supabase Auth para garantir a segurança da autenticação.
* **Requisitos Funcionais:** RF01 - O sistema deve permitir que o assessor de investimentos faça login com credenciais seguras, utilizando o serviço de autenticação do Supabase Auth.
* **História de Usuário:** Como Carlos Silva, eu quero fazer login com credenciais para garantir que só eu tenha acesso aos dados dos meus clientes.

#### 2. Visualizar Resumo de Clientes (UC02)

* **Descrição:** Após a autenticação, o assessor pode acessar um resumo com informações gerais dos clientes.
* **Requisitos Funcionais:** RF03 - O sistema deve apresentar o número total de clientes gerenciados e a quantidade de clientes com inconformidade de perfil.
* **História de Usuário:** Como Carlos Silva, eu quero ver, logo após o login, o número total de clientes que gerencio e quantos estão com inconformidade de perfil.

#### 3. Visualizar Lista de Clientes Inconformes (UC03)

* **Descrição:** Permite que o assessor visualize uma lista de clientes que não estão em conformidade com o perfil declarado.
* **Requisitos Funcionais:** RF04 - O sistema deve listar os clientes com inconformidades em ordem de prioridade.
* **História de Usuário:** Como Carlos Silva, eu quero que o sistema liste os clientes com inconformidades em ordem de prioridade.

#### 4. Visualizar Detalhes do Cliente (UC04)

* **Descrição:** O assessor pode acessar informações detalhadas de um cliente específico.
* **Requisitos Funcionais:** RF07 - O sistema deve permitir ao assessor clicar em um cliente específico para visualizar o perfil cadastrado versus perfil real.
* **História de Usuário:** Como Carlos Silva, eu quero clicar em um cliente específico para visualizar o perfil cadastrado versus o perfil real.

#### 5. Analisar Conformidade dos Perfis (UC05)

* **Descrição:** O sistema compara o perfil cadastrado do cliente com o perfil real calculado e sinaliza os casos fora do perfil.
* **Requisitos Funcionais:** RF06 - O sistema deve comparar o perfil cadastrado do cliente com o perfil real.
* **História de Usuário:** Como Carlos Silva, eu quero que o sistema compare o perfil cadastrado do cliente com o perfil real e sinalize os casos fora do perfil.

#### 6. Gerar Recomendação de Ativos (UC06)

* **Descrição:** O sistema gera sugestões de ativos financeiros para os clientes, com base em seu perfil.
* **Requisitos Funcionais:** RF08 - O sistema deve gerar recomendações para realinhamento da carteira.
* **História de Usuário:** Como Carlos Silva, eu quero receber recomendações automáticas de realinhamento da carteira, incluindo sugestões de ativos.

### Relações de Inclusão e Extensão

* **UC02 inclui UC05:** A visualização do resumo de clientes requer a análise de conformidade dos perfis.
* **UC04 estende UC06:** A visualização dos detalhes do cliente permite, de forma opcional, o acesso às recomendações de ativos previamente geradas e persistidas.

## 5.2 Diagrama de Classes

&emsp; O diagrama de classes abaixo representa o **modelo de domínio** do sistema de adequação de perfil de investimento. O modelo foi projetado buscando um equilíbrio entre implementação e abstração, de forma que não apenas os conceitos sejam bem representados, mas que também permita o entendimento de como esses conceitos serão implementados na prática.

<div align="center">
  <sub>Figura X - Diagrama UML de Classe</sub> <br>

  <img 
      src="./assets/5_solucao_tecnica/5.2_diagrama_de_classes/class_diagram.jpg" alt="Diagrama UML de Classe" 
      style="max-width: 1000px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

> **Nota:** Caso a imagem acima não esteja perfeitamente visível em termos de detalhes, é possível acessar uma versão vetorizada do diagrama em formato `.SVG`, garantindo maior clareza: [Diagrama de Classes - SVG](./assets/5_solucao_tecnica/5.2_diagrama_de_classes/class_diagram.svg).

<!-- 
O diagrama acima foi desenvolvido em PlatUMl. Para fins de ajustes futuros ou análise ainda mais aprofundada, a seguir está o código:

@startuml

class Client {
- name: String
- phone_number: String
- email: String
- risk_profile: String
- last_meeting: Date
- non_compliance: Boolean
- account: String
+ getCalculatedRiskProfile(): String
+ getInvestments(): List
+ isCompliant(): Boolean
}

class Advisor {
- name: String
- email: String
- phone_number: String
+ getClientList(): List
+ getNonCompliantClients(): List
+ getDashboardData(): Object
}

class Investment {
- name: String
- risk: String
- type: String
+ getRiskLevel(): String
+ isCompatibleWithProfile(profile: String): Boolean
}

class ClientInvestment {
- amount: Decimal
- quantity: Integer
- purchase_date: Date
+ calculateTotalValue(): Decimal
+ getRiskContribution(): String
}

class ClientRecommendation {
- recommendation_score: Double
- date_generated: Date
- accepted: Boolean
+ getJustification(): String
}

class ComplianceService {
+ checkCompliance(client: Client): Boolean
+ getComplianceStatus(client: Client): String
+ getComplianceMetrics(advisor: Advisor): Object
+ getComplianceIssues(client: Client): List
}

class RecommendationService {
+ generateRecommendations(client: Client): List
+ scoreInvestments(client: Client): List
}

class PortfolioService {
+ calculatePortfolioRisk(investments: List): String
+ compareProfiles(calculatedProfile: String, declaredProfile: String): Boolean
+ getAssetAllocation(investments: List): Object
}

class AuthService {
+ login(email: String, password: String): Advisor
+ logout(): void
+ validateSession(): Boolean
}

class DashboardService {
+ getOverviewData(advisor: Advisor): Object
+ getClientDetails(client: Client): Object
+ getClientRecommendations(client: Client): List
}

Client -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) Advisor : "1 atende 1"
Client -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) ClientInvestment : "1 possui 0.."
Investment -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) ClientInvestment : "1 inclui em 0.."
Client -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) ClientRecommendation : "1 recebe 0.."
Investment -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) ClientRecommendation : "1 recomendado em 0.."

ComplianceService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) Client : "1 analisa 0.."
ComplianceService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) PortfolioService : "1 usa 1"
RecommendationService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) Client : "1 recomenda para 0.."
PortfolioService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) ClientInvestment : "1 analisa 0.."
DashboardService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) Advisor : "1 utiliza dados de 1"
DashboardService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) ComplianceService : "1 usa 1"
DashboardService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) RecommendationService : "1 usa 1"
AuthService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) Advisor : "1 autentica 0.."

@enduml
 -->

&emsp; A seguir, apresento a documentação descritiva do diagrama de classes fornecido. Ela detalha o propósito de cada classe, os métodos e atributos principais, além das relações e cardinalidades entre elas, justificando as decisões de modelagem adotadas.

## **1. Classe `Client`**

A classe `Client` representa um cliente de investimentos. Armazena informações pessoais e de relacionamento com o assessor, como nome, telefone, e-mail, perfil de risco, data da última reunião e status de conformidade.

* **Atributos**:

  * `name`, `phone_number`, `email`: dados básicos de contato.
  * `risk_profile`: perfil de risco declarado do cliente.
  * `last_meeting`: último encontro registrado com o assessor.
  * `non_compliance`: indica se o cliente está fora de conformidade.
  * `account`: número da conta associada.

* **Métodos**:

  * `getCalculatedRiskProfile()`: retorna o perfil de risco calculado com base nos investimentos.
  * `getInvestments()`: lista os investimentos do cliente.
  * `isCompliant()`: verifica se o cliente está em conformidade.

* **Relacionamentos**:

  * Cada cliente é atendido por exatamente **um** `Advisor` (1:1).
  * Pode ter **múltiplos** `ClientInvestment` (1:0..\*).
  * Pode receber **várias** `ClientRecommendation` (1:0..\*).

## **2. Classe `Advisor`**

Representa o assessor de investimentos responsável pelo atendimento aos clientes.

* **Atributos**:

  * `name`, `email`, `phone_number`: dados de identificação e contato.

* **Métodos**:

  * `getClientList()`: retorna os clientes sob sua responsabilidade.
  * `getNonCompliantClients()`: lista clientes fora de conformidade.
  * `getDashboardData()`: fornece informações consolidadas para interface visual (dashboard).

* **Relacionamentos**:

  * Um assessor pode ter **vários** clientes associados.
  * Utilizado pelas classes de serviço como `DashboardService` e `ComplianceService`.

## **3. Classe `Investment`**

Modela os produtos de investimento disponíveis.

* **Atributos**:

  * `name`, `risk`, `type`: identificação e classificação do investimento.

* **Métodos**:

  * `getRiskLevel()`: retorna o nível de risco do ativo.
  * `isCompatibleWithProfile(profile: String)`: verifica compatibilidade com o perfil de risco do cliente.

* **Relacionamentos**:

  * Relaciona-se com `ClientInvestment` como um produto pode estar presente em várias aplicações.
  * Pode estar presente em múltiplas `ClientRecommendation`.

## **4. Classe `ClientInvestment`**

Representa um investimento realizado por um cliente, contendo valores específicos da aplicação.

* **Atributos**:

  * `amount`, `quantity`, `purchase_date`: valores investidos e data de aquisição.

* **Métodos**:

  * `calculateTotalValue()`: retorna o valor total do investimento.
  * `getRiskContribution()`: calcula a contribuição deste investimento ao risco total do portfólio.

* **Relacionamentos**:

  * Está ligado a um `Client` e a um `Investment`.

## **5. Classe `ClientRecommendation`**

Modela recomendações de investimento feitas a um cliente.

* **Atributos**:

  * `recommendation_score`, `date_generated`, `accepted`: pontuação da recomendação, data e status de aceitação.

* **Métodos**:

  * `getJustification()`: retorna a justificativa da recomendação com base em perfil, risco, etc.

* **Relacionamentos**:

  * Associada a um `Client` e a um `Investment`.

## **6. Classe `ComplianceService`**

Classe de serviço responsável por verificar conformidade do cliente com normas e políticas.

* **Métodos**:

  * `checkCompliance(client)`: retorna `true` se o cliente estiver em conformidade.
  * `getComplianceStatus(client)`: retorna descrição textual do status.
  * `getComplianceMetrics(advisor)`: retorna métricas globais do assessor.
  * `getComplianceIssues(client)`: lista problemas de conformidade identificados.

* **Relacionamentos**:

  * Usa a classe `PortfolioService` para analisar riscos e conformidade.
  * Atua sobre `Client` diretamente.

## **7. Classe `RecommendationService`**

Responsável por gerar e pontuar recomendações de investimento para clientes.

* **Métodos**:

  * `generateRecommendations(client)`: gera uma lista de recomendações com base no perfil.
  * `scoreInvestments(client)`: atribui pontuações a investimentos com base na compatibilidade.

* **Relacionamentos**:

  * Atua sobre `Client`.
  * Usado por `DashboardService`.

## **8. Classe `PortfolioService`**

Fornece funcionalidades analíticas sobre portfólios de clientes.

* **Métodos**:

  * `calculatePortfolioRisk(investments)`: calcula o risco agregado de um conjunto de investimentos.
  * `compareProfiles(calculatedProfile, declaredProfile)`: verifica consistência entre perfil declarado e calculado.
  * `getAssetAllocation(investments)`: fornece alocação de ativos do portfólio.

* **Relacionamentos**:

  * Atua sobre `ClientInvestment`.
  * Utilizado por `ComplianceService`.

## **9. Classe `AuthService`**

Controla o acesso de usuários (assessores).

* **Métodos**:

  * `login(email, password)`: autentica e retorna um `Advisor`.
  * `logout()`: finaliza a sessão.
  * `validateSession()`: valida se a sessão é ativa.

* **Relacionamentos**:

  * Está ligado a `Advisor`.

## **10. Classe `DashboardService`**

Centraliza dados para exibição visual e acompanhamento.

* **Métodos**:

  * `getOverviewData(advisor)`: fornece dados gerais do assessor.
  * `getClientDetails(client)`: informações detalhadas do cliente.
  * `getClientRecommendations(client)`: retorna recomendações do cliente.

* **Relacionamentos**:

  * Usa `ComplianceService` e `RecommendationService`.
  * Atende ao `Advisor`.

## **Relacionamentos e Cardinalidade**

* **Client → Advisor**: 1:1 — cada cliente possui exatamente um assessor, refletindo uma abordagem personalizada.
* **Client → ClientInvestment**: 1:0..\* — um cliente pode ter nenhum ou múltiplos investimentos.
* **Investment → ClientInvestment**: 1:0..\* — um investimento pode estar presente em várias aplicações diferentes.
* **Client → ClientRecommendation**: 1:0..\* — várias recomendações podem ser feitas a um mesmo cliente.
* **Investment → ClientRecommendation**: 1:0..\* — uma mesma recomendação pode ser relacionada a vários investimentos.
* **ComplianceService → Client**: 1:0..\* — avalia múltiplos clientes.
* **ComplianceService → PortfolioService**: 1:1 — depende de um serviço analítico para decisões.
* **RecommendationService → Client**: 1:0..\* — gera recomendações para vários clientes.
* **PortfolioService → ClientInvestment**: 1:0..\* — analisa múltiplas aplicações.
* **DashboardService → Advisor**: 1:1 — fornece dados para um assessor específico.
* **DashboardService → (ComplianceService, RecommendationService)**: 1:1 — usa ambos os serviços para compor os dados do painel.
* **AuthService → Advisor**: 1:0..\* — autentica vários assessores.

&emsp; A estrutura proposta separa entidades de domínio (como `Client`, `Advisor` e `Investment`) de serviços especializados (como `ComplianceService` e `RecommendationService`), alinhando-se ao princípio da **responsabilidade única (SPR)**. Essa divisão garante que cada componente tenha uma finalidade clara: as entidades gerenciam dados essenciais, enquanto os serviços concentram regras e operações complexas, aumentando a coesão interna e facilitando a reutilização em diferentes contextos.  

&emsp; A classe `ClientInvestment` atua como intermediária entre `Client` e `Investment`, oferecendo um mecanismo para controlar detalhes específicos de cada aplicação, como datas, valores ou condições personalizadas. Essa modelagem permite flexibilidade para adicionar validações ou métricas específicas sem modificar as entidades principais, preservando a integridade do domínio.  

&emsp; As relações de cardinalidade (como um cliente receber múltiplas recomendações ou um assessor gerenciar diversos clientes) refletem as dinâmicas reais do contexto de investimentos. Essa precisão na modelagem garante que o sistema reproduza as regras de negócio de forma consistente, evitando simplificações excessivas.  

&emsp; Por fim, a lógica de negócio complexa – como análises de conformidade ou algoritmos de recomendação – foi encapsulada nos métodos das classes de serviço. Essa decisão mantém as entidades enxutas e focadas em representar dados, enquanto os serviços manipulam operações críticas, facilitando a manutenção e a evolução do sistema.

## 5.2.1 Diagrama de Sequência

&emsp; Para fins de validação da **viabilidade e legitimidade** do diagrama de classes proposto, foi elaborado um **diagrama de sequência** que simula um fluxo completo de interação entre os principais componentes do sistema. Este fluxo está diretamente associado aos seguintes **casos de uso**:

* **UC04 – Visualizar Detalhes do Cliente**
* **UC05 – Analisar Conformidade dos Perfis**
* **UC06 – Gerar Recomendação de Ativos**

&emsp; Esses três casos de uso representam o **caminho mais crítico e complexo** do sistema, pois envolvem múltiplas classes, serviços e validações — desde o acesso aos dados do cliente até a geração de recomendações personalizadas de investimentos. Portanto, validar esse fluxo garante que a modelagem de classes sustenta adequadamente os requisitos de negócio mais relevantes, além de centra-se na funcionalidade que mais gera valor no MVP: a recomendação.

#### Diagrama de Sequência

&emsp; A imagem abaixo ilustra o diagrama de sequência que representa o fluxo de execução:

<div align="center">
  <sub>Figura X - Diagrama UML de Sequência</sub> <br>

  <img 
      src="./assets/5_solucao_tecnica/5.2_diagrama_de_classes/sequencie_diagram.png" alt="Diagrama UML de Sequência" 
      style="max-width: 1200px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

> **Nota:** Caso a imagem acima não esteja perfeitamente visível em termos de detalhes, é possível acessar uma versão vetorizada do diagrama em formato `.SVG`, garantindo maior clareza: [Diagrama de Sequência - SVG](./assets/5_solucao_tecnica/5.2_diagrama_de_classes/sequencie_diagram.svg).

<!-- 
Os diagramas acima foram feitos utilizando mermaid. A seguir está o código que gera o diagrama acima:

classDiagram
    %% Entidades principais
    class Client {
        -String name
        -String phone_number
        -String email
        -String risk_profile
        -Date last_meeting
        -Boolean non_compliance
        -String account
        +String getCalculatedRiskProfile()
        +List~ClientInvestment~ getInvestments()
        +Boolean isCompliant()
    }

    class Advisor {
        -String name
        -String email
        -String phone_number
        +List~Client~ getClientList()
        +List~Client~ getNonCompliantClients()
        +Object getDashboardData()
    }

    class Investment {
        -String name
        -String risk
        -String type
        +String getRiskLevel()
        +Boolean isCompatibleWithProfile(String profile)
    }

    class ClientInvestment {
        -Decimal amount
        -Integer quantity
        -Date purchase_date
        +Decimal calculateTotalValue()
        +String getRiskContribution()
    }

    class ClientRecommendation {
        -Double recommendation_score
        -Date date_generated
        -Boolean accepted
        +String getJustification()
    }

    class ComplianceService {
        +Boolean checkCompliance(Client client)
        +String getComplianceStatus(Client client)
        +Object getComplianceMetrics(Advisor advisor)
        +List~String~ getComplianceIssues(Client client)
    }

    class RecommendationService {
        +List~Investment~ generateRecommendations(Client client)
        +List~ClientRecommendation~ scoreInvestments(Client client)
    }

    class PortfolioService {
        +String calculatePortfolioRisk(List~ClientInvestment~ investments)
        +Boolean compareProfiles(String calculatedProfile, String declaredProfile)
        +Object getAssetAllocation(List~ClientInvestment~ investments)
    }

    class AuthService {
        +Advisor login(String email, String password)
        +void logout()
        +Boolean validateSession()
    }

    class DashboardService {
        +Object getOverviewData(Advisor advisor)
        +Object getClientDetails(Client client)
        +List~ClientRecommendation~ getClientRecommendations(Client client)
    }

    %% Relacionamentos
    Client "1" -- "1" Advisor : atendido por >
    Client "1" -- "0..*" ClientInvestment : possui >
    Investment "1" -- "0..*" ClientInvestment : < incluído em
    Client "1" -- "0..*" ClientRecommendation : recebe >
    Investment "1" -- "0..*" ClientRecommendation : < recomendado
    
    ComplianceService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) Client : analisa
    ComplianceService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) PortfolioService : usa
    RecommendationService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) Client : recomenda para
    PortfolioService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) ClientInvestment : analisa
    DashboardService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) Advisor : utiliza dados
    DashboardService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) ComplianceService : utiliza
    DashboardService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) RecommendationService : utiliza
    AuthService -- > (foi dado um espaço antes de ">" porque o comentário estava sendo quebrado) Advisor : autentica
-->

1. **Assessor seleciona um cliente** na interface (UC04).
2. A **Interface do Usuário (UI)** solicita ao `DashboardService` os **detalhes do cliente**.
3. O `DashboardService` consulta a classe `Client` para recuperar os investimentos do cliente.
4. Em seguida, inicia-se o processo de **verificação de conformidade** (UC05):

   * O `DashboardService` aciona o `ComplianceService`.
   * O `ComplianceService` invoca o `PortfolioService` para calcular o **perfil de risco real** com base nos investimentos.
   * Com esse perfil, verifica se o cliente está **em conformidade** com o perfil declarado.
5. Em paralelo, o `DashboardService` aciona o `RecommendationService` para **gerar recomendações de ativos** (UC06):

   * O serviço consulta o **perfil de risco calculado** do cliente.
   * Avalia os investimentos (`Investment`) com base na **compatibilidade com esse perfil**.
   * Atribui pontuações e retorna uma **lista de recomendações** (`ClientRecommendation`).

&emsp; Por fim, todos os dados são enviados de volta à interface, que apresenta ao assessor:

* Os dados cadastrais do cliente
* A lista de investimentos atuais
* O status de conformidade
* E as recomendações de realinhamento

&emsp; Esse diagrama de sequência demonstra que o diagrama de classes **provê suporte completo** ao fluxo mais exigente do sistema, promovendo clareza estrutural, coesão entre serviços e entidades, e facilidade de expansão futura. A integração entre as UCs UC04, UC05 e UC06 dentro do fluxo evidencia que a modelagem atende aos principais objetivos do sistema: **eficiência no atendimento, conformidade regulatória** e **recomendações inteligentes para os clientes**.

## 5.3 Diagrama de Componentes

&emsp; A imagem abaixo representa o diagrama de componentes da nossa solução que desenhamos na Sprint 2, projetado com uma arquitetura orientada a serviços:

<div align="center">
  <sub>Figura X - Diagrama de Componentes </sub> <br>

  <img src="./assets/5_solucao_tecnica/component_diagram.png" alt="Diagrama de Componentes" style="max-width: 550px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

&emsp;O diagrama apresentado está estruturado em três partes principais:  
1. **Frontend (Interface do App)**  
2. **Backend (Serviços e Lógica de Negócio)**  
3. **Banco de Dados Relacional**

&emsp;Cada parte é composta por componentes que se comunicam entre si por meio do API Gateway, formando a arquitetura da solução.

### Camada de Frontend

**Interface do App (React Native):**  
&emsp;Representa a interface utilizada pelos investidores e assessores. Permite visualizar recomendações, realizar reclassificações de perfil, acessar informações consolidadas dos clientes, entre outras funcionalidades. Todas as requisições são enviadas ao backend por meio do API Gateway.

### Camada de Backend (C#)

&emsp;O backend é responsável pelo processamento das regras de negócio e pela comunicação com o banco de dados. Está organizado nos seguintes componentes:

- **API Gateway:** Porta de entrada de todas as requisições vindas do frontend, sendo responsável por direcionar as chamadas para os serviços adequados.

- **Serviço de Autenticação:** Realiza a validação de credenciais dos usuários, garantindo que apenas usuários autenticados possam acessar os demais serviços.

- **Serviço de Gerenciamento de Clientes:** Responsável por gerenciar os dados dos investidores, incluindo a listagem e consulta com base em critérios como grau de conformidade com o perfil ou patrimônio investido.

- **Serviço de Classificação:** É responsável por classificar os investidores com base nos dados disponíveis e avaliar se suas carteiras estão de acordo com os respectivos perfis.

- **Serviço de Recomendação:** Gera sugestões de ajustes ou investimentos para adequar a carteira do investidor ao seu perfil, considerando boas práticas de alocação e conformidade regulatória.

### Banco de Dados Relacional

&emsp;Componente único responsável por armazenar todos os dados do sistema, incluindo:
- Informações de autenticação
- Dados cadastrais de investidores
- Perfis de classificação
- Composição das carteiras
- Recomendações geradas

### Relacionamentos entre os Componentes

#### Fluxo Principal

- **Frontend → API Gateway:**  
  O aplicativo envia requisições  para login, acesso a dados dos clientes, visualização de recomendações, entre outras funcionalidades.

- **API Gateway → Serviço de Autenticação:**  
  Valida as credenciais dos usuários. Após autenticação bem-sucedida, um token (ex: JWT) é gerado e utilizado nas próximas requisições.

- **API Gateway → Serviço de Gerenciamento de Clientes:**  
  Permite a listagem e consulta de investidores, com base em critérios relevantes para o assessor.

- **API Gateway → Serviço de Classificação:**  
  Classifica os perfis de investidores e avalia a conformidade de suas carteiras com base nos dados disponíveis.

- **API Gateway → Serviço de Recomendação:**  
  Solicita sugestões de alocação de ativos para adequação ao perfil do cliente.

- **Serviços → Banco de Dados Relacional:**  
  Todos os serviços do backend leem e escrevem dados no banco relacional conforme suas responsabilidades (ex: salvar recomendações, atualizar perfis, consultar carteiras).

- **Resposta para o App:**  
  Os dados processados são enviados de volta ao frontend via API Gateway.

&emsp;Esse fluxo assegura a integração entre as funcionalidades da solução, mantendo uma organização orientada a serviços, com divisão de responsabilidades entre os componentes.

&emsp;Porém, durante o desenvolvimento da Sprint 3, identificamos a necessidade de refinar nossa arquitetura para melhor atender aos requisitos funcionais e não funcionais do sistema. O diagrama de componentes foi atualizado para refletir uma abordagem mais robusta e escalável.

<div align="center">
  <sub>Figura X - Diagrama de Componentes Atualizado</sub> <br>

  <img src="./assets/5_solucao_tecnica/component_diagram_sprint3.png" alt="Diagrama de Componentes Atualizado" style="max-width: 550px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

### Principais Mudanças Implementadas

#### 1. Integração com APIs Externas

&emsp;A principal adição ao sistema foi a incorporação de **APIs Externas**, que agora fazem parte integral da arquitetura:

* **API Externa de Autenticação:** Responsável por integrar com serviços de autenticação externos, proporcionando maior segurança e flexibilidade no processo de login e validação de usuários.
* **API Externa de Clientes:** Permite a integração com sistemas externos para obtenção de dados atualizados dos clientes, garantindo informações sempre sincronizadas e precisas.

#### 2. Reestruturação dos Serviços Backend

&emsp;O backend passou por uma reorganização significativa:

* **Consolidação do Serviço de Usuários:** O antigo "Serviço de Autenticação" e "Serviço de Gerenciamento de Clientes" foram consolidados em um único **Serviço de Usuários**, otimizando o gerenciamento de identidades e perfis.
* **Manutenção dos Serviços Especializados:** Os serviços de Classificação e Recomendação foram mantidos como componentes independentes, preservando a separação de responsabilidades e facilitando a manutenção.

#### 3. Introdução de Banco de Dados Não Relacional

&emsp;Uma das mudanças mais significativas foi a adição de um **Banco de Dados Não Relacional** ao lado do banco relacional existente:

* **Arquitetura Híbrida:** O sistema agora utiliza uma abordagem de armazenamento híbrida, combinando as vantagens de ambos os tipos de banco de dados.
* **Banco Relacional:** Mantém dados estruturados como informações de usuários, perfis de investimento e relacionamentos entre entidades.
* **Banco Não Relacional:** Armazena dados não estruturados como logs de recomendações, dados de análise comportamental e informações de cache para melhor performance.

### Novo Fluxo de Arquitetura

**Camada de Integração Externa**

&emsp;As APIs Externas de Autenticação e Clientes fornecem dados e serviços externos essenciais para o funcionamento do sistema, estabelecendo conexões seguras com sistemas de terceiros e garantindo a sincronização de informações críticas.

**Camada de Frontend**

&emsp;A Interface do App (React Native) mantém suas funcionalidades originais, agora com maior robustez devido às integrações externas. O frontend continua sendo responsável pela experiência do usuário, mas agora se beneficia de dados mais atualizados e processos de autenticação mais seguros.

**Camada de Backend (C#)**

&emsp;O backend foi reorganizado para melhor distribuição de responsabilidades. O Serviço de Usuários gerencia tanto a autenticação quanto o cadastro e perfil dos usuários de forma unificada, enquanto os Serviços de Classificação e Recomendação mantêm suas especializações para análise de perfis de investimento e geração de recomendações personalizadas.

**Camada de Dados**

&emsp;A camada de dados agora opera com uma arquitetura híbrida, onde o Banco de Dados Relacional mantém dados estruturados e transacionais, enquanto o Banco de Dados Não Relacional armazena dados analíticos e de cache, proporcionando flexibilidade e performance otimizada.

### Benefícios das Mudanças

**Escalabilidade Aprimorada**

&emsp;A separação entre bancos relacionais e não relacionais permite otimização específica para diferentes tipos de dados, enquanto as APIs externas reduzem a carga no sistema interno, distribuindo o processamento de forma mais eficiente.

**Flexibilidade de Integração**

&emsp;A nova arquitetura facilita a conexão com sistemas externos de terceiros e permite atualizações independentes dos componentes, proporcionando maior agilidade no desenvolvimento e manutenção.

**Performance Otimizada**

&emsp;O banco não relacional oferece acesso mais rápido a dados de análise e implementa cache distribuído que melhora significativamente os tempos de resposta do sistema.

**Manutenibilidade**

&emsp;Os serviços tornaram-se mais coesos e com responsabilidades bem definidas, facilitando tanto o testing quanto o deployment independente de componentes.

### Relacionamentos entre os Componentes

**Fluxo Principal Atualizado**

* **Frontend → Backend:** O aplicativo continua enviando requisições através do API Gateway, mas agora se beneficia de dados enriquecidos pelas integrações externas.
* **APIs Externas → Serviço de Usuários:** As APIs externas alimentam o sistema com dados atualizados de autenticação e clientes, garantindo informações sempre sincronizadas.
* **Serviço de Usuários → Bancos de Dados:** Utiliza tanto o banco relacional para dados estruturados quanto o não relacional para cache e analytics.
* **Serviços de Classificação e Recomendação → Bancos:** Acessam ambos os bancos conforme a natureza dos dados necessários para suas operações específicas.

### Considerações Técnicas

&emsp;A nova arquitetura mantém os princípios de orientação a serviços, mas adiciona camadas de abstração que proporcionam maior flexibilidade operacional. A comunicação entre componentes continua sendo orquestrada pelo API Gateway, garantindo consistência no roteamento de requisições e aplicação de políticas de segurança.

&emsp;Esta evolução adequa o sistema para o que estamso pensando como solução final e prepara ele para futuras expansões e integrações, mantendo a robustez necessária para um ambiente de produção empresarial, ao mesmo tempo que oferece a flexibilidade necessária para adaptações futuras conforme as necessidades do negócio evoluem.

## 5.4 Linguagens e Tecnologias Utilizadas

&emsp; A escolha da stack tecnológica para o desenvolvimento do Pati foi guiada por critérios técnicos, práticos e também estratégicos, considerando tanto as exigências do projeto quanto o contexto real do parceiro BTG Pactual S.A. Embora a definição das principais tecnologias tenha sido estabelecida como parte dos requisitos acadêmicos, entendemos que cada componente escolhido possui características valiosas e complementares, permitindo construir uma solução sólida, coesa e alinhada com os objetivos propostos.

### Backend: C# com ASP.NET Core

&emsp; O **C#** é uma linguagem fortemente tipada, orientada a objetos e com suporte robusto ao paradigma funcional, ideal para projetos que exigem precisão, segurança e manutenibilidade — características fundamentais em sistemas voltados ao setor financeiro. O uso do **framework ASP.NET Core** se justifica pela sua ampla adoção no mercado corporativo, especialmente em instituições financeiras, incluindo grandes players como o próprio **BTG Pactual**, onde essa stack é largamente utilizada internamente. Isso torna a solução desenvolvida potencialmente compatível com os ambientes reais de produção do cliente, facilitando futuras adaptações ou integrações caso o protótipo evolua para um produto mais maduro.

&emsp; Além disso, o ASP.NET oferece recursos essenciais como injeção de dependência, middlewares customizáveis, controle de rotas bem estruturado e suporte nativo a autenticação JWT, tudo isso sem depender de bibliotecas externas complexas. Esses fatores são imprescindíveis para garantir um backend organizado, seguro e eficiente, mesmo em um ambiente acadêmico com tempo e recursos limitados.

### Processamento de Recomendação: C# com ML.NET

&emsp; O módulo responsável pelo processamento de recomendação de ativos foi implementado também em **C#**, aproveitando o ecossistema .NET através da biblioteca **ML.NET**. Apesar de Python ser tradicionalmente mais utilizado em projetos de machine learning, a decisão por usar C# nesse caso tem como base a coerência arquitetural: ter todo o backend em uma única plataforma reduz custos de integração, facilita a manutenção e elimina a necessidade de interfaces entre linguagens distintas, o que poderia gerar overhead e fragilidades desnecessárias.

&emsp; O ML.NET permite carregar modelos pré-treinados (por exemplo, usando Python), serializar e rodar inferências localmente dentro da aplicação backend, tudo isso mantendo a simplicidade operacional e a performance adequada para o escopo do projeto. Além disso, é uma ferramenta que está ganhando força na indústria, especialmente em contextos corporativos onde a interoperabilidade com outros serviços .NET é crítica.

### Banco de Dados e Autenticação: Supabase

&emsp; Como banco de dados, autenticação e armazenamento, a tecnologia definida é o **Supabase**. Trata-se de uma alternativa open-source ao Firebase, baseada no PostgreSQL, que oferece tanto persistência de dados, como também autenticação de usuários, controle de permissões e até sincronização em tempo real. A utilização do Supabase é uma escolha assertiva, pois simula bem um ambiente de produção, mesmo em escala acadêmica.

&emsp; Sua camada de segurança permite configurar políticas de acesso no nível de linha e coluna, algo relevante quando lidamos com dados sensíveis como informações de clientes e carteiras de investimento.

### Frontend: React Native com Expo e padrão MVVM

&emsp; Para o frontend mobile, utilizaremos o **React Native**, um framework de desenvolvimento multiplataforma amplamente utilizado por empresas de tecnologia e fintechs. Ele permite escrever código uma única vez e gerar builds funcionais tanto para Android quanto para iOS, otimizando o tempo de desenvolvimento e facilitando testes em dispositivos reais. A integração com APIs RESTful é natural e eficiente, o que combina muito bem com nosso backend em C#.

&emsp; Utilizarmeos também o **Expo**, uma abstração sobre o React Native, que traz agilidade significativa ao processo de desenvolvimento. Com ele, é possível debugar diretamente no dispositivo móvel, acessar câmera, sensores e outros recursos nativos com poucas linhas de código, além de simplificar o processo de build e deploy para testes internos.

&emsp; No aspecto arquitetural, seguimos o padrão **MVVM (Model-View-ViewModel)**. Esse padrão ajuda a organizar o código do frontend de maneira clara, separando a lógica de interface (View) da manipulação de estados e regras de negócio (ViewModel), enquanto mantém a camada de dados (Model) desacoplada. Essa modularidade facilita a manutenção, os testes e a escalabilidade do app, mesmo que ainda estejamos em fase de prototipagem. Para estudantes de engenharia de software, esse tipo de prática é fundamental para internalizar boas práticas reais de arquitetura.

### Infraestrutura e DevOps: Docker e GitHub

&emsp; Adotaremos o uso do **Docker** para conteinerizar os componentes do backend e do Supabase. Isso garante maior consistência entre ambientes de desenvolvimento e execução, além de facilitar o compartilhamento e a replicação do ambiente por todos os membros da equipe. A utilização de containers também aproxima o projeto de um cenário real de desenvolvimento de software, preparando-nos para práticas modernas de entrega contínua.

&emsp; Já para versionamento de código e colaboração, utilizamos o **GitHub**, plataforma centralizada e amplamente adotada no mercado, inclusive os membros do grupo já estão bastante habituados a sua utilização. Ele permite utilizar fluxos de trabalho Git convencionais, como branches, pull requests e code reviews, além de integrar com outras ferramentas de CI/CD futuramente, caso necessário.

<br/>

&emsp; Apesar de termos seguido um conjunto de tecnologias definido previamente, compreendemos profundamente cada escolha e suas implicações, ajustando-as ao contexto do projeto e ao perfil do cliente final. A stack adotada demonstrou-se altamente pertinente: desde a forte presença do .NET no ecossistema do BTG Pactual, passando pela flexibilidade do React Native no desenvolvimento mobile, até a segurança e produtividade trazidas pelo Supabase e pelas práticas de versionamento e containerização.

Essa experiência nos mostrou que seguir uma stack determinada não significa perder a capacidade de pensar criticamente sobre ela. Ao contrário, nos ensinou a justificar, adaptar e extrair valor máximo de cada tecnologia, mesmo quando elas já vêm pré-definidas; cenário muito comum no mercado.

### APIs Externas: Supabase Auth e Randomuser

#### Supabase Auth

&emsp; Na nossa aplicação, utilizamos a Supabase Auth como o serviço de autenticação responsável por gerenciar o cadastro, login e recuperação de senha dos usuários. Através dela, garantimos que o acesso às funcionalidades do app seja seguro e controlado, utilizando tokens de autenticação para manter as sessões ativas. Quando o usuário faz login, armazenamos o token retornado pela Supabase para autenticar futuras requisições. Além disso, oferecemos uma opção de recuperação de senha, na qual a própria Supabase envia o email de redefinição.

#### Randomuser

&emsp; A RandomUser API é utilizada na nossa aplicação para gerar dados fictícios de investidores que são exibidos na tela inicial. Esses investidores representam usuários inconformes com seu perfil de risco, criando um cenário realista para ilustrar a funcionalidade do app. Através dessa API, conseguimos obter informações como nome, email e diversas outras, sem precisar manter um banco de dados próprio para esses perfis fictícios. Isso facilita tanto o desenvolvimento quanto os testes, garantindo dados variados e personalizáveis.

# 6. Mapeamento Técnico de Infraestrutura e Implantação

_conteúdo_

## 6.1 Diagrama de Implantação da UML

_conteúdo_

## 6.2 Justificativa das Escolhas de Implantação

_conteúdo_

## 6.3 Considerações sobre Desempenho e Segurança

_conteúdo_

## 6.4 Estimativas de Custo do Projeto

&emsp;Essa seção dedica-se à construção de uma estimativa de custos do projeto, considerando três fatores de custo principais: pessoas, software e infraestrutura, em um período de um ano - entre desenvolvimento e manutenção. A estimativa busca de aproximar da estrutura de custos para implementação do projeto, utilizando-se de valores reais de mercado para o orçamento e refletindo restrições impostas pelo contexto de desenvolvimento do projeto.<br/>
&emsp;De forma complementar, é válido ressaltar que <a href="https://aws.amazon.com/pt/">Amazon Web Services (AWS)</a> foi a escolha de provedor de serviços de infraestrutura e o software <a href="https://www.figma.com/pt-br/">Figma</a> foi selecionado como ferramenta de prototipação e design por questões de familiaridade técnica com o time de desenvolvimento, considerando ainda que o parceiro de projeto não elucidou restrições na escolha desses serviços.<br/>
&emsp;Além disso, os preços utilizados na estimativa refletem o valor corrente dos serviços no dia 30/04/2025 e considerando uma cotação de câmbio média de 1.00 USD (dólar americano) para 5.50 BRL (reais brasileiros).<br/>
&emsp;A seguir, é possível observar os orçamentos preliminares para desenvolvimento e implantação do projeto: 

### 6.4.1 Investimento em Pessoas
<div align="center">

<sup>**Tabela X - Estimativa de custos de Pessoas**<sup><br/>

| Categoria                     | Descrição                                              | Unidade               | Qtde                   | Custo Unitário       | Custo Total (R$) |
|-------------------------------|--------------------------------------------------------|-----------------------|------------------------|----------------------|------------------|
| Estagiários **(período de desenvolvimento)** | 7 estagiários em React Native + ASP .NET Core + ML.NET                  | estagiário · mês      | 7 × 2,5 meses          | R\$ 3.500            | R\$ 61.250       |
| Estagiários **(período de manutenção)**      | 2 estagiários para bugs, monitoramento e suporte leve   | estagiário · mês      | 2 × 9,5 meses          | R\$ 3.500            | R\$ 66.500       |
| **Subtotal**                  |                                                        |                       |                        |                      | **R\$ 127.750**  |
| Reserva Contingência (15%)    | Reserva financeira adicional para turnover (despesas decorrentes de rotação de pessoas, como demissões/substituições), horas extras e imprevistos financeiros       | pagamento único              | –                      | 15% sobre subtotal   | R\$ 19.162      |
| **Total Estimado (Pessoas)**  |                                                        |                       |                        |                      | **R\$ 146.912**  |

<sub>Fonte: Material produzido pelos autores(2025).<sub>
</div>

### 6.4.2 Investimento em Software

<div align="center">

<sup>**Tabela X - Estimativa de custos de Software**<sup><br/>

| Categoria               | Descrição                                                      | Unidade            | Qtde                | Custo Unitário      | Custo Total (R$) |
|-------------------------|----------------------------------------------------------------|--------------------|---------------------|---------------------|------------------|
| Figma                   | 4 licenças Professional  Dev                                        | licença · ano      | 4 × 1 ano        | R\$ 813              | R\$ 3.254        |
**Subtotal**            |                                                                |                    |                     |                     | **R\$ 3.254**   |
| Reserva Contingência (10%) | Reserva financeira adicional para custos extras, upgrades surpresa                | pagamento único           | –                   | 10% sobre subtotal  | R\$ 325        |
| **Total Estimado (Software)** |                                                       |                    |                     |                     | **R\$ 3579**   |

<sub>Fonte: Material produzido pelos autores(2025).<sub>
</div>

### 6.4.3 Investimento em Infraestrutura

<div align="center">

<sup>**Tabela X - Estimativa de custos de Infraestrutura**<sup><br/>

| Categoria                       | Descrição                                            | Unidade              | Qtde              | Custo Unitário    | Custo Total (R$) |
|---------------------------------|------------------------------------------------------|----------------------|-------------------|-------------------|------------------|
| EC2 (API + Modelo de ML)                       | t3.medium para ASP.NET Core + ML.NET inferência      | instância · mês      | 1 × 12            | US\$ 35 ≈ R\$ 192 | R\$ 2.310        |
| RDS (Banco)                     | db.t3.small (MySQL)                                  | instância · mês      | 1 × 12            | US\$ 40 ≈ R\$ 220 | R\$ 2.640        |
| EBS (Storage)                   | 100 GB de SSD                                        | volume · mês         | 1 × 12            | US\$ 10 ≈ R\$ 55  | R\$ 660          |
| S3 + Logs                       | Artefatos, APK e logs                                | volume · mês        | US\$ 50 × 12      | ≈ R\$ 275         | R\$ 3.300        |
| ECR (Docker Registry)           | 100 GB de imagens                                    | serviço · mês        | US\$ 10 × 12      | ≈ R\$ 55          | R\$ 660          |
| Ambiente de Staging (50%)       | 50% dos recursos de produção para testes/desenvolvimento             | replicação mensal    | –                 | ≈ R\$ 643/mês     | R\$ 7.734        |
| AWS Backup                      | Backup automatizado de RDS e EFS                     | serviço · mês        | US\$ 50 × 12      | ≈ R\$ 275         | R\$ 3.300        |
| CloudFront (CDN)                | Distribuição de APK e assets de forma global                         | serviço · mês        | US\$ 20 × 12      | ≈ R\$ 110         | R\$ 1.320        |
| AWS Support (Business)          | Suporte 24×7                                         | plano · mês          | US\$ 100 × 12     | ≈ R\$ 550         | R\$ 6.600        |
| **Subtotal**        |                                                      |                      |                   |                   | **R\$ 28.524**   |
| Reserva Contingência (10%)      | Reserva financeira adicional para custos extras e picos de uso             | pagamento único             | –                 | 10% sobre subtotal  | R\$ 2.852        |
| **Total Estimado (Infra)**      |                                                      |                      |                   |                   | **R\$ 31.376**  |

<sub>Fonte: Material produzido pelos autores(2025).<sub>
</div>

### 6.4.4 Investimento Consolidado

<div align="center">

<sup>**Tabela X - Estimativa de custos do projeto consolidado**</sup><br/>

| Categoria | Total Estimado |
|---|---|
|Pessoas|R\$ 146.912|
|Software|R\$ 3.579|
|Infraestrutura|R\$ 31.376|
|**Total estimado (consolidado)**|**R\$ 181.867**|

<sub>Fonte: Material produzido pelos autores(2025).<sub>
</div>

&emsp; Desse modo, apresenta-se uma estimativa de custos consolidada para o projeto, com a especificação dos diferentes fatores de custo, a fim de esclarecer o investimento relacionado ao desenvolvimento e à manutenção do projeto - durante o período de 1 (um) ano.

# 7. Projeto Visual da Solução

&emsp; Nesta seção, mostramos as partes visuais que criamos para o app, como os wireframes, mockups e o guia visual. A ideia é dar uma visão geral de como o sistema foi pensado visualmente e como o usuário vai interagir com ele.

&emsp; Antes de detalhar cada uma das telas, incluímos um fluxo simples para ajudar a entender o caminho que o usuário percorre dentro do app. Em seguida, explicamos como foram feitos os wireframes, os mockups e como organizamos os elementos visuais de forma padronizada.

<div align="center">
  <sub>Figura X - Fluxo do Usuário </sub> <br>

  <img src="./assets/7_projeto_visual_da_solucao/user_flow.png" alt=" Fluxo do Usuário" style="max-width: 700px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

## 7.1 Desenvolvimento de Wireframes

Nessa seção consta os wireframes da solução.

<div align="center">
  <sub>Figura X - Wireframe tela de login</sub> 

  <img src="./assets/7_projeto_visual_da_solucao/wireframe/login.png" alt="Wireframe tela de login" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

- **Escolhas de Design:** O wireframe apresenta uma tela de login com layout centralizado e seguindo os conceitos de mobile first. A logo no topo reforça a identidade visual, seguida do título “Login” para orientar o usuário, com campos de e-mail e senha bem destacados e um botão de login abaixo para facilitar o fluxo. A linha inferior é uma área destinada a "esqueci minha senha".


- **Descrição da Tela:** A tela representa uma interface de login simples e objetiva, com elementos centralizados que facilitam a navegação. Ela inclui espaço para a logo, campos de entrada para e-mail e senha, um botão de acesso e uma área inferior reservada para um "esqueci a senha"

<div align="center">
  <sub>Figura X - Wireframe tela inicial</sub> 

  <img src="./assets/7_projeto_visual_da_solucao/wireframe/home.png" alt="Wireframe tela inicial" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

- **Escolhas de Design:** A tela inicial foi desenhada com uma estrutura mobile first que começa com ícones funcionais (busca, logo e filtro), seguida de um painel visual com gráfico e texto explicativo, para oferecer uma visão geral dos dados dos ativos. Abaixo, uma lista de cartões apresenta informações sobre os usuários com as maiores incnformidades de perfil, que são simbolizadas pelo X.

- **Descrição da Tela:**  No topo, há ícones de busca, logo e filtro, seguidos por um gráfico circular com legenda, que resume dados principais das conformidades.Abaixo, cartões listam informações detalhadas com nome do cliente, uma descrição e se está conforme com seu risco.

<div align="center">
  <sub>Figura X - Wireframe tela recomendação</sub> 
  <img src="./assets/7_projeto_visual_da_solucao/wireframe/recommendation.png" alt="Wireframe tela recomendação" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

- **Escolhas de Design:** A tela foi organizada com uma barra superior funcional contendo botões de voltar, filtro e logo centralizada para navegação. O painel principal exibe o resumo do perfil, seguido por recomendações de fundos apresentadas em cartões horizontais com informações organizadas. Contendo um botão slider acima do texto para alterar a visualização de recomendações para carteira real.

- **Descrição da Tela:**  A tela exibe recomendações de investimentos, com foco em ajudar o usuário a retornar ao seu perfil de risco ideal. Possui barra superior com navegação (voltar, logo e filtro), um resumo do perfil atual com alguns dados. Em seguida, são listadas sugestões de fundos, cada uma apresentada em cartões com nome tipo e etc. E por fim, acima do texto um slider que alterna entre visualização das recomendações e da carteira real.

<div align="center">
  <sub>Figura X - Wireframe tela de carteira</sub> 

  <img src="./assets/7_projeto_visual_da_solucao/wireframe/wallet.png" alt="Wireframe tela de carteira" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

- **Escolhas de Design:** A tela foi organizada com uma barra superior funcional contendo botões de voltar, filtro e logo centralizada para navegação. O painel principal exibe o resumo do perfil, seguido pelos fundos da carteira real do cliente, que são apresentados em cartões horizontais com informações organizadas. Contendo um botão slider acima do texto para alterar a visualização de carteira para recomendações de investimento.

- **Descrição da Tela:**  A tela exibe cada fundo da carteira do cliente, mostrando quais estão inconformes e conformes. Possui barra superior com navegação (voltar, logo e filtro), um resumo do perfil atual com alguns dados. Em seguida, são listados os investimentos atuais do cliente suas conformidades com o perfil, e uma breve descrição. E por fim, acima do texto um slider que alterna entre visualização da carteira real e das recomendações.

## 7.2 Desenvolvimento de Mockups

Nessa seção consta os mockups  (protótipos de alta fidelidade) da solução.

<div align="center">
  <sub>Figura X - Mockup da tela de login </sub> <br>

  <img src="./assets/7_projeto_visual_da_solucao/mockup/login.png" alt="Mockup da tela de login" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

- **Escolhas de Design:** Elementos foram centralizados para facilitar o uso e guiar o olhar do usuário.
O logotipo estilizado reforça a identidade visual com formas e cores marcantes.
Os textos deixam tudo mais simples e intuitivo.

- **Descrição da Tela:** Tela de login simples e direta, com o logotipo no topo.
Abaixo, há campos para e-mail e senha com design limpo e bordas arredondadas.
Um link para recuperar senha e um botão de login rosa.

<div align="center">
  <sub>Figura X - Mockup da tela inicial </sub> <br>

  <img src="./assets/7_projeto_visual_da_solucao/mockup/home.png" alt="Mockup da tela inicial" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

- **Escolhas de Design:** O topo possui barra com ícones de busca e filtro, reforçando a funcionalidade.
Um gráfico de pizza destaca visualmente o número de clientes não conformes.
Cartões individuais usam cores para indicar status de conformidade dos clientes e exibir certas informações.

- **Descrição da Tela:** A tela apresenta um resumo visual com um gráfico e a contagem de clientes não conformes.
Abaixo, há uma lista de clientes com nome, perfil, investimento e último contato.
Cada cartão é rotulado como "Conforme" ou "Inconforme", com destaque por cor.

<div align="center">
  <sub>Figura X - Mockup da tela perfil</sub> <br>

  <img src="./assets/7_projeto_visual_da_solucao/mockup/profile.png" alt="Mockup da tela perfil" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>


- **Escolhas de Design:** O topo mantém identidade visual com ícones de navegação e filtro.
A seção do cliente destaca informações-chave com contraste forte e rótulo de status.
A navegação por abas ("Recomendações" e "Carteira") serve para organizar o contéudo.

- **Descrição da Tela:** Tela detalha o perfil de um cliente com status "Inconforme" em destaque.
Abaixo, são exibidas recomendações de fundos para ajuste do perfil de risco.
Cada fundo aparece em um card com informações como gestora e classe.

<div align="center">
  <sub>Figura X - Mockup da tela carteira </sub> <br>

  <img src="./assets/7_projeto_visual_da_solucao/mockup/wallet.png" alt="Mockup da tela carteira" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

- **Escolhas de Design:** A interface mantém o cabeçalho fixo com a identidade visual e ícones de navegação.
As abas "Recomendações" e "Carteira" organizam a navegação de forma clara e intuitiva.
Os investimentos são exibidos em cartões com rótulos coloridos de conformidade para fácil leitura.

- **Descrição da Tela:** A tela exibe os investimentos atuais do cliente, com dados como valor, quantidade e classe.
Cada fundo é apresentado em cartões separados com indicação de status "Conforme" ou "Inconforme".
A visualização facilita o entendimento da carteira e possíveis ajustes a serem feitos.

## 7.3 Guia Visual

Nessa seção consta o guia visual da nossa solução.

<div align="center">
  <sub>Figura X - Guia de Estilo </sub> <br>

  <img src="./assets/7_projeto_visual_da_solucao/style_guide.png" alt="Guia de Estilo" style="max-width: 750px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

# Guia de Estilo – Paleta de Cores da Marca **PATI**

## Principais

| Cor      | Código   | Função sugerida                                               |
|----------|----------|----------------------------------------------------------------|
| Rosa PATI | `#C81363` | Cor institucional principal                                   |
| Rosa Claro | `#EE3480` | Destaques e botões                                           |
| Vinho Escuro | `#4D0E1F` | Tipografia ou fundo escuro (inspirada na fonte da logo)     |
| Verde Água | `#24A49D` | Detalhes/ícones (inspirada no losango verde da logo)         |

---

## Secundárias

| Cor        | Código   | Função sugerida                                              |
|------------|----------|---------------------------------------------------------------|
| Rosa Suave | `#FF8BA7` | Hover, gráficos, ilustrações suaves                          |
| Vinho Médio| `#7B1E51` | Fundos alternativos ou contornos                             |
| Verde Claro| `#00B2A1` | Destaque complementar (variação da cor do losango)            |

---

## Neutras

| Cor          | Código   | Função sugerida                                            |
|--------------|----------|-------------------------------------------------------------|
| Preto        | `#000000` | Texto padrão ou contrastes                                 |
| Cinza Escuro | `#505A66` | Texto secundário ou bordas                                 |
| Bege Claro   | `#FCF7ED` | Fundo claro (ótimo para contraste com o rosa)              |
| Branco       | `#FFFFFF` | Fundo neutro, textos invertidos                            |

---

## Recomendações de Uso

- Utilize `#C81363` como a cor institucional dominante em elementos como **menus e cabeçalhos**.
- Aplique `#EE3480` para **chamadas à ação**, como **botões e links**.
- Reserve `#4D0E1F` e `#505A66` para **textos**, garantindo **acessibilidade e contraste**.
- Use os tons de **verde e azul suaves** (`#24A49D`, `#00B2A1`) como **cores funcionais ou decorativas**.

---

 **Dica:** Mantenha um bom contraste entre fundo e texto para garantir legibilidade, especialmente ao usar tons escuros ou muito vibrantes.

**Curiosidade:** O uso da cor rosa foi para justamente ser um projeto "whitelabel", que não se alinhasse com a identidade visual de nenhum banco.

# 8. Desenvolvimento do Projeto

# 8.1 Arquitetura de Codificação e Estrutura de Diretórios

## 8.1.1 Backend

O backend da aplicação **Pati** é desenvolvido em **C# com ASP.NET Core**, seguindo uma arquitetura orientada a serviços (SOA), conforme visualizado no diagrama de arquitetura da solução. Cada serviço possui uma responsabilidade de negócio específica, promovendo modularidade, escalabilidade e manutenibilidade. A interface gráfica (`MobileApp`) interage diretamente com os serviços expostos, primariamente com o `api_data_presentation`. Internamente, cada serviço backend adota os princípios da **Clean Architecture** para uma separação clara de responsabilidades.

Os serviços seguem a Clean Architecture, aplicando os princípios SOLID: **SRP (Princípio da Responsabilidade Única)** - cada classe possui uma única responsabilidade; **OCP (Princípio Aberto/Fechado)** - entidades são extensíveis sem modificação; **ISP (Princípio da Segregação de Interfaces)** - interfaces específicas para cada necessidade; **LSP (Princípio da Substituição de Liskov)** - classes derivadas podem substituir suas classes base; e **DIP (Princípio da Inversão de Dependência)** - dependência de abstrações, não de implementações concretas.

## Justificativa da Estrutura Adotada

A organização dos diretórios com uma pasta `apps/` dentro de `src/` foi escolhida para:
- **Agrupamento de Componentes Implantáveis:** Centralizar todas as aplicações e serviços que são unidades de implantação (frontend e microsserviços backend) em um local dedicado (`src/apps/`).
- **Clareza e Separação:** Distinguir claramente os componentes executáveis/implantáveis de outras pastas de suporte e bibliotecas (como `database/`, `dataset/`, `models/`) que residem como irmãos de `apps/` dentro de `src/`.
- **Modularidade e Escalabilidade:** Manter cada serviço (ex: `api_data_presentation`) e a aplicação móvel (`MobileApp`) como unidades coesas e independentes dentro de `apps/`, facilitando o desenvolvimento, o versionamento, os testes e a implantação individual de cada componente.

## Camadas Lógicas Internas de Cada Serviço Backend

Cada microsserviço backend (ex: `api_data_presentation`, `api_classification`, `api_ml_recommendation`) é estruturado internamente seguindo uma arquitetura em camadas, inspirada na Clean Architecture. Esta abordagem visa promover a separação de responsabilidades, testabilidade e manutenibilidade do código. Abaixo, descrevemos cada camada com exemplos ilustrativos:

-   **Presentation Layer (Camada de Apresentação):** Interface do serviço com o mundo externo. Para os serviços backend, são os `Controllers` que expõem endpoints da API RESTful. Responsável por receber requisições HTTP, validar dados de entrada (superficialmente), serializar/desserializar DTOs e formatar respostas.
    ```csharp
    // Exemplo (api_data_presentation/Controllers/UserController.cs):
    [ApiController]
    [Route("api/v1/users")]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService; // Injeção da interface da camada de Aplicação

        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpGet("{userId}/profile")] // Rota para obter perfil do usuário
        public async Task<IActionResult> GetUserProfile(string userId)
        {
            // Validação básica do input (ex: userId não nulo) pode ocorrer aqui ou em middleware
            var userProfileDto = await _userService.GetUserProfileAsync(userId);
            if (userProfileDto == null)
            {
                return NotFound(); // Resposta HTTP padrão
            }
            return Ok(userProfileDto); // Retorna o DTO com status 200 OK
        }
        // Outros endpoints (ex: POST para criar usuário, PUT para atualizar)
    }
    ```

-   **Application Layer (Camada de Aplicação):** Contém a lógica de aplicação e os casos de uso específicos do serviço (também conhecidos como "interactors" ou "services"). Orquestra as interações entre o domínio e a infraestrutura para realizar as operações. Não contém lógica de negócio do domínio, mas coordena sua execução e lida com questões transversais como logging em nível de caso de uso e mapeamento entre DTOs e entidades de domínio.
    ```csharp
    // Exemplo (api_data_presentation/Application/UserService.cs):
    public class UserService : IUserService // Implementa a interface definida na própria camada de Aplicação
    {
        private readonly IUserRepository _userRepository; // Injeção da interface do repositório (abstração da infra)
        private readonly IMapper _mapper; // Ex: AutoMapper para mapear entre Entidade e DTO

        public UserService(IUserRepository userRepository, IMapper mapper)
        {
            _userRepository = userRepository;
            _mapper = mapper;
        }

        public async Task<UserProfileDto> GetUserProfileAsync(string userId)
        {
            var user = await _userRepository.FindByIdAsync(userId); // Usa a abstração do repositório
            if (user == null)
            {
                return null; // Lógica de tratamento se usuário não encontrado
            }
            // Lógica de orquestração, manipulação de DTOs
            return _mapper.Map<UserProfileDto>(user); // Mapeia a entidade de domínio para um DTO
        }
        // Outros casos de uso (ex: CreateUserAsync, UpdateUserPreferencesAsync)
    }
    ```

-   **Domain Layer (Camada de Domínio):** O coração do serviço. Contém as entidades de negócio (objetos com identidade e ciclo de vida), agregados (conjuntos de entidades tratadas como uma unidade), value objects (objetos imutáveis que representam um valor descritivo), regras de domínio e eventos de domínio. É totalmente independente de frameworks e tecnologias de infraestrutura (POCOs - Plain Old CLR Objects).
    ```csharp
    // Exemplo (api_data_presentation/Domain/Entities/User.cs):
    public class User // Entidade Raiz de um Agregado, pode herdar de uma BaseEntity com Id
    {
        public string UserId { get; private set; } // Identificador único
        public string Name { get; private set; }
        public Email Email { get; private set; } // Exemplo de Value Object
        // Outras propriedades e coleções de entidades filhas ou Value Objects

        private User(string userId, string name, Email email)
        {
            // Validações de domínio (invariantes do agregado) devem estar aqui
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("User name cannot be empty.");
            UserId = userId;
            Name = name;
            Email = email;
        }

        public static User Create(string userId, string name, string emailAddress)
        {
            var email = Email.Create(emailAddress); // Criação do Value Object
            return new User(userId, name, email); // Factory Method para encapsular criação
        }

        public void UpdateName(string newName)
        {
            if (string.IsNullOrWhiteSpace(newName))
                throw new DomainException("New name cannot be empty.");
            Name = newName;
            // Pode disparar um evento de domínio aqui (ex: UserProfileUpdatedEvent)
        }
        // Outras regras e lógicas de negócio encapsuladas na entidade
    }

    // Exemplo de Value Object (api_data_presentation/Domain/ValueObjects/Email.cs)
    public class Email : ValueObject // Herda de uma classe base para Value Objects
    {
        public string Address { get; private set; }
        private Email(string address) { Address = address; }

        public static Email Create(string emailAddress)
        {
            if (string.IsNullOrWhiteSpace(emailAddress) || !emailAddress.Contains("@")) // Validação simples
                throw new DomainException("Invalid email address.");
            return new Email(emailAddress);
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Address;
        }
    }
    ```

-   **Infrastructure Layer (Camada de Infraestrutura):** Implementa os detalhes técnicos para persistência de dados (repositórios concretos), comunicação com outros serviços (clients HTTP), acesso a sistemas de arquivos, interação com filas de mensagens, bibliotecas de terceiros, etc. Depende do Domínio (para conhecer as entidades) e da Aplicação (para implementar as interfaces de abstração, como `IRepository`).
    ```csharp
    // Exemplo (api_data_presentation/Infrastructure/Repositories/UserRepository.cs):
    public class UserRepository : IUserRepository // IUserRepository definido na Aplicação ou Domínio
    {
        private readonly PatiDbContext _dbContext; // Ex: Entity Framework Core DbContext

        public UserRepository(PatiDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<User> FindByIdAsync(string userId)
        {
            // Lógica de acesso ao banco de dados usando o ORM (ex: Entity Framework Core)
            // O objeto retornado aqui é a entidade de domínio User
            return await _dbContext.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        }

        public async Task AddAsync(User user)
        {
            await _dbContext.Users.AddAsync(user);
            // _dbContext.SaveChangesAsync() geralmente é chamado por uma unidade de trabalho (Unit of Work)
            // na camada de Aplicação ou em um middleware.
        }
        // Outras implementações de persistência (UpdateAsync, DeleteAsync, etc.)
    }
    ```

    #### Abstrações de Infraestrutura
    Interfaces (ex: `IUserRepository`, `IEmailService`) definidas pela camada de Aplicação ou, em alguns casos, pelo Domínio. Estas interfaces são implementadas pela camada de Infraestrutura, permitindo a inversão de dependência (Dependency Inversion Principle).

Abaixo, um diagrama ilustra a disposição dessas camadas e suas dependências dentro de um microsserviço:

```mermaid
graph LR
    subgraph "Microsserviço (ex: api_data_presentation)"
        direction TB
        P["Presentation Layer <br/> (Controllers, DTOs de Entrada/Saída)"] --> A["Application Layer <br/> (Casos de Uso, Services, DTOs Internos, Interfaces de Repositórios)"]
        A --> D["Domain Layer <br/> (Entidades, Agregados, Value Objects, Regras de Domínio, Eventos de Domínio)"]
        A --> I_IF["Abstrações de Infraestrutura <br/> (ex: IUserRepository, IEmailService)"]
        I["Infrastructure Layer <br/> (Repositórios Concretos, DBContext, Clients HTTP, Implementações de Serviços Externos)"] -.-> D
        I -- Implementa --> I_IF
    end

    I --> E["Sistemas Externos <br/> (Banco de Dados, Outros APIs, Filas)"]

    classDef layer fill:#f2f2f2,stroke:#333,stroke-width:2px,color:#333;
    class P,A,D,I,I_IF layer;
    classDef external fill:#e6f3ff,stroke:#333,stroke-width:2px,color:#333;
    class E external;
```
**Legenda para o Diagrama de Camadas Internas do Microsserviço:**
-   **Presentation Layer:** Responsável pela interface com o mundo exterior (ex: Controllers da API que lidam com requisições HTTP e DTOs de entrada/saída).
-   **Application Layer:** Orquestra os casos de uso e a lógica da aplicação (ex: Services que implementam as operações de negócio). Define as interfaces para as dependências externas (como repositórios).
-   **Domain Layer:** Contém a lógica de negócio central, as entidades e os value objects do domínio. É o núcleo da aplicação e não depende de outras camadas.
-   **Infrastructure Layer:** Lida com detalhes técnicos como acesso a banco de dados (implementando os repositórios), comunicação com serviços externos, etc. Implementa as interfaces definidas pela camada de Aplicação.
-   **Sistemas Externos:** Banco de dados, outros microsserviços, APIs de terceiros, filas de mensagens, etc., com os quais a camada de Infraestrutura interage.

As setas no diagrama indicam a **direção das dependências**. A regra principal da Clean Architecture é que as dependências apontam para dentro: a camada de Domínio não depende de nenhuma outra camada; a camada de Aplicação depende do Domínio; e as camadas de Apresentação e Infraestrutura dependem da Aplicação e, indiretamente, do Domínio (geralmente através das abstrações definidas na Aplicação).

---

## Estrutura de Diretórios Detalhada

A estrutura de diretórios do projeto é organizada da seguinte forma:

```plaintext
src/
├── apps/                                # Contém todas as aplicações e serviços implantáveis
│   ├── MobileApp/                       # Aplicação Frontend React Native
│   │   ├── components/                  # Componentes reutilizáveis da UI
│   │   ├── screens/                     # Telas da aplicação
│   │   ├── navigation/                  # Lógica de navegação
│   │   ├── services/                    # Camada de serviço do frontend (chamadas API, incluindo randomuser.me)
│   │   ├── store/                       # Gerenciamento de estado (ex: Redux, Zustand)
│   │   ├── assets/                      # Imagens, fontes, etc.
│   │   ├── tests/                       # Testes para o MobileApp (unitários, de componentes, e2e)
│   │   ├── package.json                 # Dependências e scripts do frontend
│   │   └── (outros arquivos e pastas do React Native: android/, ios/, metro.config.js, etc.)
│   │
│   ├── api_data_presentation/           # Serviço de Apresentação de Dados (Backend C#)
│   │   ├── Controllers/                 # Endpoints da API (Presentation Layer)
│   │   ├── Application/                 # Lógica de aplicação, Casos de Uso, Interfaces de Repositório (Application Layer)
│   │   ├── Domain/                      # Entidades, Value Objects, Lógica de Domínio, Eventos (Domain Layer)
│   │   ├── Infrastructure/              # Repositórios concretos, acesso a DB, Clients HTTP (Infrastructure Layer)
│   │   ├── DTOs/                        # Objetos de Transferência de Dados (usados pela Presentation e Application)
│   │   ├── Configs/                     # Arquivos de configuração (ex: appsettings.json, secrets.json)
│   │   ├── tests/                       # Testes (unitários, integração) para api_data_presentation
│   │   ├── api_data_presentation.csproj # Arquivo de projeto C#
│   │   └── Program.cs                   # Configuração e inicialização do serviço (Startup.cs em versões legadas)
│   │
│   ├── api_classification/     # Serviço de Classificação (Backend C#) - Estrutura similar ao api_classification
│   │   ├── Controllers/                 # (Presentation Layer)
│   │   ├── Application/                 # (Application Layer)
│   │   ├── Domain/                      # (Domain Layer)
│   │   ├── Infrastructure/              # (Infrastructure Layer)
│   │   ├── DTOs/
│   │   ├── Configs/                     # Arquivos de configuração
│   │   ├── tests/                       # Testes para api_classification
│   │   ├── pati_classification_service.csproj
│   │   └── Program.cs
│   │
│   ├── api_ml_recommendation/           # Serviço de Recomendação ML (Backend C#) - Estrutura similar ao api_data_presentation
│   │   ├── Controllers/                 # (Presentation Layer)
│   │   ├── Application/                 # (Application Layer)
│   │   ├── Domain/                      # (Domain Layer)
│   │   ├── Infrastructure/              # (Infrastructure Layer)
│   │   ├── DTOs/
│   │   ├── Configs/                     # Arquivos de configuração
│   │   ├── tests/                       # Testes para api_ml_recommendation
│   │   ├── api_ml_recommendation.csproj
│   │   └── Program.cs
│
├── database/                            # Scripts SQL, configuração do banco de dados
│   ├── 01_create_database.sql           # Script de criação do banco de dados
│   └── 02_initial_data.sql              # Script de dados iniciais
│
├── dataset/                             # Arquivos de dados brutos, amostras para processamento/teste
│   ├── PosicaoIntelli.csv               # Dados brutos para classificação
│   └── inconformidades.xlsx             # Dados processados para classificação
│                                        # Os arquivos PosicaoIntelli.csv (dados brutos) e inconformidades.xlsx (dados processados) são usados para classificação.
│
├── models/                              # Artefatos de Machine Learning (ex: notebooks, modelos serializados)
│   ├── colaborative_filtering_user_based_memory_based.ipynb/              # Notebook com a filtragem colaborativa
│   └── SVD_CollaborativeFiltering_CVM175/                                 # Notebook com modelo final SVD
│
└── Pati.Backend.sln                     # Arquivo de Solução Visual Studio para todos os projetos C# do backend
                                         # (api_data_presentation, api_classification, api_ml_recommendation)
```

Este exemplo detalha subpastas típicas dentro de cada componente para ilustrar melhor a organização interna e as responsabilidades de cada camada.

---

## Descrição dos Serviços e Suas Interações

Conforme o diagrama de arquitetura da solução, os principais serviços e suas interações são:

### `api_data_presentation` (localizado em `src/apps/api_data_presentation/`)
-   **Responsabilidade Principal:** Gerencia informações de assessores e clientes, expondo endpoints RESTful para a MobileApp. É o principal ponto de contato para a interface gráfica, consolidando e apresentando dados relacionados a assessores de investimentos e seus clientes.
-   **Interações Chave:**
    -   Recebe requisições da `MobileApp` via API REST.
    -   Interage com o `auth-service` (assumindo Supabase Auth) para autenticação do usuário.
    -   Consulta e armazena dados de usuário no banco de dados.

### `api_classification` (localizado em `src/apps/api_classification/`)
-   **Responsabilidade Principal:** Realiza a classificação de dados de carteiras com base em regras de negócio e políticas de conformidade. As regras de classificação estão implementadas na lógica do serviço, não no banco de dados. Identifica inconformidades na carteira do cliente.
-   **Interações Chave:**
    -   Recebe dados de carteiras via API interna (ex.: de outro serviço ou processo interno).
    -   Aplica regras de classificação implementadas na lógica do serviço.
    -   Envia o resultado da sua análise (`{dados de carteiras, inconformidades}`) para o `api_ml_recommendation` (via API interna).

### `api_ml_recommendation` (localizado em `src/apps/api_ml_recommendation/`)
-   **Responsabilidade Principal:** Aplica algoritmos, modelos de Machine Learning e heurísticas de negócio para gerar recomendações de investimento personalizadas, visando adequar a carteira do cliente ao seu perfil.
-   **Interações Chave:**
    -   Recebe os `{dados de carteiras, inconformidades}` do `pati_classification_service` (via API interna).
    -   Persiste recomendações no banco NoSQL para armazenamento das recomendações geradas.
    -   Aplica modelos de Machine Learning para gerar recomendações personalizadas.

---

## Diagrama de Arquitetura Visual

Este diagrama ilustra as interações entre os componentes principais do sistema, refletindo a estrutura de serviços e o fluxo de dados definido:

```mermaid
graph TD
    subgraph Frontend
        UI[MobileApp]
    end

    subgraph External_Services
        AUTH[Supabase Auth]
        RANDOM_USER_API[randomuser.me API]
    end

    subgraph Backend_PATI_Services [Backend PATI]
        direction LR

        subgraph Database_Layer [Camada de Dados]
            direction TB
            DB_SQL[DB App SQL Banco de Dados]
            DB_NoSQL[DB NoSQL Recomendações]
        end

        subgraph Application_Microservices [Microsserviços da Aplicação]
            direction TB
            DATA_SVC[api_data_presentation]
            CLASS_SVC[api_classification]
            REC_SVC[api_ml_recommendation]
        end

        UI -- Requisições API --> DATA_SVC

        CLASS_SVC -- "2- Envia '{dados de carteiras, inconformidades}'" --> REC_SVC
        REC_SVC -- "Persiste recomendações" --> DB_NoSQL
        DATA_SVC -- "Consulta/Armazena dados de usuário" --> DB_SQL

    end

    UI -- Autenticação --> AUTH
    AUTH -- Validação/Token --> DATA_SVC
    UI -- "Usa para popular UI com dados mock (nomes, e-mails, etc.)" --> RANDOM_USER_API


    %% Estilização para clareza
    style UI fill:#DAE8FC,stroke:#6C8EBF,stroke-width:2px
    style AUTH fill:#E1E1E1,stroke:#999,stroke-width:2px
    style DATA_SVC fill:#D5E8D4,stroke:#82B366,stroke-width:2px
    style CLASS_SVC fill:#FFE6CC,stroke:#D79B00,stroke-width:2px
    style REC_SVC fill:#F8CECC,stroke:#B85450,stroke-width:2px
    style DB_SQL fill:#FFF2CC,stroke:#D6B656,stroke-width:2px
    style DB_NoSQL fill:#E1D5E7,stroke:#9673A6,stroke-width:2px
```

---

## Documentação da API (Swagger/OpenAPI)

-   O `api_data_presentation` é o principal serviço que expõe endpoints para a `MobileApp`. Sua API deve ser documentada utilizando Swagger (OpenAPI) para detalhar os contratos (endpoints, schemas de requisição/resposta, métodos de autenticação).
    -   **Exemplos de Endpoints Públicos (expostos pelo `api_data_presentation`):**
        -   `GET /api/v1/user/profile`: Retorna o perfil do usuário autenticado.
        -   `GET /api/advisor/{advisorId}/compliance-stats`: Retorna estatísticas de compliance dos clientes.
        -   `GET /api/advisor/clients/inconform`: Lista clientes com inconformidades.

-   Os serviços `api_classification` e `api_ml_recommendation` também devem ter sua própria documentação Swagger. A documentação Swagger do `api_classification` está disponível em http://localhost:8080/swagger. Nem todas as documentações de API estão completas nesta etapa do projeto. Embora seus endpoints sejam destinados principalmente à comunicação interna entre os serviços do backend, essa documentação é crucial para:
    -   Facilitar o desenvolvimento e a integração entre as equipes responsáveis por cada serviço.
    -   Permitir testes de contrato e testes de integração automatizados.
    -   Servir como referência clara dos dados esperados e produzidos por cada serviço.
    -   **Exemplos de Endpoints Internos (não expostos diretamente à `MobileApp`):**
        -   `api_classification`: `POST /internal/v1/portfolios/classify`
        -   `api_ml_recommendation`: `POST /internal/v1/recommendations/generate`

A comunicação interna entre os serviços backend (ex: `api_data_presentation` chamando `api_classification`) pode ser realizada através de chamadas HTTP diretas às suas APIs internas.

## 8.1.2 Frontend

Este projeto utiliza uma estrutura de frontend baseada em React Native, com foco em modularização, manutenibilidade e escalabilidade.

#### Estrutura de Diretórios:

```plaintext
frontend/
└── pati/
    ├── .expo/
    ├── assets/
    ├── node_modules/
    ├── src/
    │   ├── components/
    │   │   └── ... (Componentes reutilizáveis)
    │   │
    │   ├── config/
    │   │   └── ... (Configurações globais como rotas, temas, constantes)
    │   │
    │   ├── helpers/
    │   │   └── ... (Funções utilitárias e auxiliares)
    │   │
    │   ├── hooks/
    │   │   └── ... (Hooks customizados)
    │   │
    │   ├── screens/
    │   │   └── ... (Telas da aplicação)
    │   │
    │   ├── services/
    │   │   └── ... (Comunicação com backend - APIs)
    │   │
    │   └── tests/
    │       └── ... (Testes unitários e de integração)
    │
    ├── app.json
    ├── App.jsx
    ├── index.jsx
    ├── package-lock.json
    └── package.json
```
### Justificativa da Arquitetura (Frontend)

### components/
Componentes reutilizáveis como botões, inputs, modais.

 **Por que?** Promove reuso de código e facilita testes e manutenção.

 ### config/
Configurações globais como:
- Endpoints da API
- Temas
- Constantes gerais

**Por que?** Centraliza configurações, facilitando alterações.

### helpers/
Funções utilitárias e auxiliares, como:
- Formatação de datas
- Máscaras de dados
- Validações comuns como de e-mail

**Por que?** Evita duplicatas de funções de lógica.

### hooks/
Hooks customizados para abstrair lógicas complexas ou repetitivas, como: `useFetch`

**Por que?** Melhora a organização e o reaproveitamento de lógica entre componentes.

### screens/
Telas principais da aplicação, cada uma representando uma rota ou uma funcionalidade relevante.

**Por que?** Separa claramente a estrutura de navegação das partes do projeto.

### services/
Camada de comunicação com o backend:
- Configuração de cliente HTTP
- Funções para consumir endpoints

**Por que?** Centraliza a lógica de comunicação com APIs, promovendo uma separação de responsabilidades clara.

### tests/
Testes automatizados da aplicação:
- Testes de renderização
- Testes de integração

**Por que?** Promove qualidade de código, evita regressões e documenta o comportamento esperado dos componentes e funções.

## 8.2 Modelo de Recomendação

_conteúdo_

## 8.3 Desenvolvimento de Features

_conteúdo_

**Nota:** Insira uma explicação de entregas em cada Sprint.

## 8.3.1 Sprint 3

### API Data Presentation

&emsp; A **API Data Presentation** é um serviço desenvolvido em **ASP.NET Core 9** com o objetivo de consolidar, filtrar e apresentar dados relacionados a assessores de investimentos e seus clientes. Esse serviço atua como a camada de visualização e consulta do backend, intermediando o acesso a dois bancos de dados distintos (relacional e não-relacional).

#### Organização do Projeto

&emsp; A aplicação separa claramente as responsabilidades por camadas. A seguir estão as principais pastas e seus propósitos:

##### Estrutura de Diretórios

&emsp; A estrutura completa da solução está organizada conforme abaixo:

```text
├── src/apps/api_data_presentation
│   ├── api_data_presentation.csproj
│   ├── api-data-presentation.http
│   ├── appsettings.Development.json
│   ├── appsettings.json
│   ├── Config
│   │   └── DependencyInjection.cs
│   ├── Controllers
│   │   └── AdvisorController.cs
│   ├── Dockerfile
│   ├── Dockerfile.dev
│   ├── Factories
│   │   ├── DtoFactory.cs
│   │   └── IDtoFactory.cs
│   ├── Models
│   │   ├── Dtos
│   │   │   ├── AdvisorDto.cs
│   │   │   ├── ClientComplianceStatsDto.cs
│   │   │   ├── ClientInvestmentDto.cs
│   │   │   ├── InconformClientDto.cs
│   │   │   ├── InvestmentDto.cs
│   │   │   ├── InvestmentRecommendationResponseDto.cs
│   │   │   └── UpdateLastContactDto.cs
│   │   ├── Entities
│   │   │   ├── Advisor.cs
│   │   │   ├── Client.cs
│   │   │   └── Investment.cs
│   │   └── Mongo
│   │       ├── InvestmentRecommendation.cs
│   │       └── RiskClassification.cs
│   ├── Program.cs
│   ├── Properties
│   │   └── launchSettings.json
│   ├── Repositories
│   │   ├── Interfaces
│   │   │   ├── INonRelacionalRepository.cs
│   │   │   └── IRelacionalRepository.cs
│   │   ├── NonRelationalDB
│   │   │   └── AdvisorRepository.cs
│   │   └── RelacionalDB
│   │       └── AdvisorRepository.cs
│   ├── Services
│   │   └── AdvisorService.cs
│   ├── Sorting
│   │   ├── ISortingStrategy.cs
│   │   ├── SortingContext.cs
│   │   └── Strategies
│   │       ├── LastContactSortingStrategy.cs
│   │       ├── NonComplianceScoreSortingStrategy.cs
│   │       └── TotalInvestedSortingStrategy.cs
│   └── Utils
│       ├── IRandomUserService.cs
│       └── RandomClientService.cs
├── api_data_presentation.sln
└── api_data_presentation.Tests
    ├── AdvisorServiceTests.cs
    ├── api_data_presentation.Tests.csproj
    └── README.md
```
> A aplicação está localizada em `./src/apps/api_data_presentation/api_data_presentation`

&emsp; Para iniciar o projeto em modo de desenvolvimento, utilize o comando `dotnet run` ou, preferencialmente, `dotnet watch run`, que permite aproveitar o recurso de *hot reload* — acelerando o desenvolvimento ao evitar recompilações manuais a cada modificação no código.
Ao iniciar a aplicação dessa forma, ela estará disponível em:
**[http://localhost:5182/](http://localhost:5182/)**

&emsp; Também é possível rodar o projeto de forma conteinerizada com Docker. Para isso, navegue até o diretório `./src/apps/` e execute:

```bash
docker-compose -f docker-compose.dev.yml up
```

&emsp; Esse arquivo (`docker-compose.dev.yml`) foi posicionado em `.src/apps` para centralizar a orquestração de novos serviços e funcionalidades que venham a ser integrados ao repositório.
Ao subir o contêiner com esse comando, a aplicação da API **Data Presentation** será executada em modo de desenvolvimento, com suporte ao *hot reload*, oferecendo uma experiência semelhante à execução com `dotnet watch run`.
Nesse caso, a aplicação estará acessível em:
**[http://localhost:5000/](http://localhost:5000/)**

&emsp; Além disso, foi criado um projeto separado para testar a camada de negócios (`Services`), cujos detalhes de execução e configuração são abordados em `/src/apps/api_data_presentation/api_data_presentation.Tests/README.md`.
> **Atualização:** Este projeto de teste foi descontinuado dada a refatoração realizada da sprint 4, mas foi criado um novo projeto de teste mais sofisticado. Isto foi discorrido na seguinte seção: [8.3.2 Sprint 4](#832-sprint-4)

##### Controllers

&emsp; Responsáveis por receber as requisições HTTP, acionar os serviços e retornar as respostas adequadas. Exemplo:

* `AdvisorController.cs`: expõe os endpoints relacionados a assessores e seus clientes.

##### Services

&emsp; Contêm a lógica de negócio da aplicação, orquestrando a comunicação entre os repositórios e aplicando as regras necessárias antes de enviar os dados ao controller.

* `AdvisorService.cs`: concentra as operações envolvendo assessores, clientes, recomendações e conformidade.

##### Repositories

&emsp; Responsáveis pela comunicação direta com os bancos de dados, utilizando SQL puro para o banco relacional (PostgreSQL) e o driver oficial do MongoDB para acesso ao banco não relacional.

###### Repositórios Relacionais (PostgreSQL)

* Localizados em `Repositories/RelacionalDB`
* Interface: `IRelacionalRepository.cs`
* Implementação: `AdvisorRepository.cs`

###### Repositórios Não-Relacionais (MongoDB)

* Localizados em `Repositories/NonRelationalDB`
* Interface: `INonRelacionalRepository.cs`
* Implementação: `AdvisorRepository.cs`

##### Testes da Camada de Services

&emsp; A lógica de negócio implementada na camada de Services é testada através de testes unitários. Os testes garantem a qualidade e confiabilidade das operações envolvendo assessores, clientes, recomendações e conformidade.

&emsp; A documentação completa dos testes, incluindo cenários testados, estrutura dos testes e instruções de execução, está disponível em:

**Documentação de Testes:** `./src/apps/api_data_presentation/api_data_presentation.Tests/README.md`

&emsp; Esta documentação abrange:
* Estrutura e organização dos testes
* Cenários de teste implementados para cada método da camada de Services
* Configuração e execução dos testes
* Cobertura de código e métricas de qualidade

##### Models

&emsp; Contêm as estruturas de dados da aplicação:

* `Entities/`: representações das entidades do banco relacional
* `Mongo/`: representações dos documentos do banco MongoDB
* `Dtos/`: objetos de transferência de dados usados entre camadas

##### Factories

&emsp; Padronizam a construção dos objetos de transferência de dados (DTOs), centralizando a lógica de mapeamento.

* `IDtoFactory.cs`: interface
* `DtoFactory.cs`: implementação

##### Sorting

&emsp; Define estratégias de ordenação para listas de clientes, conforme diferentes critérios de negócio:

* `LastContactSortingStrategy.cs`
* `NonComplianceScoreSortingStrategy.cs`
* `TotalInvestedSortingStrategy.cs`
* O contexto de ordenação é gerenciado por `SortingContext.cs`, com base na escolha do usuário via query parameters.

##### Utils

&emsp; Contém serviços auxiliares como geração de dados falsos para testes:

* `IRandomUserService.cs`
* `RandomClientService.cs`

---

#### Endpoints

&emsp; A seguir estão os endpoints atualmente implementados no serviço, com detalhamento de propósito, parâmetros e formatos de entrada/saída.

##### 1. Autenticação de Assessor

**Endpoint:**
`POST /api/advisor/auth`

**Descrição:** Registra ou autentica um assessor com base em seu `uid` proveniente do Supabase Auth.

**Entrada:**

```json
{
  "uid": "string"
}
```

**Resposta:**

```json
{
  "advisorId": int,
  "uid": "string",
  "isNewAdvisor": boolean
}
```

---

##### 2. Estatísticas de Compliance

**Endpoint:**
`GET /api/advisor/{advisorId}/compliance-stats`

**Descrição:** Retorna a quantidade de clientes conforme e inconformes com seu perfil de risco, vinculados a um assessor específico.

**Resposta:**

```json
{
  "conforme": int,
  "inconforme": int
}
```

---

##### 3. Listagem de Clientes Inconformes

**Endpoint:**
`GET /api/advisor/clients/inconform`

**Descrição:** Lista clientes que possuem incompatibilidade entre o perfil de risco declarado e o perfil calculado com base em sua carteira.

**Parâmetros de Query:**

| Parâmetro | Tipo   | Obrigatório | Descrição                                                             |
| --------- | ------ | ----------- | --------------------------------------------------------------------- |
| advisorId | int    | Sim         | ID do assessor                                                        |
| sortBy    | string | Não         | Campo para ordenação (nonComplianceScore, lastContact, totalInvested) |
| sortOrder | string | Não         | `asc` ou `desc`                                                       |
| offset    | int    | Não         | Posição inicial para paginação                                        |
| limit     | int    | Não         | Número máximo de registros (padrão: 20, máx: 100)                     |

**Resposta:**

```json
[
  {
    "clientId": int,
    "name": "string",
    "declaredRiskProfile": "string",
    "complianceStatus": "string",
    "nonComplianceScore": double,
    "totalInvested": decimal,
    "lastContact": "datetime"
  }
]
```

---

##### 4. Recomendações de Investimento

**Endpoint:**
`GET /api/advisor/clients/{clientId}/recommendations`

**Descrição:** Retorna os ativos recomendados para readequação do portfólio de um cliente específico, incluindo dados de contato do cliente via API externa.

**Resposta:**

```json
{
  "clientId": int,
  "email": "string",
  "phone": "string",
  "portfolioRiskProfile": "string",
  "recommendedInvestments": [
    {
      "investmentId": int,
      "name": "string",
      "type": "string",
      "risk": "string",
      "price": decimal
    }
  ]
}
```

---

##### 5. Investimentos do Cliente

**Endpoint:**
`GET /api/advisor/client/{clientId}/investments`

**Descrição:** Retorna a carteira atual de investimentos de um cliente, com suporte a paginação e ordenação.

**Parâmetros de Query:**

| Parâmetro | Tipo   | Obrigatório | Descrição                                                          |
| --------- | ------ | ----------- | ------------------------------------------------------------------ |
| limit     | int    | Não         | Máximo de registros (máx: 100)                                     |
| offset    | int    | Não         | Deslocamento inicial da consulta                                   |
| sortBy    | string | Não         | Campo para ordenação (investment\_date, total\_invested, quantity) |
| sortOrder | string | Não         | `asc` ou `desc`                                                    |

**Resposta:**

```json
[
  {
    "investmentName": "string",
    "investmentType": "string",
    "investmentRisk": "string",
    "price": decimal,
    "quantity": int,
    "investmentDate": "datetime",
    "totalInvested": decimal
  }
]
```

---

##### 6. Atualização de Último Contato

**Endpoint:**
`POST /api/advisor/client/contact`

**Descrição:** Atualiza a data do último contato com um cliente. Essa funcionalidade será acionada pelo frontend quando o assessor registrar um novo contato.

**Entrada:**

```json
{
  "clientId": int,
  "contactDate": "datetime"
}
```

**Resposta:**

```json
{
  "message": "Contato atualizado com sucesso",
  "clientId": int,
  "lastContact": "datetime"
}
```

---

### Padrões de Projeto (Design Patterns) Implementados na Aplicação (API Data Presentation)

#### 1. Factory Method (Padrão Criacional)

##### Propósito
&emsp; O padrão Factory Method foi introduzido para centralizar e desacoplar a lógica de criação de DTOs (Data Transfer Objects) e objetos de resposta. Anteriormente, a instanciação desses objetos estava dispersa pela aplicação, o que dificultava a manutenção e a introdução de modificações. Com o Factory Method, a criação é encapsulada, promovendo um design mais limpo e flexível.

**Justificativa da Aplicação:**
* Diversidade de DTOs (ex: `InconformClientDto`, `ClientComplianceStatsDto`, `InvestmentRecommendationResponseDto`).
* Lógica de criação espalhada, resultando em múltiplas instanciações diretas.
* Necessidade de centralizar a criação para facilitar futuras alterações e evoluções dos DTOs.

##### Implementação

**Estrutura Chave:**
* **Interface do Factory:** `IDtoFactory` - Define o contrato para a criação dos DTOs.
* **Implementação Concreta do Factory:** `DtoFactory` - Implementa `IDtoFactory` e contém a lógica específica para instanciar cada DTO.

**Localização dos Arquivos:**
* `Factories/IDtoFactory.cs`
* `Factories/DtoFactory.cs`

**Exemplo de Código:**

*Interface `IDtoFactory`*:
```csharp
public interface IDtoFactory
{
    InconformClientDto CreateInconformClientDto(int clientId, string riskProfile, double score, decimal invested);
    ClientComplianceStatsDto CreateComplianceStatsDto(int conforme, int inconforme);
    InvestmentRecommendationResponseDto CreateRecommendationDto(int clientId, string email, string phone);
}
```

*Classe `DtoFactory`*:
```csharp
public class DtoFactory : IDtoFactory
{
    public InconformClientDto CreateInconformClientDto(int clientId, string riskProfile, double score, decimal invested)
    {
        return new InconformClientDto
        {
            ClientId = clientId,
            DeclaredRiskProfile = riskProfile,
            ComplianceStatus = "inconforme", // Exemplo de lógica centralizada
            NonComplianceScore = score,
            TotalInvested = invested
        };
    }

    public ClientComplianceStatsDto CreateComplianceStatsDto(int conforme, int inconforme)
    {
        return new ClientComplianceStatsDto
        {
            Conforme = conforme,
            Inconforme = inconforme
        };
    }

    public InvestmentRecommendationResponseDto CreateRecommendationDto(int clientId, string email, string phone)
    {
        return new InvestmentRecommendationResponseDto
        {
            ClientId = clientId,
            Email = email,
            Phone = phone,
            RecommendedInvestments = new List<InvestmentDto>() // Inicialização padrão
        };
    }
}
```

##### Benefícios Alcançados
1.  **Centralização da Criação**: Toda a lógica de instanciação de DTOs reside na `DtoFactory`, simplificando a gestão.
2.  **Manutenibilidade Aprimorada**: Alterações na construção ou nos campos padrão dos DTOs são feitas em um único local.
3.  **Flexibilidade**: Facilita a introdução de novos tipos de DTOs ou a modificação dos existentes sem impactar significativamente o código cliente.
4.  **Consistência**: Assegura que os DTOs sejam criados de maneira uniforme em toda a aplicação.
5.  **Teste Simplificado**: Permite a substituição (mock) da `IDtoFactory` em testes unitários, facilitando o isolamento das unidades de código.

##### Uso na Aplicação
&emsp; A `IDtoFactory` é injetada, principalmente, em serviços como o `AdvisorService`. Este serviço utiliza o factory para obter instâncias de DTOs necessárias para suas operações.

*Exemplo de injeção no `AdvisorService`*:
```csharp
public class AdvisorService
{
    private readonly IDtoFactory _dtoFactory;
    // ... outros campos e construtor

    public AdvisorService(IDtoFactory dtoFactory, /* ... outras dependências */)
    {
        _dtoFactory = dtoFactory;
        // ...
    }

    // Exemplo de uso em um método:
    public InconformClientDto GetSomeInconformClientData(int id, string profile, double score, decimal total)
    {
        // ... lógica de negócios ...
        return _dtoFactory.CreateInconformClientDto(id, profile, score, total);
    }
}
```

---

#### 2. Strategy (Padrão Comportamental)

##### Propósito
&emsp; O padrão Strategy foi implementado para gerenciar e aplicar diferentes algoritmos de ordenação para a lista de clientes inconformes. Antes, a lógica de ordenação estava acoplada diretamente no serviço usando uma estrutura `switch`, tornando a adição de novas estratégias de ordenação invasiva e propensa a erros. O Strategy Pattern permite encapsular cada algoritmo de ordenação em sua própria classe, tornando o sistema extensível e aderente ao Princípio Aberto/Fechado (Open/Closed Principle).

**Justificativa da Aplicação:**
* Lógica de ordenação de clientes inconformes concentrada em um método extenso (ex: `ApplyInconformClientsSorting` com `switch`).
* Adicionar novas estratégias de ordenação exigia modificar o código existente, violando o Princípio Aberto/Fechado.
* Necessidade de permitir a seleção dinâmica da estratégia de ordenação.

##### Implementação

**Estrutura Chave:**
* **Interface da Estratégia:** `ISortingStrategy` - Define o contrato comum para todas as estratégias de ordenação.
* **Implementações Concretas da Estratégia:**
    * `NonComplianceScoreSortingStrategy`: Ordena por pontuação de não conformidade.
    * `LastContactSortingStrategy`: Ordena pela data do último contato.
    * `TotalInvestedSortingStrategy`: Ordena pelo valor total investido.
* **Contexto:** `SortingContext` - Gerencia as estratégias disponíveis e aplica a estratégia selecionada.

**Localização dos Arquivos:**
* `Sorting/ISortingStrategy.cs`
* `Sorting/NonComplianceScoreSortingStrategy.cs`
* `Sorting/LastContactSortingStrategy.cs`
* `Sorting/TotalInvestedSortingStrategy.cs`
* `Sorting/SortingContext.cs`

**Exemplo de Código:**

*Interface `ISortingStrategy`*:
```csharp
public interface ISortingStrategy
{
    List<InconformClientDto> Sort(List<InconformClientDto> clients, bool ascending);
}
```

*Exemplo de Estratégia Concreta (`NonComplianceScoreSortingStrategy`)*:
```csharp
public class NonComplianceScoreSortingStrategy : ISortingStrategy
{
    public List<InconformClientDto> Sort(List<InconformClientDto> clients, bool ascending)
    {
        return ascending
            ? clients.OrderBy(c => c.NonComplianceScore).ToList()
            : clients.OrderByDescending(c => c.NonComplianceScore).ToList();
    }
}
```
*(Outras estratégias como `LastContactSortingStrategy` e `TotalInvestedSortingStrategy` seguiriam uma estrutura similar, adaptando o critério de ordenação.)*

*Classe `SortingContext`*:
```csharp
public class SortingContext
{
    private readonly Dictionary<string, ISortingStrategy> _strategies;

    public SortingContext()
    {
        _strategies = new Dictionary<string, ISortingStrategy>(StringComparer.OrdinalIgnoreCase)
        {
            { "noncompliancescore", new NonComplianceScoreSortingStrategy() },
            { "lastcontact", new LastContactSortingStrategy() },
            { "totalinvested", new TotalInvestedSortingStrategy() }
        };
    }

    public List<InconformClientDto> Sort(List<InconformClientDto> clients, string sortBy, string sortOrder)
    {
        var isAscending = sortOrder.Equals("asc", StringComparison.OrdinalIgnoreCase);

        if (_strategies.TryGetValue(sortBy.ToLower(), out var strategy))
        {
            return strategy.Sort(clients, isAscending);
        }

        // Estratégia padrão caso a chave não seja encontrada
        return _strategies["noncompliancescore"].Sort(clients, isAscending);
    }
}
```

##### Benefícios Alcançados
1.  **Flexibilidade e Extensibilidade**: Novas estratégias de ordenação podem ser adicionadas criando novas classes que implementam `ISortingStrategy`, sem modificar o `SortingContext` ou o serviço cliente.
2.  **Manutenibilidade**: Cada lógica de ordenação está isolada em sua própria classe, tornando o código mais fácil de entender, manter e testar.
3.  **Aderência ao SOLID**: Respeita o Princípio Aberto/Fechado (aberto para extensão, fechado para modificação) e o Princípio da Responsabilidade Única (cada estratégia tem uma única responsabilidade).
4.  **Clareza do Código**: A lógica de seleção e aplicação da estratégia é bem definida no `SortingContext`, tornando o código do serviço cliente mais limpo.

##### Estratégias Implementadas
* **`NonComplianceScoreSortingStrategy`**: Ordena os clientes pela pontuação de não conformidade.
* **`LastContactSortingStrategy`**: Ordena os clientes pela data do último contato (tratando valores nulos).
* **`TotalInvestedSortingStrategy`**: Ordena os clientes pelo valor total investido.

##### Uso na Aplicação
&emsp; O `SortingContext` é injetado no `AdvisorService`. Quando uma requisição para listar clientes inconformes é recebida com parâmetros de ordenação (`sortBy`, `sortOrder`), o `AdvisorService` utiliza o `SortingContext` para aplicar a estratégia de ordenação apropriada.

*Exemplo de injeção e uso no `AdvisorService`*:
```csharp
public class AdvisorService
{
    private readonly SortingContext _sortingContext;
    // ... outros campos e construtor

    public AdvisorService(SortingContext sortingContext, /* ... outras dependências */)
    {
        _sortingContext = sortingContext;
        // ...
    }

    public async Task<List<InconformClientDto>> GetInconformClientsAsync(string sortBy, string sortOrder, /*...outros params...*/)
    {
        // ... busca inicial dos clientes inconformes ...
        List<InconformClientDto> inconformClients = // ... resultado da busca ...;

        // Aplica a ordenação usando o SortingContext
        inconformClients = _sortingContext.Sort(inconformClients, sortBy, sortOrder);

        return inconformClients;
    }
}
```
### **API Classification**

O **Pati.ClassificationService** é um microserviço desenvolvido em .NET 8.0 que implementa a classificação de carteiras de investimento baseada nas regras CVM 175 e análise de filtragem colaborativa usando modelo SVD (Singular Value Decomposition). O serviço segue os princípios da Clean Architecture e está totalmente dockerizado para facilitar a implantação e escalabilidade.

#### Propósito Principal

O serviço é responsável por:

1. **Receber dados de carteiras** do `Pati.UserService` via endpoint HTTP
2. **Classificar carteiras** identificando inconformidades baseado nas regras CVM 175
3. **Integrar modelo SVD** para análise de adequação de ativos usando filtragem colaborativa
4. **Enviar resultados** para o `Pati.RecommendationService` via chamada HTTP
5. **Verificar conformidade** com regulamentações financeiras brasileiras

#### Tecnologias Utilizadas

- **.NET 8.0** - Framework principal
- **ASP.NET Core** - API Web
- **Clean Architecture** - Padrão arquitetural
- **AutoMapper** - Mapeamento de objetos
- **Entity Framework Core** - ORM para acesso a dados
- **ML.NET** - Integração com modelo SVD
- **Docker** - Containerização
- **xUnit + Moq** - Testes unitários
- **Swagger/OpenAPI** - Documentação da API

#### Organização do Projeto

#### Estrutura de Diretórios

```
src/apps/Pati.ClassificationService/
│
├── Controllers/                          # Presentation Layer
│   └── ClassificationController.cs      # Controller principal da API
│
├── Application/                          # Application Layer
│   ├── Services/                         # Serviços de aplicação
│   │   ├── IClassificationService.cs     # Interface do serviço principal
│   │   ├── ClassificationService.cs      # Implementação do serviço principal
│   │   ├── ClassificationApplicationService.cs # Serviço de aplicação
│   │   ├── ISVDModelService.cs           # Interface do serviço SVD
│   │   └── IRecommendationServiceClient.cs # Interface para comunicação externa
│   ├── Mappings/                         # Mapeamentos AutoMapper
│   │   └── MappingProfile.cs             # Perfis de mapeamento
│   └── Repositories/                     # Interfaces de repositórios
│       └── IRepositories.cs              # Definições de interfaces
│
├── Domain/                               # Domain Layer
│   ├── Portfolio.cs                      # Entidade principal - Carteira
│   ├── Asset.cs                          # Entidade Ativo
│   ├── Client.cs                         # Entidade Cliente
│   ├── Investment.cs                     # Entidade Investimento
│   ├── ClientInvestment.cs               # Entidade relacionamento Cliente-Investimento
│   ├── ClientRecommendation.cs           # Entidade Recomendação
│   ├── Advisor.cs                        # Entidade Consultor
│   ├── ClassificationRule.cs             # Entidade Regras de Classificação
│   ├── Inconsistency.cs                  # Entidade Inconformidade
│   └── RiskProfile.cs                    # Entidade Perfil de Risco
│
├── Infrastructure/                       # Infrastructure Layer
│   ├── Data/                             # Contexto de dados
│   │   └── ClassificationDbContext.cs    # Contexto Entity Framework
│   ├── Repositories/                     # Implementações de repositórios
│   │   ├── ClientRepository.cs           # Repositório de clientes
│   │   ├── AdvisorRepository.cs          # Repositório de consultores
│   │   ├── InvestmentRepository.cs       # Repositório de investimentos
│   │   ├── ClientRecommendationRepository.cs # Repositório de recomendações
│   │   ├── ClassificationRuleRepository.cs # Repositório de regras
│   │   └── UnitOfWork.cs                 # Padrão Unit of Work
│   └── Services/                         # Serviços de infraestrutura
│       ├── SVDModelService.cs            # Implementação do serviço SVD
│       └── RecommendationServiceClient.cs # Cliente HTTP para RecommendationService
│
├── DTOs/                                 # Data Transfer Objects
│   └── PortfolioDtos.cs                  # DTOs para entrada e saída da API
│
├── Configs/                              # Arquivos de configuração
│   ├── appsettings.json                  # Configurações principais
│   ├── appsettings.Development.json      # Configurações de desenvolvimento
│   └── swagger.json                      # Configuração Swagger
│
├── Properties/                           # Propriedades do projeto
│   └── launchSettings.json               # Configurações de execução
│
├── tests/                                # Testes unitários
│   ├── ClassificationControllerTests.cs  # Testes do controller
│   ├── ClassificationServiceCoverageTests.cs # Testes de cobertura do serviço
│   ├── DtoAndDomainValidationTests.cs    # Testes de validação
│   ├── PortfolioTests.cs                 # Testes da entidade Portfolio
│   └── Pati.ClassificationService.Tests.csproj # Projeto de testes
│
├── docs/                                 # Documentação
│   └── project.md                        # Este documento
│
├── Dockerfile                            # Configuração Docker
├── Program.cs                            # Ponto de entrada da aplicação
├── Pati.ClassificationService.csproj     # Arquivo do projeto
└── README.md                             # Documentação principal
```

#### Descrição das Camadas

##### **Controllers (Presentation Layer)**
- **Responsabilidade**: Receber requisições HTTP, validar entrada, chamar serviços de aplicação
- **Principais Classes**: `ClassificationController`
- **Padrões**: Controller pattern, Dependency Injection

##### **Application (Application Layer)**
- **Responsabilidade**: Orquestrar casos de uso, coordenar entre domínio e infraestrutura
- **Principais Classes**: `ClassificationService`, `SVDModelService` (interface)
- **Padrões**: Service pattern, Interface Segregation

##### **Domain (Domain Layer)**
- **Responsabilidade**: Regras de negócio, entidades, value objects
- **Principais Classes**: `Portfolio`, `Asset`, `Inconsistency`
- **Padrões**: Domain-Driven Design, Rich Domain Model

##### **Infrastructure (Infrastructure Layer)**
- **Responsabilidade**: Acesso a dados, serviços externos, implementações técnicas
- **Principais Classes**: `ClassificationDbContext`, repositórios, `SVDModelService`
- **Padrões**: Repository pattern, Unit of Work, Adapter pattern

#### Endpoints

##### Endpoint Principal

##### **POST /internal/v1/portfolios/classify**

**Descrição**: Classifica uma carteira de investimento, identifica inconformidades baseado nas regras CVM 175 e modelo SVD, e envia resultados para o `Pati.RecommendationService`.

**URL Completa**: `http://localhost:8080/internal/v1/portfolios/classify`

**Entrada (Request Body)**:
```json
{
  "accountId": 27,
  "portfolioId": "portfolio_27",
  "portfolioProfile": "Conservador",
  "assets": [
    {
      "assetId": "IGTI11_001",
      "name": "IGTI11",
      "type": "ACOES BRASIL",
      "incomeType": "Renda Variável",
      "investmentProfile": "Arrojado",
      "quantity": 6800,
      "value": 132396
    },
    {
      "assetId": "TESOURO_001",
      "name": "Tesouro Direto",
      "type": "RENDA FIXA",
      "incomeType": "Renda Fixa",
      "investmentProfile": "Conservador",
      "quantity": 1000,
      "value": 50000
    }
  ]
}
```

**Resposta (Response Body)**:
```json
{
  "accountId": 27,
  "portfolioId": "portfolio_27",
  "portfolioProfile": "Conservador",
  "assets": [
    {
      "assetId": "IGTI11_001",
      "name": "IGTI11",
      "type": "ACOES BRASIL",
      "incomeType": "Renda Variável",
      "investmentProfile": "Arrojado",
      "quantity": 6800,
      "value": 132396
    }
  ],
  "inconsistencies": [
    {
      "assetId": "IGTI11_001",
      "name": "IGTI11",
      "description": "Ativo de Renda Variável com perfil Arrojado incompatível com perfil Conservador",
      "severity": "high"
    }
  ],
  "incomeAllocation": {
    "fixedIncome": 0.27,
    "variableIncome": 0.73
  }
}
```

#### Endpoints Auxiliares

##### **GET /internal/v1/portfolios/risk-validation**

**Descrição**: Valida se um risco é permitido para um perfil específico (implementa função `risco_permitido` do notebook SVD).

**URL Completa**: `http://localhost:8080/internal/v1/portfolios/risk-validation?profile=Conservador&risk=Baixo`

**Parâmetros**:
- `profile` (string): Perfil da carteira (Conservador, Moderado, Arrojado, Sofisticado)
- `risk` (string): Nível de risco do ativo

**Resposta**:
```json
true
```

##### **GET /internal/v1/portfolios/ideal-proportions**

**Descrição**: Obtém as proporções ideais de Renda Fixa e Renda Variável para um perfil.

**URL Completa**: `http://localhost:8080/internal/v1/portfolios/ideal-proportions?profile=Conservador`

**Parâmetros**:
- `profile` (string): Perfil da carteira

**Resposta**:
```json
{
  "fixedIncome": 0.8,
  "variableIncome": 0.2
}
```

##### **GET /internal/v1/portfolios/{portfolioId}/status**

**Descrição**: Retorna o status de um portfólio específico.

**URL Completa**: `http://localhost:8080/internal/v1/portfolios/PORT-001/status`

**Resposta**:
```json
{
  "portfolioId": "PORT-001",
  "status": "active",
  "lastClassification": "2025-05-30T03:05:00Z",
  "inconsistencyCount": 2
}
```

##### **GET /internal/v1/portfolios/{portfolioId}/details**

**Descrição**: Retorna detalhes completos de um portfólio.

**URL Completa**: `http://localhost:8080/internal/v1/portfolios/PORT-001/details`

##### **GET /internal/v1/portfolios/non-compliant-clients**

**Descrição**: Lista clientes com carteiras não conformes.

**URL Completa**: `http://localhost:8080/internal/v1/portfolios/non-compliant-clients`

##### **GET /internal/v1/portfolios/dashboard**

**Descrição**: Retorna dados agregados para dashboard de classificação.

**URL Completa**: `http://localhost:8080/internal/v1/portfolios/dashboard`

##### **GET /internal/v1/portfolios/health**

**Descrição**: Verifica o status de saúde do serviço de classificação.

**URL Completa**: `http://localhost:8080/internal/v1/portfolios/health`

##### **GET /health**

**Descrição**: Health check global do serviço.

**URL Completa**: `http://localhost:8080/health`

#### Exemplos de Uso

##### Usando cURL

```bash
# Classificar uma carteira
curl -X POST "http://localhost:8080/internal/v1/portfolios/classify" \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": 27,
    "portfolioId": "portfolio_27",
    "portfolioProfile": "Conservador",
    "assets": [
      {
        "assetId": "IGTI11_001",
        "name": "IGTI11",
        "type": "ACOES BRASIL",
        "incomeType": "Renda Variável",
        "investmentProfile": "Arrojado",
        "quantity": 6800,
        "value": 132396
      }
    ]
  }'

# Validar risco para perfil
curl "http://localhost:8080/internal/v1/portfolios/risk-validation?profile=Conservador&risk=Arrojado"

# Obter proporções ideais
curl "http://localhost:8080/internal/v1/portfolios/ideal-proportions?profile=Moderado"

# Health check
curl "http://localhost:8080/health"
```

#### Padrões de Projeto

##### 1. Repository Pattern

**Propósito**: Encapsular a lógica de acesso a dados e fornecer uma interface mais orientada a objetos para a camada de domínio.

**Implementação**:

```csharp
// Interface no Domain/Application
public interface IClientRepository
{
    Task<Client?> GetByIdAsync(int clientId);
    Task<IEnumerable<Client>> GetAllAsync();
    Task AddAsync(Client client);
    Task UpdateAsync(Client client);
    Task DeleteAsync(int clientId);
}

// Implementação na Infrastructure
public class ClientRepository : IClientRepository
{
    private readonly ClassificationDbContext _context;

    public ClientRepository(ClassificationDbContext context)
    {
        _context = context;
    }

    public async Task<Client?> GetByIdAsync(int clientId)
    {
        return await _context.Clients
            .Include(c => c.Investments)
            .FirstOrDefaultAsync(c => c.ClientId == clientId);
    }
    // ... outras implementações
}
```

**Benefícios**:
- Separação de responsabilidades
- Facilita testes unitários com mocks
- Permite mudança de tecnologia de persistência sem afetar o domínio

##### 2. Strategy Pattern

**Propósito**: Gerenciar diferentes estratégias de classificação de carteiras baseadas em perfis de risco e regras CVM 175.

**Implementação**:

```csharp
// Interface da estratégia
public interface IClassificationStrategy
{
    string ProfileType { get; }
    List<Inconsistency> AnalyzePortfolio(Portfolio portfolio);
    (decimal FixedIncome, decimal VariableIncome) GetIdealProportions();
}

// Estratégia para perfil Conservador
public class ConservativeProfileStrategy : IClassificationStrategy
{
    public string ProfileType => "Conservador";

    public List<Inconsistency> AnalyzePortfolio(Portfolio portfolio)
    {
        var inconsistencies = new List<Inconsistency>();

        foreach (var asset in portfolio.Assets)
        {
            // Conservador só aceita ativos Conservadores
            if (asset.InvestmentProfile != "Conservador")
            {
                inconsistencies.Add(new Inconsistency(
                    asset.AssetId,
                    asset.Name,
                    $"Perfil {asset.InvestmentProfile} incompatível com carteira Conservadora",
                    "high"
                ));
            }
        }

        return inconsistencies;
    }

    public (decimal FixedIncome, decimal VariableIncome) GetIdealProportions()
    {
        return (0.8m, 0.2m); // 80% Renda Fixa, 20% Renda Variável
    }
}

// Uso no ClassificationService
public class ClassificationService : IClassificationService
{
    private readonly Dictionary<string, IClassificationStrategy> _strategies;

    public ClassificationService()
    {
        _strategies = new Dictionary<string, IClassificationStrategy>
        {
            { "Conservador", new ConservativeProfileStrategy() },
            { "Moderado", new ModerateProfileStrategy() },
            { "Arrojado", new AggressiveProfileStrategy() },
            { "Sofisticado", new SophisticatedProfileStrategy() }
        };
    }

    private List<Inconsistency> AnalyzeWithStrategy(Portfolio portfolio)
    {
        if (_strategies.TryGetValue(portfolio.PortfolioProfile, out var strategy))
        {
            return strategy.AnalyzePortfolio(portfolio);
        }

        throw new ArgumentException($"Estratégia não encontrada para perfil: {portfolio.PortfolioProfile}");
    }
}
```

**Benefícios**:
- Extensibilidade: Fácil adição de novos perfis
- Aderência ao Princípio Aberto/Fechado
- Código mais limpo e organizizado por responsabilidade

##### 3. Factory Method Pattern

**Propósito**: Centralizar e padronizar a criação de DTOs e entidades de domínio.

**Implementação**:

```csharp
// Interface da Factory
public interface IDtoFactory
{
    PortfolioOutputDto CreatePortfolioOutput(Portfolio portfolio, List<Inconsistency> inconsistencies);
    Portfolio CreatePortfolioFromInput(PortfolioInputDto input);
    InconsistencyDto CreateInconsistencyDto(Inconsistency inconsistency);
}

// Implementação da Factory
public class DtoFactory : IDtoFactory
{
    private readonly IMapper _mapper;

    public DtoFactory(IMapper mapper)
    {
        _mapper = mapper;
    }

    public PortfolioOutputDto CreatePortfolioOutput(Portfolio portfolio, List<Inconsistency> inconsistencies)
    {
        var currentProportions = CalculateCurrentProportions(portfolio.Assets);

        return new PortfolioOutputDto
        {
            AccountId = portfolio.AccountId,
            PortfolioId = portfolio.PortfolioId,
            PortfolioProfile = portfolio.PortfolioProfile,
            Assets = _mapper.Map<List<AssetOutputDto>>(portfolio.Assets),
            Inconsistencies = _mapper.Map<List<InconsistencyDto>>(inconsistencies),
            IncomeAllocation = new IncomeAllocationDto
            {
                FixedIncome = currentProportions.FixedIncome,
                VariableIncome = currentProportions.VariableIncome
            }
        };
    }

    public Portfolio CreatePortfolioFromInput(PortfolioInputDto input)
    {
        var assets = _mapper.Map<List<Asset>>(input.Assets);
        return new Portfolio(input.AccountId, input.PortfolioId, input.PortfolioProfile, assets);
    }
}
```

**Benefícios**:
- Centralização da lógica de criação
- Consistência na criação de objetos
- Facilita manutenção e testes

##### 4. Unit of Work Pattern

**Propósito**: Manter uma lista de objetos afetados por uma transação de negócio e coordenar a escrita das mudanças.

**Implementação**:

```csharp
public interface IUnitOfWork : IDisposable
{
    IClientRepository Clients { get; }
    IInvestmentRepository Investments { get; }
    IAdvisorRepository Advisors { get; }
    IClientRecommendationRepository ClientRecommendations { get; }

    Task<int> SaveChangesAsync();
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}

public class UnitOfWork : IUnitOfWork
{
    private readonly ClassificationDbContext _context;
    private IDbContextTransaction? _transaction;

    public UnitOfWork(ClassificationDbContext context)
    {
        _context = context;
        Clients = new ClientRepository(_context);
        Investments = new InvestmentRepository(_context);
        // ... outros repositórios
    }

    public IClientRepository Clients { get; }
    public IInvestmentRepository Investments { get; }
    // ... outras propriedades

    public async Task<int> SaveChangesAsync()
    {
        return await _context.SaveChangesAsync();
    }
}
```

**Benefícios**:
- Controle transacional
- Consistência de dados
- Performance otimizada (batch operations)

#### Testes

##### Estrutura de Testes

O projeto possui uma cobertura abrangente de testes unitários organizados em 4 arquivos principais:

##### **ClassificationControllerTests.cs**
- **Propósito**: Testa os endpoints da API e validações de entrada
- **Cenários Cobertos**:
  - Validação de DTOs de entrada
  - Respostas HTTP corretas
  - Tratamento de erros
  - Integração com serviços de aplicação

```csharp
[Fact]
public async Task ClassifyPortfolio_ValidInput_ReturnsOkResult()
{
    // Arrange
    var portfolioInput = new PortfolioInputDto
    {
        AccountId = 27,
        PortfolioId = "portfolio_27",
        PortfolioProfile = "Conservador",
        Assets = new List<AssetInputDto> { /* assets */ }
    };

    // Act
    var result = await _controller.ClassifyPortfolio(portfolioInput);

    // Assert
    var okResult = Assert.IsType<OkObjectResult>(result.Result);
    var response = Assert.IsType<PortfolioOutputDto>(okResult.Value);
    Assert.Equal(27, response.AccountId);
}
```

##### **ClassificationServiceCoverageTests.cs**
- **Propósito**: Testa a lógica de negócio do serviço principal
- **Cenários Cobertos**:
  - Classificação de carteiras por perfil
  - Identificação de inconformidades
  - Cálculo de proporções de renda
  - Integração com modelo SVD

##### **DtoAndDomainValidationTests.cs**
- **Propósito**: Testa validações de DTOs e entidades de domínio
- **Cenários Cobertos**:
  - Validações de campos obrigatórios
  - Validações de formato e tamanho
  - Regras de negócio em entidades
  - Mapeamentos AutoMapper

##### **PortfolioTests.cs**
- **Propósito**: Testa a entidade Portfolio e suas regras de negócio
- **Cenários Cobertos**:
  - Função `risco_permitido` (regras CVM 175)
  - Cálculo de proporções ideais
  - Identificação de inconformidades
  - Validações de perfil de carteira

```csharp
[Theory]
[InlineData("Conservador", "Conservador", true)]
[InlineData("Conservador", "Moderado", false)]
[InlineData("Moderado", "Conservador", true)]
[InlineData("Moderado", "Moderado", true)]
[InlineData("Arrojado", "Arrojado", true)]
public void IsRiskAllowedForProfile_ValidatesCorrectly(string profile, string risk, bool expected)
{
    // Act
    var result = Portfolio.IsRiskAllowedForProfile(profile, risk);

    // Assert
    Assert.Equal(expected, result);
}
```

##### Execução de Testes

```bash
# Executar todos os testes
dotnet test

# Executar com cobertura de código
dotnet test --collect:"XPlat Code Coverage"

# Executar testes específicos
dotnet test --filter "ClassificationServiceTests"

# Executar com output detalhado
dotnet test --logger "console;verbosity=detailed"
```

##### Métricas de Teste

- **Total de Testes**: 57 testes
- **Taxa de Sucesso**: 100%
- **Cobertura de Código**: >80%
- **Tempo de Execução**: ~2-3 segundos

##### Referência Adicional

Para detalhes completos sobre os testes, consulte o arquivo [README.md](../src/apps/api_classification/tests/README.md) que contém:
- Descrição detalhada de cada arquivo de teste
- Cenários específicos testados
- Instruções para adicionar novos testes
- Configurações de mocking e fixtures

#### Integrações

##### 1. Banco de Dados Relacional

###### **ClassificationDbContext**

**Tecnologia**: Entity Framework Core com suporte a PostgreSQL/SQL Server

**Configuração** (`appsettings.json`):
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=pati_db;Username=pati_user;Password=pati_password_2025;"
  }
}
```

**Entidades Mapeadas**:
- `Client` - Dados dos clientes
- `Investment` - Tipos de investimentos
- `ClientInvestment` - Relacionamento cliente-investimento
- `Advisor` - Consultores financeiros
- `ClientRecommendation` - Recomendações geradas
- `ClassificationRule` - Regras de classificação CVM 175

**Configuração no Program.cs**:
```csharp
// Database configuration for PostgreSQL
builder.Services.AddDbContext<ClassificationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));
```

##### 2. Modelo SVD (Singular Value Decomposition)

###### **SVDModelService**

**Propósito**: Integração com modelo de machine learning para filtragem colaborativa

**Configuração** (`appsettings.json`):
```json
{
  "SVDModel": {
    "ModelPath": "/app/models/modelo_svd_treinado.pkl",
    "MinSuitabilityThreshold": 0.5,
    "DefaultRecommendationCount": 5,
    "EnableModelCaching": true
  }
}
```

**Funcionalidades**:
- Carregamento do modelo treinado (`.pkl`)
- Predição de adequação de ativos para clientes
- Recomendação de ativos baseada em similaridade
- Cache de resultados para performance

**Interface**:
```csharp
public interface ISVDModelService
{
    Task<bool> LoadModelAsync();
    bool IsModelLoaded { get; }
    Task<List<Inconsistency>> AnalyzePortfolioAsync(Portfolio portfolio);
    Task<List<string>> RecommendAssetsForClientAsync(int accountId, string portfolioProfile, int topN = 5);
    Task<double> PredictAssetSuitabilityAsync(int accountId, string assetId);
}
```

##### 3. Pati.UserService (Entrada)

**Comunicação**: Recebe dados via HTTP POST

**Endpoint de Entrada**: `POST /internal/v1/portfolios/classify`

**Formato de Dados**: Baseado no arquivo `inconformidades.csv`

**Fluxo**:
1. UserService envia dados da carteira
2. ClassificationService processa e classifica
3. Retorna resultado com inconformidades identificadas

##### 4. Pati.RecommendationService (Saída)

**Comunicação**: Envia dados via HTTP POST

**Configuração** (`appsettings.json`):
```json
{
  "RecommendationService": {
    "BaseUrl": "http://localhost:5001",
    "TimeoutSeconds": 30
  }
}
```

**Configuração HTTP Client**:
```csharp
builder.Services.AddHttpClient("RecommendationService", client =>
{
    client.BaseAddress = new Uri(builder.Configuration.GetValue<string>("RecommendationService:BaseUrl"));
    client.Timeout = TimeSpan.FromSeconds(builder.Configuration.GetValue<int>("RecommendationService:TimeoutSeconds", 30));
});
```

**Fluxo**:
1. ClassificationService processa carteira
2. Identifica inconformidades e calcula métricas
3. Envia resultado para RecommendationService
4. RecommendationService gera recomendações baseadas na análise

#### Dockerização

##### Configuração do Dockerfile

O serviço está totalmente dockerizado usando uma abordagem multi-stage para otimizar o tamanho da imagem final:

```dockerfile
# Dockerfile for Pati.ClassificationService
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project file and restore dependencies
COPY ["apps/Pati.ClassificationService/Pati.ClassificationService.csproj", "apps/Pati.ClassificationService/"]
RUN dotnet restore "apps/Pati.ClassificationService/Pati.ClassificationService.csproj"

# Copy source code and build
COPY apps/Pati.ClassificationService/ ./apps/Pati.ClassificationService/
WORKDIR "/src/apps/Pati.ClassificationService"
RUN dotnet build "Pati.ClassificationService.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "Pati.ClassificationService.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create directories for models and data
RUN mkdir -p /app/models /app/data

# Copy SVD model and CSV files
COPY models/ /app/models/
COPY database/inconformidades.csv /app/data/
COPY database/Tipo_ativos_com_risco.csv /app/data/

# Set environment variables for model paths
ENV SVDModel__ModelPath=/app/models/modelo_svd_treinado.pkl
ENV DataFiles__InconformidadesPath=/app/data/inconformidades.csv
ENV DataFiles__TipoAtivosPath=/app/data/Tipo_ativos_com_risco.csv

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

ENTRYPOINT ["dotnet", "Pati.ClassificationService.dll"]
```

##### Integração com Docker Compose

O serviço está integrado ao `docker-compose.yml` principal do projeto:

```yaml
services:
  classification-service:
    build:
      context: .
      dockerfile: ./apps/Pati.ClassificationService/Dockerfile
    ports:
      - "8080:8080"  # Porta externa:interna
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - Urls=http://+:8080
      - ServiceConfiguration__BaseUrl=http://classification-service:8080
      - ConnectionStrings__DefaultConnection=Host=db;Database=pati_db;Username=pati_user;Password=pati_password_2025;
      - RecommendationService__BaseUrl=http://recommendation-service:80
      - SVDModel__ModelPath=/app/models/modelo_svd_treinado.pkl
      - DataFiles__InconformidadesPath=/app/data/inconformidades.csv
      - DataFiles__TipoAtivosPath=/app/data/Tipo_ativos_com_risco.csv
    depends_on:
      - db
    networks:
      - app-network
    volumes:
      - ./database/inconformidades.csv:/app/data/inconformidades.csv:ro
      - ./database/Tipo_ativos_com_risco.csv:/app/data/Tipo_ativos_com_risco.csv:ro
```

#### Instruções de Execução

##### Executar apenas o Classification Service

```bash
# Navegar para o diretório raiz do projeto
cd C:\Projetos\modulo6\2025-1B-T13-ES06-G02\src

# Construir e executar apenas o classification-service
docker-compose up --build classification-service
```

##### Executar todos os serviços

```bash
# Navegar para o diretório raiz do projeto
cd C:\Projetos\modulo6\2025-1B-T13-ES06-G02\src

# Construir e executar todos os serviços
docker-compose up --build
```

##### Construir imagem individual

```bash
# Construir apenas a imagem do classification-service
docker build -t pati-classification-service:latest -f apps/Pati.ClassificationService/Dockerfile .

# Executar container individual
docker run -p 8080:8080 \
  -e ConnectionStrings__DefaultConnection="Host=db;Database=pati_db;Username=pati_user;Password=pati_password_2025;" \
  -e RecommendationService__BaseUrl="http://recommendation-service:80" \
  -e SVDModel__ModelPath="/app/models/modelo_svd_treinado.pkl" \
  pati-classification-service:latest
```

#### URLs de Acesso

##### Desenvolvimento Local
- **API Base**: `http://localhost:8080`
- **Swagger UI**: `http://localhost:8080/swagger`
- **Health Check**: `http://localhost:8080/health`

##### Docker (Rede Externa)
- **API Base**: `http://localhost:8080`
- **Swagger UI**: `http://localhost:8080/swagger`
- **Health Check**: `http://localhost:8080/health`

##### Docker (Rede Interna)
- **API Base**: `http://classification-service:8080`
- **Health Check**: `http://classification-service:8080/health`

#### Verificação da Execução

```bash
# Verificar se o container está rodando
docker ps

# Verificar logs do serviço
docker-compose logs classification-service

# Testar health check
curl http://localhost:8080/health

# Testar endpoint principal
curl -X POST http://localhost:8080/internal/v1/portfolios/classify \
  -H "Content-Type: application/json" \
  -d '{"accountId": 27, "portfolioId": "test", "portfolioProfile": "Conservador", "assets": []}'
```

#### Configurações de Ambiente

O serviço suporta diferentes configurações através de variáveis de ambiente:

| Variável | Descrição | Valor Padrão |
|----------|-----------|--------------|
| `ASPNETCORE_ENVIRONMENT` | Ambiente de execução | `Production` |
| `ASPNETCORE_URLS` | URLs de binding | `http://+:8080` |
| `ConnectionStrings__DefaultConnection` | String de conexão do banco | - |
| `RecommendationService__BaseUrl` | URL do RecommendationService | `http://localhost:5001` |
| `SVDModel__ModelPath` | Caminho do modelo SVD | `/app/models/modelo_svd_treinado.pkl` |
| `DataFiles__InconformidadesPath` | Caminho do CSV de inconformidades | `/app/data/inconformidades.csv` |

#### Conclusão

O **Pati.ClassificationService** é um microserviço bem estruturado que implementa:

-  **Clean Architecture** com separação clara de responsabilidades
-  **Padrões de Projeto** (Repository, Strategy, Factory, Unit of Work)
-  **Testes Abrangentes** com 57 testes unitários (100% de sucesso)
-  **Integração SVD** para análise de filtragem colaborativa
-  **Conformidade CVM 175** com regras de classificação de carteiras
-  **Dockerização Completa** com docker-compose
-  **Documentação Detalhada** com Swagger/OpenAPI

O serviço está pronto para produção e pode ser facilmente estendido para novos requisitos de classificação de carteiras e integração com outros sistemas financeiros.

#### Próximos Passos

1. **Implementar autenticação** para endpoints internos
2. **Adicionar métricas** e observabilidade
3. **Otimizar performance** do modelo SVD
4. **Implementar cache** para resultados de classificação
5. **Adicionar testes de integração** end-to-end

#### Suporte e Documentação

- **Swagger UI**: `http://localhost:8080/swagger`
- **README Principal**: `../README.md`
- **Documentação de Testes**: `../tests/README.md`

## Frontend
Nessa seção mostramos o nosso frontend completo, funcionando com dados mockados sem nenhuma integração.

<div align="center">
  <sub>Figura X - Tela de Login</sub> <br>

  <img src="./assets/imagens_front/loginpage.png" alt="Mockup da tela perfil" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>
Esta é a tela de login de aplicativo. Ainda sem autenticação, porém já conta com campos de "e-mail", "senha" e um botao de login para ser redirecionado para a nossa homepage.

<div align="center">
  <sub>Figura X - Tela Inicial</sub> <br>

  <img src="./assets/imagens_front/homepage.png" alt="Mockup da tela perfil" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>
Esta é a tela inicial do nosso projeto. No topo, o cabeçalho (header) oferece funcionalidades de filtro e um ícone de pesquisa. Logo abaixo, temos um gráfico funcional que exibe a porcentagem de inconformidade dos clientes – por exemplo, '40% dos clientes estão inconformes'. Na sequência, são apresentados cards individuais para cada cliente(que quando clicados, abrem o perfil do respectivo cliente), detalhando informações como perfil de investimento, investimento total, data do último contato e seu respectivo status de conformidade.

<div align="center">
  <sub>Figura X - Funcionalidade Search</sub> <br>

  <img src="./assets/imagens_front/searchbar.png" alt="Mockup da tela perfil" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>
Quando clicamos no Icone de pesquisa na header, temos essa search bar que nos permite filtrar os clientes por nome, que facilita para o assessor achar o cliente.

<div align="center">
  <sub>Figura X - Funcionalidade Filtro</sub> <br>

  <img src="./assets/imagens_front/filtro.png" alt="Mockup da tela perfil" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>
Quando clicamos no icone de filtro, abre um grande card branco, que nos permite filtrar os clientes por: status (conforme ou inconfome), perfil(conservador, moderado e arrojado), investimento total(maior investimento para menor e menor para maior) e por fim por último contato(mais recente e mais antigo).

<div align="center">
  <sub>Figura X - Tela do Cliente</sub> <br>

  <img src="./assets/imagens_front/clientpage.png" alt="Mockup da tela perfil" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>
Na tela do cliente, o header é similar ao da tela anterior, mas aqui se restringe à função de busca. Logo abaixo dele, um banner apresenta informações do cliente selecionado, como nome e perfil. Na sequência, encontra-se um botão que permite alternar a visualização entre 'Recomendações' e 'Carteira'. Após essa seção, um texto explica a proposta de investimento, e, por fim, são exibidos os cards dos investimentos recomendados.

<div align="center">
  <sub>Figura X - Tela da Carteira do Cliente</sub> <br>

  <img src="./assets/imagens_front/carteira.png" alt="Mockup da tela perfil" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>
Essa tela segue o mesmo padrão da anterior, porém ao invés de mostrar recomendações ela mostra a carteira original do cliente.

<div align="center">
  <sub>Figura X - Funcionalidade de Search do Cliente</sub> <br>

  <img src="./assets/imagens_front/searchcliente.png" alt="Mockup da tela perfil" style="max-width: 300px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

Quando o icone de lupa na header é clicado abre uma barra de pesquisa, que permite pesquisar fundos de investimento da forma que preferir, tanto pelo nome tanto pela gestora.

### Video da Aplicação:

<div align="center">
  <sub>Figura X - Demonstração da Aplicação</sub> <br>

  <video width="600" controls>
    <source src="./assets/imagens_front/videofront.mp4" type="video/mp4">
    Seu navegador não suporta a tag de vídeo.
  </video>

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

### API ML Recommender

&emsp; A **API ML Recommender** É um serviço desenvolvido em **ASP.NET Core 9** com o objetivo de analisar carteiras de investimentos dos clientes e fornecer recomendações para adequar seus perfis de risco conforme a Resolução CVM 175. O serviço utiliza **Machine Learning** (modelo SVD - Matrix Factorization) para sugerir novos ativos e realiza verificações de conformidade entre o perfil declarado pelo cliente e sua carteira atual de investimentos.

#### Organização do Projeto

&emsp; A aplicação segue uma arquitetura em camadas bem definida e separando as responsabilidades, como mostrado a seguir:

##### Estrutura de Diretórios


```text
src/apps/api-ml-recommender
│   .dockerignore
│   api-ml-recommender.csproj
│   appsettings.Development.json
│   appsettings.json
│   Dockerfile
│   Program.cs
│
├───Controllers
│       HealthController.cs
│       RecomendacaoController.cs
│
├───DTOs
│       InvestimentoDto.cs
│       RecomendacaoRequestDto.cs
│       RecomendacaoResponseDto.cs
│
├───Models
│       Cliente.cs
│       Investimento.cs
│       Recomendacao.cs

└───Services
        CsvReaderService.cs
        IRecomendacaoService.cs
        RecomendacaoService.cs
```

> Está localizado em `./src/apps/api-ml-recommender`



##### Controllers

&emsp; Responsáveis por receber as requisições HTTP, validar os dados de entrada e acionar os serviços adequados:

* `RecomendacaoController.cs`: expõe os endpoints relacionados a geração de recomendações, cálculos de score de adequação e listagem de clientes inconformes.
* `HealthController.cs`: expõe endpoints para verificação de saúde da aplicação e suas dependências.

##### Services

&emsp; Contêm toda a lógica de negócio da aplicação, incluindo processamento de dados, treinamento do modelo de ML e aplicação das regras de conformidade:

* `RecomendacaoService.cs`: serviço principal que gerencia recomendações, análise de carteiras, cálculo de scores e treinamento do modelo SVD.
* `CsvReaderService.cs`: responsável pela leitura e processamento dos arquivos CSV com dados de clientes e informações de risco dos ativos.

Observação: Atualmente estamos lendo o arquivo inconformidades.csv, pois ainda não temos os serviços integrados. Quando tivermos o serviço de classificação integrado, ele fornecerá os clientes inconformes diretamente para o serviço de recomendação.

##### DTOs (Data Transfer Objects)

&emsp; Definem a estrutura dos dados que transitam entre o frontend e o backend:

* `RecomendacaoRequestDto.cs`: define o formato dos dados enviados para solicitar uma recomendação.
* `InvestimentoDto.cs`: representa cada investimento dentro da carteira do cliente.
* `RecomendacaoResponseDto.cs`: define o formato da resposta com recomendações, ajustes e análises.

##### Models

&emsp; Representam as entidades principais do domínio de negócio:

* `Cliente.cs`: representa um cliente com suas informações e perfil de risco.
* `Investimento.cs`: representa um investimento individual com suas características.
* `Recomendacao.cs`: representa uma recomendação gerada pelo sistema.

##### Arquivos dentro do RecommendationService
&emsp; Contém os arquivos CSV utilizados pelo sistema:

* `inconformidades.csv`: dados simulados de clientes e suas carteiras para treinamento do modelo.
* `Tipo_ativos_com_risco.csv`: classificação de risco para diferentes tipos de ativos.

---

#### Endpoints

&emsp; A seguir estão os endpoints implementados no serviço, com detalhamento de propósito, parâmetros e formatos de entrada/saída.

##### 1. Teste de Funcionamento

**Endpoint:**
`GET /api/recomendacao/teste`

**Descrição:** Verifica se a API está funcionando corretamente e retorna informações básicas do serviço.

**Resposta:**

```json
{
  "message": "API de Recomendação funcionando corretamente",
  "timestamp": "datetime",
  "version": "string"
}
```

---

##### 2. Gerar Recomendação

**Endpoint:**
`POST /api/recomendacao/gerar`

**Descrição:** Gera uma recomendação personalizada para um cliente, analisando sua carteira atual e sugerindo novos ativos baseado no modelo de Machine Learning e regras de conformidade.

**Entrada:**

```json
{
  "cpf": "12345678901",
  "nome": "João da Silva",
  "perfilRisco": "Conservador",
  "investimentos": [
    {
      "codigo": "ABC123",
      "nome": "Tesouro Selic",
      "categoria": "Renda Fixa",
      "tipoAtivo": "Tesouro Direto",
      "nivelRisco": 1,
      "valorAtual": 1000.00,
      "percentualCarteira": 100
    }
  ]
}
```

**Resposta:**

```json
{
  "cliente": {
    "cpf": "12345678901",
    "nome": "João da Silva",
    "perfilRisco": "Conservador"
  },
  "analiseCarteira": {
    "scoreAdequacao": 85.5,
    "statusConformidade": "Conforme",
    "valorTotalCarteira": 1000.00,
    "distribuicaoRisco": {
      "baixoRisco": 100,
      "medioRisco": 0,
      "altoRisco": 0
    }
  },
  "recomendacoes": [
    {
      "tipoAtivo": "CDB",
      "categoria": "Renda Fixa",
      "nivelRisco": 1,
      "justificativa": "Ativo adequado ao perfil conservador",
      "prioridadeRecomendacao": "Alta"
    }
  ],
  "ajustesSugeridos": [
    {
      "acao": "Diversificar",
      "descricao": "Considere diversificar em outros ativos de renda fixa",
      "impactoScore": 5.0
    }
  ],
  "resumo": {
    "dataAnalise": "datetime",
    "totalRecomendacoes": 1,
    "melhoriaEstimada": 5.0
  }
}
```

---

##### 3. Calcular Score de Adequação

**Endpoint:**
`POST /api/recomendacao/score-adequacao`

**Descrição:** Calcula apenas o score de adequação de uma carteira específica sem gerar recomendações completas.

**Entrada:**

```json
{
  "perfilRisco": "Conservador",
  "investimentos": [
    {
      "codigo": "ABC123",
      "nome": "Tesouro Selic",
      "categoria": "Renda Fixa",
      "tipoAtivo": "Tesouro Direto",
      "nivelRisco": 1,
      "valorAtual": 1000.00,
      "percentualCarteira": 100
    }
  ]
}
```

**Resposta:**

```json
{
  "scoreAdequacao": 85.5,
  "perfilRisco": "Conservador",
  "valorTotalInvestimentos": 1000.00,
  "dataCalculo": "datetime"
}
```

---

##### 4. Listar Clientes Inconformes

**Endpoint:**
`GET /api/recomendacao/inconformes`

**Descrição:** Retorna a lista de clientes que estão em situação de inconformidade (descompasso entre perfil declarado e carteira atual).



```json
[
  {
  "score": 54.0,
  "perfilRisco": "Conservador",
  "totalInvestimentos": 1,
  "dataCalculo": "2025-05-29T23:23:56.4397121-03:00"
}
]
```

---

##### 5. Verificação de Saúde Básica

**Endpoint:**
`GET /api/health`

**Descrição:** Verifica o status básico de saúde da aplicação.

**Resposta:**

```json
{
  "status": "Healthy",
  "timestamp": "datetime"
}
```

---


#### Funcionalidades Principais

##### 1. Machine Learning com SVD (Matrix Factorization)

&emsp; O serviço implementa um modelo de **Matrix Factorization usando SVD** para gerar recomendações personalizadas:

* **Treinamento**: O modelo é treinado com dados históricos de clientes e suas carteiras (arquivo `inconformidades.csv`).
* **Predição**: Para cada novo cliente, o modelo sugere ativos baseado em padrões de clientes similares.
* **Atualização**: O modelo pode ser retreinado periodicamente com novos dados.

##### 2. Análise de Conformidade CVM 175

&emsp; O sistema verifica se a carteira do cliente está adequada ao seu perfil de risco declarado:

* **Score de Adequação**: Calcula um score de 0 a 100 indicando o nível de adequação.
* **Classificação de Risco**: Utiliza o arquivo `Tipo_ativos_com_risco.csv` para classificar cada ativo.
* **Regras de Negócio**: Aplica regras específicas da resolução CVM 175 para determinar conformidade.

##### 3. Processamento de Dados CSV

&emsp; O `CsvReaderService` processa os arquivos de dados.

---

#### Validações e Regras de Negócio

##### Validações de Entrada

&emsp; Os DTOs implementam validações rigorosas:

* **CPF**: Deve ter exatamente 11 dígitos numéricos.
* **Perfil de Risco**: Deve ser um dos valores válidos (Conservador, Moderado, Arrojado).
* **Investimentos**: Lista não pode estar vazia e cada item deve ter todos os campos obrigatórios.
* **Valores**: Devem ser positivos e dentro de faixas aceitáveis.

##### Regras de Conformidade

* **Perfil Conservador**: Máximo 10% em ativos de alto risco.
* **Perfil Moderado**: Máximo 30% em ativos de alto risco.
* **Perfil Arrojado**: Sem restrições específicas, mas deve manter diversificação.

#

#### Containerização

&emsp; A aplicação está preparada para execução em containers Docker:

##### Dockerfile

&emsp; O arquivo `Dockerfile` está configurado para:

* **Base Image**: `mcr.microsoft.com/dotnet/aspnet:9.0` para runtime.
* **Build Image**: `mcr.microsoft.com/dotnet/sdk:9.0` para compilação.
* **Otimizações**: Multi-stage build para imagens menores.
* **Dados**: Arquivos CSV são copiados para o container.

##### Execução

```bash
cd src
docker-compose up --build  
```

---

A API ML Recommender oferece uma forma prática de verificar se a carteira de investimentos de um cliente está adequada ao seu perfil de risco, seguindo os critérios da Resolução CVM 175. O serviço analisa os dados e sugere possíveis ajustes com base em um modelo de machine learning. A estrutura do projeto foi organizada para facilitar a manutenção e futuras integrações.

## 8.3.2 Sprint 4

### API Data Presentation

&emsp; Esta aplicação foi refatorada porque, inicialmente, havia sido desenvolvida com base em uma modelagem de dados específica. No entanto, devido a decisões do grupo, essa modelagem foi alterada, o que exigiu mudanças significativas na estrutura da aplicação. Como a nova abordagem tornaria o sistema mais simples, optou-se por reconstruí-lo do zero — tanto para facilitar o desenvolvimento quanto como oportunidade de aprendizado. Desta vez, a aplicação foi construída já integrada aos bancos de dados, que haviam sido implementados na sprint anterior. Isso permitiu um desenvolvimento mais assertivo, pois os dados estavam disponíveis desde o início do processo. A nova estrutura de diretórios ficou da seguinte forma:


```
src/apps/api_data_presentation/api_data_presentation
│   .dockerignore
│   .env
│   api_data_presentation.csproj
│   appsettings.Development.json
│   appsettings.json
│   Dockerfile
│   Program.cs
│   
├───Controllers
│       AdvisorController.cs
│       
├───Models
│   ├───DTOs
│   │       AdvisorAuthRequestDto.cs
│   │       AdvisorAuthResponseDto.cs
│   │       ClientInvestmentDto.cs
│   │       ClientListDto.cs
│   │       ClientRecommendationsDto.cs
│   │       ComplianceStatsDto.cs
│   │       
│   ├───Entities
│   │       Advisor.cs
│   │       Client.cs
│   │       ClientInvestment.cs
│   │       Investment.cs
│   │       MongoRecommendation.cs
│   │       
│   └───QueryParameters
│           ClientListQueryParams.cs
│           InvestmentQueryParams.cs
|
├───Properties
│       launchSettings.json
│       
├───Repositories
│   ├───Interfaces
│   │       IClientRepository.cs
│   │       IRecommendationRepository.cs
│   │       
│   ├───MongoDB
│   │       RecommendationRepository.cs
│   │       
│   └───PostgreSQL
│           ClientRepository.cs
│           
└───Services
    │   AdvisorService.cs
    │   
    └───Interfaces
            IAdvisorService.cs
```

> A aplicação está localizada em `./src/apps/api_data_presentation/api_data_presentation`

&emsp; Para iniciar é possível fazer da mesma forma que foi mostrada na seção [8.3.1 Sprint 3](#831-sprint-3) mas agora que é possível já iniciar com os bancos de dados através do docker-compose.yml presente em `./src/`, então basta navegar até o diretório até `./src/` e executar:

```bash
docker-compose up --build
```

&emsp; E então esta aplicação será rodada de forma conteinerizada na mesma rede Docker que os bancos de dados e as outras aplicações.

&emsp; O funcionamento da aplicação permanece bastante similar ao da sprint anterior. Por esse motivo, não se faz necessário detalhar novamente o funcionamento de cada camada e classe, uma vez que essa explicação já foi abordada anteriormente.

&emsp; Nesta sprint, o foco esteve na **refatoração e organização do código**. Consolidamos diversos métodos que apresentavam funcionalidades muito semelhantes, promovendo maior coesão e legibilidade. Também realizamos uma limpeza estrutural da aplicação, removendo elementos desnecessários — como o arquivo `DependencyInjection.cs`.

&emsp; Essas melhorias foram possíveis — e desejáveis — graças ao avanço do conhecimento da equipe na stack **ASP.NET**, que se aprimorou significativamente de uma sprint para outra.

&emsp; Outro ponto importante foi a **remoção do endpoint** `/api/advisor/client/contact`, responsável por atualizar a data do último contato com um cliente. Após discussão em equipe, decidimos descontinuar essa funcionalidade.

&emsp; Alguns aspectos, como payloads, query parameters e respostas, passaram por pequenas alterações. Em vez de descrevê-los textualmente, optamos por anexar um documento já utilizado internamente pela equipe. Esse material tem como objetivo esclarecer os contratos entre frontend e backend e foi gerado diretamente a partir de uma captura de tela do Swagger, garantindo, assim, maior precisão na documentação dos endpoints e suas particularidades. Acesse o documento clicando [aqui](./assets/8_desenvolvimento_do_projeto/docs_api_data_presentation.pdf).

> Devido à refatoração da aplicação, o antigo projeto de testes foi removido e substituído por um novo projeto dedicado exclusivamente aos testes. Este novo projeto foca em testes de integração, utilizando abordagens mais sofisticadas, como o uso de Gherkin. Para mais detalhes, consulte o [README.md](../src/apps/api_data_presentation/api_data_presentation.Tests.Integration/README.md) localizado no próprio projeto.


## 8.3.3 Sprint 5

_conteúdo_

## 8.4 Testes Unitários e de Integração

### 8.4.1 Planejamento de Testes de Integração

&emsp; Esta seção descreve o planejamento dos testes de integração para validar a comunicação entre os componentes internos da solução, bem como entre o sistema e serviços externos. O objetivo principal é garantir que os módulos interajam corretamente e que os dados fluam conforme esperado, mesmo diante de falhas parciais ou comportamentos inesperados.

#### Objetivos Gerais

* Verificar a correta comunicação entre os três serviços principais: **classificação**, **recomendação** e **usuário**.
* Validar a integração com os bancos de dados relacionais (PostgreSQL) e não relacionais (MongoDB).
* Testar o consumo das **APIs externas** (Supabase Auth e RandomUser) e o funcionamento do **módulo de caching VHS**.
* Assegurar que o sistema lida corretamente com falhas de comunicação, indisponibilidades e inconsistências entre sistemas.


#### Casos de Teste de Integração

| ID   | Interação Crítica                         | Cenário                                                         | Pré-condições                          | Pós-condições                                | Resultado Esperado                                   |
| ---- | ----------------------------------------- | --------------------------------------------------------------- | -------------------------------------- | -------------------------------------------- | ---------------------------------------------------- |
| TI01 | Classificação → MongoDB                   | Serviço grava perfil de risco e status de conformidade          | CSV válido enviado                     | Documento salvo em `Carteiras Classificadas` | Dados armazenados corretamente e sem duplicação      |
| TI02 | Recomendação → MongoDB                    | Serviço identifica cliente inconforme e gera recomendações      | Documento "inconforme" no MongoDB      | Documento salvo em `Recomendações`           | Recomendação gerada com data e investimentos válidos |
| TI03 | Usuário → PostgreSQL                      | Listagem de clientes de um assessor autenticado                 | Assessor autenticado com `uid` válido  | Lista de clientes vinculada ao `advisor_id`  | HTTP 200 com dados consistentes                      |
| TI04 | Usuário → MongoDB                         | Solicitação de recomendações por cliente                        | Cliente com recomendação gerada        | Recomendação retornada corretamente          | Dados coerentes com a base e cliente                 |
| TI05 | VHS Cache → RandomUser                    | Cache ativo para respostas de geração de clientes               | Requisição já realizada anteriormente  | Dados retornados do cache                    | Redução de tempo e uso do serviço externo            |
| TI06 | Supabase Auth → PostgreSQL                | Criação automática de `advisor_id` ao autenticar com novo `uid` | Novo `uid` enviado ao backend          | `advisor_id` persistido e vinculado          | Assessor criado com sucesso                          |
| TI07 | API RandomUser → VHS → Serviço de Usuário | Falha de rede simula indisponibilidade da API externa           | Cache pré-existente                    | Dados retornados mesmo sem acesso externo    | Sistema resiliente à falha externa                   |
| TI08 | MongoDB inconsistente                     | Documento com schema incompleto em `Carteiras Classificadas`    | Inserção intencional com campo ausente | Erro tratado e logado                        | Sistema ignora ou remove o registro inválido         |


### Instruções de Execução

1. **Ambiente de Testes:**

   * Executar serviços via `docker-compose`.
   * Utilizar bancos de dados isolados para testes com dados fictícios.
   * Monitorar logs e tempos de resposta.

2. **Mock de Falhas:**

   * Simular falhas com ferramentas como `nock`, `Mock Service Worker` ou bloqueios de rede (iptables/fake DNS).
   * Avaliar tratamento de falhas, mensagens de erro, e logs.

3. **Verificação de Cache VHS:**

   * Limpar cache e registrar tempo de resposta da primeira requisição.
   * Repetir requisição e verificar ganho de performance e ausência de novas chamadas externas.

4. **Validação dos Dados:**

   * Verificar consistência entre as bases PostgreSQL e MongoDB.
   * Garantir integridade referencial, onde aplicável.


### Abrangência e Justificativa

&emsp; Os testes cobrem todas as interações críticas do sistema: processamento e persistência de dados, geração de recomendações, autenticação de usuários e consumo de serviços externos com caching. A validação inclui:

* Comunicação entre serviços via base de dados compartilhada.
* Sincronização entre dados armazenados e dados exibidos ao usuário.
* Robustez frente a falhas externas (API fora do ar, dados corrompidos).
* Correção e eficiência do cache VHS.

&emsp; A cobertura abrange aproximadamente 70% dos fluxos mais importantes, superando o mínimo exigido. Isso garante que a aplicação funcione corretamente como um sistema coeso, mesmo quando seus módulos atuam de forma assíncrona ou distribuída.

## 8.5 Documentações automáticas

_conteúdo_
Obs.: Insira informações sobre quais frameworks foram usados e como acessar.

## 8.6 Runbook: Gerenciamento de Incidentes

### 8.6.1 Incidentes Comuns e Procedimentos

#### Falha de Banco de Dados
- **Detecção**: Alerta no Grafana (tempo de resposta > 500ms) ou erro de conexão
- **Sintomas**: APIs retornando erro 500, timeouts de conexão
- **Ações Imediatas**:
  1. Verificar logs no ELK Stack: `kubectl logs -f deployment/pati-api`
  2. Verificar status do PostgreSQL: `kubectl get pods -l app=postgresql`
  3. Reiniciar container do banco: `kubectl rollout restart deployment/postgresql`
  4. Se persistir, executar rollback: `pg_restore --dbname=pati_db backup.sql`
- **Escalação**: Se não resolvido em 15 minutos, acionar equipe de infraestrutura

#### Falha de API de Classificação
- **Detecção**: Taxa de erro > 5% no endpoint `/api/classification`
- **Sintomas**: Classificações não sendo processadas, dashboard sem dados
- **Ações Imediatas**:
  1. Verificar logs da aplicação: `kubectl logs -f deployment/pati-classification-api`
  2. Verificar recursos: `kubectl top pods`
  3. Reiniciar serviço: `kubectl rollout restart deployment/pati-classification-api`
  4. Verificar conectividade com ML model: testar endpoint `/health`
- **Rollback**: `kubectl rollout undo deployment/pati-classification-api`

#### Sobrecarga de Sistema
- **Detecção**: CPU > 80% ou memória > 85%
- **Sintomas**: Lentidão geral, timeouts
- **Ações Imediatas**:
  1. Escalar horizontalmente: `kubectl scale deployment/pati-api --replicas=5`
  2. Verificar queries lentas no banco: `SELECT * FROM pg_stat_activity WHERE state = 'active';`
  3. Implementar rate limiting temporário
  4. Analisar logs para identificar gargalos

#### Falha de Autenticação
- **Detecção**: Usuários não conseguem fazer login
- **Sintomas**: Erro 401/403 em massa
- **Ações Imediatas**:
  1. Verificar serviço de autenticação: `kubectl get pods -l app=auth-service`
  2. Verificar certificados JWT: validar expiração
  3. Reiniciar serviço de auth: `kubectl rollout restart deployment/auth-service`
  4. Verificar configurações de CORS

### 8.6.2 Procedimentos de Rollback

#### Rollback de Aplicação
```bash
# Verificar histórico de deployments
kubectl rollout history deployment/pati-api

# Rollback para versão anterior
kubectl rollout undo deployment/pati-api

# Rollback para versão específica
kubectl rollout undo deployment/pati-api --to-revision=2

# Verificar status do rollback
kubectl rollout status deployment/pati-api
```

#### Rollback de Banco de Dados
```sql
-- Backup antes de qualquer operação
pg_dump pati_db > backup_$(date +%Y%m%d_%H%M%S).sql

-- Restaurar backup específico
pg_restore --dbname=pati_db --clean --if-exists backup_20250624_143000.sql

-- Verificar integridade após restore
SELECT COUNT(*) FROM client;
SELECT COUNT(*) FROM investment;
```

### 8.6.3 Comunicação de Incidentes

#### Canais de Comunicação
- **Slack**: #pati-incidents (alertas automáticos)
- **Email**: <EMAIL> (incidentes críticos)
- **Status Page**: status.pati.btg.com (comunicação externa)

#### Template de Comunicação
```
🚨 INCIDENTE DETECTADO
Serviço: [Nome do Serviço]
Severidade: [Crítica/Alta/Média/Baixa]
Início: [Timestamp]
Impacto: [Descrição do impacto]
Ações: [Ações sendo tomadas]
ETA Resolução: [Estimativa]
```

# 9. Planejamento e Execução de Testes

## 9.1 Introdução

&ensp;Esta seção apresenta o planejamento de testes para a Plataforma de Adequação de Tipo de Investidor (PATI), integrando cinco abordagens complementares: testes funcionais, não-funcionais, usabilidade, APIs externas e integração.

&ensp;O planejamento foi estruturado para garantir a validação completa do sistema, verificando tanto o atendimento aos requisitos especificados quanto a qualidade da experiência do usuário. Cada tipo de teste possui objetivos específicos:

- **Testes Funcionais (RFs)**: Validam o comportamento das funcionalidades conforme os requisitos, incluindo login, dashboard, análise de clientes não conformes, visualização de carteiras e recomendações.

- **Testes Não-Funcionais (RNFs)**: Avaliam aspectos de desempenho, segurança, usabilidade e compatibilidade, garantindo que a plataforma atenda aos padrões de qualidade necessários.

- **Testes de Usabilidade**: Verificam a interface e experiência do usuário através de sessões com participantes reais, representando diferentes perfis de assessores de investimento.

- **Testes de APIs Externas**: Asseguram a comunicação eficiente entre a plataforma e serviços externos, validando a integração e o tratamento adequado de respostas.

- **Testes de Integração**: Validam as interações entre os componentes internos e externos, com atenção especial ao mecanismo de cache (VHS) para otimização de desempenho.

&ensp;Os testes funcionais e não-funcionais seguem uma estrutura que inclui pré-condições, cenários (positivos e negativos), passos de execução, resultados esperados e pós-condições. Os testes de usabilidade, APIs externas e integração são adaptados conforme suas particularidades metodológicas, mantendo o foco na validação sistemática dos requisitos. Os dados obtidos orientarão os ajustes e refinamentos necessários antes da finalização do MVP.

## 9.1 Testes Funcionais

&ensp;Os testes funcionais validam o comportamento das funcionalidades mais importantes do sistema, garantindo que cada requisito seja atendido conforme especificado. Estes testes abrangem desde o login seguro até a visualização de carteiras e recomendações, verificando tanto cenários positivos quanto negativos para assegurar a solidez da solução.

## 9.1.1 Planejamento

## RF01 - Login Seguro do Assessor

### Descrição
&ensp;O sistema deve permitir que o assessor de investimentos faça login utilizando credenciais seguras.

### Critério de Aceitação
&ensp;O assessor deve conseguir acessar a plataforma fornecendo um nome de usuário e senha válidos. Tentativas com credenciais inválidas devem ser bloqueadas, e o acesso deve ser direcionado ao dashboard principal após a autenticação bem-sucedida.

### Cenários de Teste
* **Positivo**: Realizar login com credenciais válidas de um assessor cadastrado.
* **Positivo Adicional**: Verificar se a sessão do usuário é mantida após o login (ex: navegando para outras páginas e voltando).
* **Positivo Adicional**: Verificar o comportamento do login com relação à sensibilidade a maiúsculas/minúsculas no campo de usuário/email (confirmar se é case-sensitive ou insensitive).
* **Negativo**: Tentar realizar login com senha incorreta para um usuário válido.
* **Negativo**: Tentar realizar login com um nome de usuário inexistente.
* **Negativo**: Tentar realizar login com campos de usuário e/ou senha vazios.
* **Negativo Adicional**: Verificar se há algum mecanismo de bloqueio após múltiplas tentativas falhas de login (dependendo dos requisitos de segurança definidos, como RNF16).

### Pré-condição
&ensp;Existência de um assessor de investimentos cadastrado no sistema com credenciais válidas (usuário e senha). A tela de login deve estar implementada e acessível.

### Procedimento
1. Acessar a página de login da plataforma Pati.
2. Inserir credenciais válidas (usuário e senha) nos campos correspondentes.
3. Clicar no botão de autenticação/login.
4. Verificar se o acesso é concedido e o usuário é redirecionado para o dashboard.
5. Repetir os passos 2-3 com credenciais inválidas (senha incorreta, usuário inexistente, campos vazios) e verificar as mensagens de erro e o bloqueio do acesso.
6. Realizar múltiplas tentativas falhas e verificar o comportamento do sistema (bloqueio).

### Pós-condição
&ensp;Para o cenário positivo, o assessor está autenticado na plataforma e tem acesso às funcionalidades correspondentes ao seu perfil. Para os cenários negativos, o acesso é negado e mensagens de erro apropriadas são exibidas, sem comprometer a segurança do sistema.

### Resultado Esperado
&ensp;Login bem-sucedido com credenciais válidas, redirecionando para a página principal. Falha na autenticação com mensagens de erro claras e informativas para credenciais inválidas ou campos vazios. Mecanismos de segurança (como bloqueio de tentativas) funcionam conforme especificado nos RNFs.

## RF02 - Visualização Restrita de Clientes

### Descrição
&ensp;O sistema deve garantir que o assessor visualize apenas dados dos clientes sob sua responsabilidade.

### Critério de Aceitação
&ensp;Um assessor logado só pode visualizar e interagir com a lista de clientes que estão formalmente associados a ele no sistema. Dados de clientes de outros assessores não devem ser acessíveis.

### Cenários de Teste
* **Positivo**: Assessor A logado visualiza a lista de clientes e confirma que apenas os clientes associados a ele (Cliente A1, Cliente A2) são exibidos.
* **Negativo**: Verificar se o Assessor A consegue visualizar dados de clientes não associados a ele tentando pesquisar um cliente de outro assessor na barra de pesquisa e verificando se ele aparece nos resultados.
* **Negativo Adicional**: Verificar o comportamento do sistema caso um assessor não tenha nenhum cliente associado (ex: exibir lista vazia com mensagem informativa).

### Pré-condição
&ensp;Assessor A logado na plataforma. Existem clientes cadastrados e associados a diferentes assessores (ex: Cliente A1, A2 associados ao Assessor A; Cliente B1 associado ao Assessor B).

### Procedimento
1. Com o Assessor A logado, navegar até a seção de visualização de clientes (dashboard ou lista de clientes).
2. Verificar se a lista exibida contém apenas os clientes A1 e A2.
3. Confirmar que o Cliente B1 não está presente na lista.
4. Pesquisar na barra de pesquisa o Cliente B1 pela conta do Assessor A.
5. (Se aplicável) Logar com um assessor sem clientes e verificar a interface.

### Pós-condição
&ensp;O assessor visualiza corretamente apenas os clientes sob sua gestão. Tentativas de acesso de visualizações de clientes de outros assessores não são bem sucedidas.

### Resultado Esperado
&ensp;A interface exibe exclusivamente os clientes associados ao assessor logado. O sistema impede o acesso a dados de clientes de outros assessores, garantindo a confidencialidade e o controle de acesso (conforme também RNF17).

## RF03 - Exibir Porcentagem de Clientes Não Conformes

### Descrição
&ensp;O sistema deve apresentar, após login, a porcentagem de clientes com inconformidade de perfil.

### Critério de Aceitação
&ensp;O dashboard inicial, exibido logo após o login bem-sucedido, deve mostrar corretamente a porcentagem exata de clientes cuja carteira está desalinhada com o perfil de risco cadastrado.

### Cenários de Teste
* **Positivo**: Verificar se a porcentagem de clientes não conformes é exibida corretamente no dashboard após o login.
* **Positivo Adicional**: Realizar uma ação que mude o status de conformidade de um cliente e verificar se a porcentagem no dashboard é atualizada.
* **Positivo Adicional**: Verificar o comportamento quando o assessor não possui clientes não conformes (0%).
* **Positivo Adicional**: Verificar o comportamento quando o assessor não possui nenhum cliente cadastrado.
* **Negativo**: Verificar se a porcentagem exibida no dashboard está incorreta (diferente da contagem real na base de dados).

### Pré-condição
&ensp;Assessor logado na plataforma. Base de dados contém clientes associados ao assessor com status de conformidade definidos.

### Procedimento
1. Realizar o login com o assessor.
2. Observar a porcentagem de clientes não conformes no dashboard inicial.
3. Comparar com o cálculo manual baseado nos dados reais da base.
4. Realizar uma ação que altere o status de conformidade de um cliente e verificar a atualização da porcentagem.
5. Testar com assessores em diferentes situações (sem clientes não conformes, sem clientes).

### Pós-condição
&ensp;O dashboard exibe a porcentagem de clientes não conformes de forma precisa e atualizada.

### Resultado Esperado
&ensp;A porcentagem de clientes não conformes exibida no dashboard corresponde exatamente à realidade dos dados. A atualização ocorre conforme esperado após mudanças no status dos clientes.

## RF04 - Listagem de Clientes com Inconformidades e Filtros

### Descrição
&ensp;O sistema deve listar os clientes com inconformidades e permitir filtrar por status, perfil e valor investido.

### Critério de Aceitação
&ensp;A lista deve mostrar todos os clientes do assessor que possuem inconformidade entre perfil declarado e carteira atual, com indicação visual clara do status e informações básicas de cada cliente. O sistema deve permitir a aplicação de filtros para refinar a visualização dos clientes.

### Cenários de Teste
* **Positivo**: Verificar se a lista exibe corretamente todos os clientes com inconformidade associados ao assessor logado.
* **Positivo Adicional**: Confirmar que a lista é atualizada quando o status de conformidade de um cliente é alterado.
* **Positivo Adicional**: Verificar se a lista exibe corretamente as informações básicas de cada cliente (nome, perfil, valor investido, etc.).
* **Positivo Adicional**: Verificar se a indicação visual de status (conforme/não conforme) está clara e consistente.
* **Positivo**: Filtrar por status "Conforme" e verificar se apenas clientes conformes são exibidos.
* **Positivo**: Filtrar por status "Inconforme" e verificar se apenas clientes não conformes são exibidos.
* **Positivo**: Filtrar por perfil "Conservador" e verificar se apenas clientes com esse perfil são exibidos.
* **Positivo**: Filtrar por perfil "Moderado" e verificar se apenas clientes com esse perfil são exibidos.
* **Positivo**: Filtrar por perfil "Arrojado" e verificar se apenas clientes com esse perfil são exibidos.
* **Positivo**: Ordenar por investimento total "Maior para Menor" e verificar se a lista é exibida na ordem correta.
* **Positivo**: Ordenar por investimento total "Menor para Maior" e verificar se a lista é exibida na ordem correta.
* **Positivo Adicional**: Combinar múltiplos critérios de filtro (ex: status "Inconforme" + perfil "Conservador") e verificar se a interseção dos resultados é correta.
* **Positivo Adicional**: Verificar se o botão "Limpar" remove todos os filtros aplicados e restaura a lista completa de clientes.
* **Negativo**: Verificar se a lista não exibe clientes que estão em conformidade quando filtrado por "Inconforme".
* **Negativo Adicional**: Verificar o comportamento quando não há clientes não conformes (lista vazia com mensagem informativa).
* **Negativo**: Aplicar filtros que não correspondem a nenhum cliente e verificar se uma mensagem informativa adequada é exibida.
* **Negativo Adicional**: Verificar se a aplicação mantém a estabilidade e fornece feedback adequado quando todos os filtros são aplicados simultaneamente e nenhum cliente corresponde a todos os critérios.

### Pré-condição
&ensp;Assessor logado na plataforma. Existem clientes cadastrados com diferentes status de conformidade, perfis e valores investidos.

### Procedimento
1. Navegar até a seção de listagem de clientes.
2. Verificar se todos os clientes com inconformidade são exibidos.
3. Confirmar que clientes em conformidade não aparecem na lista quando filtrado por "Inconforme".
4. Verificar as informações exibidas para cada cliente.
5. Alterar o status de conformidade de um cliente e verificar a atualização da lista.
6. Acessar a seção de filtros.
7. Aplicar filtro de status (Conforme ou Inconforme) e verificar os resultados.
8. Aplicar filtro de perfil (Conservador, Moderado ou Arrojado) e verificar os resultados.
9. Aplicar ordenação por investimento total (Maior para Menor ou Menor para Maior) e verificar os resultados.
10. Combinar diferentes critérios de filtro e verificar a precisão dos resultados.
11. Utilizar o botão "Limpar" e confirmar que todos os filtros são removidos.
12. Testar filtros que resultam em conjunto vazio e verificar a mensagem exibida.
13. Testar o cenário sem clientes não conformes.

### Pós-condição
&ensp;O sistema exibe a lista de clientes conforme os critérios de filtro aplicados, ou a lista completa após limpar os filtros.

### Resultado Esperado
&ensp;A lista apresenta de forma clara e completa todos os clientes conforme os filtros aplicados, facilitando a identificação e priorização pelo assessor. A interface responde corretamente a mudanças de status, aplicação de filtros e situações especiais (sem clientes não conformes ou sem resultados para os filtros aplicados).

## RF05 - Análise Automática da Carteira

### Descrição
&ensp;O sistema deve analisar automaticamente a carteira de cada cliente.

### Critério de Aceitação
&ensp;O sistema deve processar os dados da carteira de cada cliente, identificando a composição de ativos, valores investidos e classes de investimento, para determinar o perfil real de investimento.

### Cenários de Teste
* **Positivo**: Verificar se o sistema analisa corretamente a carteira de um cliente com diversos tipos de investimentos.
* **Positivo Adicional**: Confirmar que a análise é atualizada quando há mudanças na composição da carteira.
* **Positivo Adicional**: Verificar se o sistema processa corretamente diferentes classes de ativos (renda fixa, renda variável, etc.).
* **Negativo**: Verificar o comportamento quando a carteira contém dados incompletos ou inconsistentes.
* **Negativo Adicional**: Verificar o comportamento quando o cliente não possui investimentos.

### Pré-condição
&ensp;Assessor logado na plataforma. Existem clientes cadastrados com carteiras de investimento definidas.

### Procedimento
1. Acessar os detalhes de um cliente com carteira diversificada.
2. Verificar se o sistema apresenta a análise completa da carteira.
3. Modificar a composição da carteira e verificar a atualização da análise.
4. Testar com carteiras contendo diferentes classes de ativos.
5. Verificar o comportamento com dados incompletos ou cliente sem investimentos.

### Pós-condição
&ensp;O sistema apresenta uma análise precisa e atualizada da carteira de cada cliente.

### Resultado Esperado
&ensp;A análise automática identifica corretamente a composição da carteira, processando diferentes tipos de ativos e respondendo adequadamente a mudanças ou situações especiais.

## RF06 - Comparação de Perfil Cadastrado vs. Perfil Real

### Descrição
&ensp;O sistema deve comparar o perfil cadastrado do cliente com o perfil real calculado com base na composição atual da carteira e sinalizar os clientes cuja carteira esteja fora do perfil declarado.

### Critério de Aceitação
&ensp;O sistema deve calcular o perfil real do cliente com base na análise da carteira, comparar com o perfil declarado/cadastrado, e sinalizar claramente quando há inconformidade.

### Cenários de Teste
* **Positivo**: Verificar se o sistema identifica corretamente a inconformidade quando o perfil real difere do perfil cadastrado.
* **Positivo Adicional**: Confirmar que o sistema reconhece quando o perfil real está alinhado ao perfil cadastrado.
* **Positivo Adicional**: Verificar se a sinalização de inconformidade é clara e visível.
* **Positivo Adicional**: Verificar se o sistema atualiza o status de conformidade quando há mudanças na carteira ou no perfil cadastrado.
* **Negativo**: Verificar o comportamento quando não é possível determinar o perfil real devido a dados insuficientes.

### Pré-condição
&ensp;Assessor logado na plataforma. Existem clientes cadastrados com perfis declarados e carteiras definidas.

### Procedimento
1. Acessar os detalhes de um cliente com inconformidade entre perfil real e cadastrado.
2. Verificar se o sistema sinaliza corretamente a inconformidade.
3. Acessar os detalhes de um cliente com perfil real alinhado ao cadastrado.
4. Verificar se o sistema indica corretamente a conformidade.
5. Modificar a carteira ou o perfil cadastrado e verificar a atualização do status.
6. Testar com dados insuficientes para determinar o perfil real.

### Pós-condição
&ensp;O sistema identifica e sinaliza corretamente as inconformidades entre perfil cadastrado e perfil real.

### Resultado Esperado
&ensp;A comparação entre perfis é precisa e a sinalização de inconformidade é clara, permitindo ao assessor identificar facilmente os clientes que necessitam de atenção.

## RF07 - Visualização de Perfil Cadastrado vs. Perfil Real

### Descrição
&ensp;O sistema deve permitir ao assessor clicar em um cliente específico para visualizar o perfil cadastrado versus perfil real.

### Critério de Aceitação
&ensp;Ao selecionar um cliente, o sistema deve exibir uma comparação clara entre o perfil declarado/cadastrado e o perfil real calculado com base na carteira atual.

### Cenários de Teste
* **Positivo**: Verificar se ao clicar em um cliente, o sistema exibe corretamente a comparação entre perfil cadastrado e perfil real.
* **Positivo Adicional**: Confirmar que a visualização inclui detalhes sobre os critérios utilizados para determinar o perfil real.
* **Positivo Adicional**: Verificar se a interface destaca claramente as diferenças entre os perfis quando há inconformidade.
* **Positivo Adicional**: Verificar se as informações do cabeçalho (nome, perfil, valor total investido, data do último contato e status de conformidade) são exibidas corretamente.
* **Negativo**: Verificar o comportamento quando não é possível determinar o perfil real devido a dados insuficientes.
* **Negativo Adicional**: Verificar o comportamento ao tentar acessar detalhes de um cliente que não existe ou foi removido.

### Pré-condição
&ensp;Assessor logado na plataforma. Cliente selecionado possui perfil cadastrado e carteira definida.

### Procedimento
1. A partir da lista de clientes, selecionar um cliente específico.
2. Verificar se a comparação entre perfil cadastrado e perfil real é exibida.
3. Confirmar que os detalhes sobre a determinação do perfil real estão presentes.
4. Verificar se as diferenças entre perfis são destacadas quando há inconformidade.
5. Confirmar que as informações do cabeçalho estão corretas e completas.
6. Testar com um cliente que possui dados insuficientes para determinar o perfil real.
7. Tentar acessar detalhes de um cliente inexistente ou removido.

### Pós-condição
&ensp;O assessor visualiza claramente a comparação entre o perfil cadastrado e o perfil real do cliente selecionado.

### Resultado Esperado
&ensp;A interface apresenta de forma clara e detalhada a comparação entre os perfis, destacando as diferenças quando há inconformidade e fornecendo informações suficientes para o assessor compreender a situação do cliente.

## RF08 - Recomendações para Realinhamento da Carteira

### Descrição
&ensp;O sistema deve gerar recomendações para realinhamento da carteira, apresentando novas oportunidades de ativos alinhados ao perfil do cliente.

### Critério de Aceitação
&ensp;Para clientes não conformes, o sistema deve gerar e exibir sugestões específicas de investimentos que ajudariam a alinhar a carteira ao perfil de risco, focando em novas oportunidades de investimento.

### Cenários de Teste
* **Positivo**: Verificar se o sistema gera recomendações relevantes para um cliente não conforme.
* **Positivo Adicional**: Verificar se as recomendações são específicas para o perfil e situação atual do cliente.
* **Positivo Adicional**: Verificar se o sistema indica o impacto esperado das recomendações na conformidade da carteira.
* **Negativo**: Verificar o comportamento quando não é possível gerar recomendações adequadas.
* **Negativo Adicional**: Verificar se o sistema não gera recomendações desnecessárias para clientes já conformes.

### Pré-condição
&ensp;Assessor logado na plataforma. Cliente selecionado possui status não conforme.

### Procedimento
1. Acessar a página de detalhes de um cliente não conforme.
2. Navegar para a seção de recomendações.
3. Verificar se as recomendações são geradas e exibidas.
4. Verificar a relevância e especificidade das recomendações para o perfil do cliente.
5. Confirmar se o impacto esperado na conformidade é indicado.
6. Testar o comportamento com clientes em diferentes situações (já conformes, sem possibilidade de recomendações).

### Pós-condição
&ensp;O assessor visualiza recomendações úteis e específicas para adequar a carteira do cliente.

### Resultado Esperado
&ensp;O sistema apresenta recomendações personalizadas e eficazes para ajudar o assessor a realinhar a carteira do cliente ao seu perfil de risco. As recomendações são apresentadas de forma clara, com informações suficientes para tomada de decisão.

## RF09 - Visualização da Composição da Carteira

### Descrição
&ensp;O sistema deve permitir ao assessor visualizar a composição atual da carteira, com destaque para ativos desalinhados ao perfil.

### Critério de Aceitação
&ensp;A visualização da carteira deve mostrar cada investimento do cliente, com informações como nome do produto, valor investido, quantidade, classe de investimento e status de conformidade individual, destacando visualmente os ativos desalinhados ao perfil.

### Cenários de Teste
* **Positivo**: Verificar se todos os investimentos do cliente são exibidos corretamente na visualização da carteira.
* **Positivo Adicional**: Confirmar que as informações de cada investimento estão completas e precisas.
* **Positivo Adicional**: Verificar se os ativos desalinhados ao perfil são destacados visualmente.
* **Positivo Adicional**: Verificar se o valor total da carteira é calculado e exibido corretamente.
* **Negativo**: Verificar o comportamento quando o cliente não possui investimentos.
* **Negativo Adicional**: Verificar se investimentos removidos ou liquidados não aparecem na carteira ativa.

### Pré-condição
&ensp;Assessor logado na plataforma. Cliente selecionado possui investimentos cadastrados.

### Procedimento
1. Acessar a página de detalhes de um cliente.
2. Navegar para a seção de carteira de investimentos.
3. Verificar se todos os investimentos estão listados com suas informações completas.
4. Confirmar que os ativos desalinhados ao perfil estão visualmente destacados.
5. Verificar a precisão dos valores e cálculos, incluindo o valor total da carteira.
6. Testar o cenário de cliente sem investimentos.
7. Verificar se investimentos removidos ou liquidados não aparecem na carteira ativa.

### Pós-condição
&ensp;O assessor visualiza a composição completa e atualizada da carteira do cliente, com destaque para os ativos desalinhados.

### Resultado Esperado
&ensp;A carteira é apresentada de forma detalhada e organizada, permitindo ao assessor analisar facilmente a composição dos investimentos e identificar itens não conformes. A interface lida adequadamente com casos especiais (sem investimentos, investimentos removidos).

## RF10 - Atualização Periódica dos Dados

### Descrição
&ensp;O sistema deve atualizar periodicamente os dados de clientes e carteiras.

### Critério de Aceitação
&ensp;O sistema deve realizar atualizações automáticas dos dados em intervalos regulares, garantindo que as informações de clientes e carteiras estejam sempre atualizadas sem necessidade de intervenção manual.

### Cenários de Teste
* **Positivo**: Verificar se o sistema atualiza automaticamente os dados em intervalos regulares conforme configurado.
* **Positivo Adicional**: Confirmar que as atualizações incluem tanto dados de clientes quanto de carteiras.
* **Positivo Adicional**: Verificar se o sistema mantém um registro das atualizações realizadas.
* **Negativo**: Verificar o comportamento quando a atualização periódica falha (ex: fonte de dados indisponível).
* **Negativo Adicional**: Verificar se o sistema possui mecanismo de recuperação para atualizações falhas.

### Pré-condição
&ensp;Sistema configurado para realizar atualizações periódicas. Fontes de dados disponíveis para atualização.

### Procedimento
1. Configurar o sistema para realizar atualizações em intervalos curtos para fins de teste.
2. Monitorar o sistema durante o período de atualização programada.
3. Verificar se os dados são atualizados conforme esperado.
4. Confirmar que tanto dados de clientes quanto de carteiras são atualizados.
5. Verificar o registro de atualizações.
6. Simular uma falha na fonte de dados e observar o comportamento do sistema.
7. Verificar se o sistema tenta recuperar-se de falhas de atualização.

### Pós-condição
&ensp;O sistema mantém os dados atualizados conforme a periodicidade configurada.

### Resultado Esperado
&ensp;As atualizações periódicas ocorrem conforme programado, mantendo os dados de clientes e carteiras atualizados. O sistema lida adequadamente com falhas e mantém um registro das atualizações realizadas.

## RF11 - Importação de Dados

### Descrição
&ensp;O sistema deve permitir a importação de dados via arquivos (.csv, .xlsx) ou via API REST no esquema designado na documentação.

### Critério de Aceitação
&ensp;O sistema deve aceitar e processar corretamente dados importados de diferentes fontes, validando o formato e a integridade dos dados antes de incorporá-los à base de dados.

### Cenários de Teste
* **Positivo**: Verificar se o sistema importa corretamente dados de um arquivo CSV válido.
* **Positivo Adicional**: Confirmar que o sistema importa corretamente dados de um arquivo XLSX válido.
* **Positivo Adicional**: Verificar se o sistema processa corretamente dados recebidos via API REST no formato especificado.
* **Positivo Adicional**: Verificar se o sistema valida os dados importados antes de incorporá-los à base de dados.
* **Positivo Adicional**: Verificar se o sistema fornece feedback sobre o resultado da importação (quantidade de registros processados, erros encontrados).
* **Negativo**: Verificar o comportamento quando um arquivo com formato inválido é fornecido.
* **Negativo Adicional**: Verificar o comportamento quando os dados importados contêm erros ou inconsistências.
* **Negativo Adicional**: Verificar o comportamento quando a API REST recebe dados em formato diferente do especificado.
* **Negativo Adicional**: Verificar se o sistema impede a importação de dados duplicados ou que possam comprometer a integridade da base.

### Pré-condição
&ensp;Sistema configurado para aceitar importações. Arquivos de teste (CSV, XLSX) preparados com dados válidos e inválidos. API REST configurada para testes.

### Procedimento
1. Acessar a funcionalidade de importação de dados.
2. Importar um arquivo CSV válido e verificar o processamento.
3. Importar um arquivo XLSX válido e verificar o processamento.
4. Enviar dados válidos via API REST e verificar o processamento.
5. Verificar se o sistema valida os dados antes da importação.
6. Confirmar que o sistema fornece feedback adequado sobre o resultado da importação.
7. Tentar importar arquivos com formato inválido e observar o comportamento.
8. Tentar importar dados com erros ou inconsistências e verificar o tratamento.
9. Enviar dados em formato incorreto via API REST e observar o comportamento.
10. Tentar importar dados duplicados e verificar se o sistema impede ou trata adequadamente.

### Pós-condição
&ensp;O sistema processa corretamente os dados válidos importados e rejeita ou trata adequadamente dados inválidos.

### Resultado Esperado
&ensp;A importação de dados funciona corretamente para as diferentes fontes suportadas (CSV, XLSX, API REST). O sistema valida os dados, fornece feedback adequado e protege a integridade da base de dados durante o processo de importação.

&ensp;**Nota:** Os requisitos foram reorganizados, removendo o antigo RF11 (Atualização Sob Demanda) e o antigo RF13 (Registro de Logs). A funcionalidade de filtros foi incorporada ao RF04 para refletir a implementação atual da interface. O antigo RF12 (Importação de Dados) foi mantido como RF11 na lista reorganizada. Veja a [seção 3.1 Requisitos Funcionais (RFs)](#31-requisitos-funcionais-rfs) para mais detalhes.

## 9.1.2 Resultados

_conteúdo_

## 9.2 Testes de RNFs

Os testes não-funcionais avaliam aspectos críticos de qualidade como desempenho, segurança, usabilidade e compatibilidade do sistema. Estes testes garantem que, além de funcionar corretamente, a aplicação atenda aos padrões de qualidade necessários para uma experiência satisfatória e proteção adequada dos dados sensíveis.

## 9.2.1 Planejamento

&ensp;**Observação sobre Ferramentas:** As ferramentas listadas em cada requisito são apenas sugestões ou opções que podem ser utilizadas para realizar os testes. A equipe tem total liberdade para escolher quais ferramentas utilizar, incluindo outras que não foram mencionadas no planejamento, de acordo com a disponibilidade, familiaridade da equipe e adequação ao contexto específico de cada teste.

## 9.2.2 Tabela de Testes dos Requisitos Não Funcionais

### RNF01 - Compatibilidade com Dispositivos Móveis
**Descrição:**  
&ensp;O sistema deve ser compatível com Android 10+ e iOS 13+.

**Critério de Aceitação:**  
&ensp;O sistema deve funcionar corretamente em dispositivos com Android 10 ou superior e iOS 13 ou superior, mantendo todas as funcionalidades e apresentando interface adequada aos diferentes tamanhos de tela.

**Cenários de Teste:**  
- **Positivo:** O sistema carrega e funciona corretamente em dispositivos Android 10+ e iOS 13+.
- **Positivo Adicional:** A interface se adapta adequadamente a diferentes tamanhos de tela (smartphones e tablets).
- **Positivo Adicional:** Todas as funcionalidades principais estão acessíveis e operacionais em ambas as plataformas.
- **Negativo:** O sistema apresenta falhas ou comportamento inconsistente em versões específicas do Android ou iOS.
- **Negativo Adicional:** Elementos da interface ficam desalinhados ou inacessíveis em determinados dispositivos.

**Pré-condição:**  
&ensp;Dispositivos de teste com Android 10+ e iOS 13+ disponíveis.
&ensp;Aplicação instalada e configurada nos dispositivos de teste.
&ensp;Conexão de internet estável para os dispositivos.

**Procedimento:**  
1. Instalar a aplicação em diferentes dispositivos com Android 10+ e iOS 13+.
2. Navegar por todas as telas principais da aplicação em cada dispositivo.
3. Testar todas as funcionalidades críticas (login, visualização de clientes, filtros, etc.).
4. Verificar a adaptação da interface em diferentes tamanhos de tela.
5. Testar a aplicação em orientação retrato e paisagem.
6. Verificar o comportamento com diferentes resoluções de tela.

**Pós-condição:**  
&ensp;A aplicação funciona corretamente em todos os dispositivos testados.
&ensp;Todas as funcionalidades estão acessíveis e operacionais.
&ensp;A interface se adapta adequadamente a diferentes tamanhos e orientações de tela.

**Ferramentas:**  
&ensp;BrowserStack, Firebase Test Lab, Dispositivos físicos para teste

**Resultado Esperado:**  
&ensp;O sistema funciona de forma consistente em todos os dispositivos e versões de sistema operacional especificados, mantendo todas as funcionalidades e apresentando interface adequada aos diferentes tamanhos de tela.

### RNF02 - Disponibilidade (Teste Simplificado para Um Dia)
**Descrição:**  
&ensp;O sistema deve ter disponibilidade mínima de 99,5% em horário comercial.

**Critério de Aceitação Simplificado:**  
&ensp;O sistema deve permanecer acessível durante o dia de teste, com verificações periódicas simples.

**Cenários de Teste:**  
- **Positivo:** Verificar se o sistema está acessível em intervalos regulares durante o dia.
- **Negativo:** Simular uma interrupção breve e verificar se o sistema se recupera.

**Pré-condição:**  
&ensp;Sistema em ambiente de teste ou homologação.
&ensp;Ferramenta básica de monitoramento configurada.

**Procedimento:**  
1. Configurar verificações de disponibilidade a cada hora durante um dia de trabalho (8h às 17h).
2. Registrar se o sistema está acessível em cada verificação.
3. Em um momento planejado, reiniciar um serviço para simular uma breve interrupção.
4. Verificar quanto tempo o sistema leva para voltar a funcionar.
5. Calcular a porcentagem de verificações bem-sucedidas ao final do dia.

**Pós-condição:**  
&ensp;Registro simples de disponibilidade do dia.
&ensp;Tempo de recuperação após a interrupção documentado.

**Ferramentas:**  
&ensp;Ferramenta simples de monitoramento (como UptimeRobot gratuito)
&ensp;Cronômetro para medir tempo de recuperação

**Resultado Esperado:**  
&ensp;O sistema está disponível em pelo menos 90% das verificações do dia.
&ensp;O sistema se recupera em menos de 5 minutos após a interrupção simulada.

**Observação:**  
&ensp;Este teste simplificado oferece apenas uma verificação básica inicial. Para validação completa do requisito de 99,5% de disponibilidade mensal, será necessário um monitoramento contínuo por período mais longo.

### RNF03 - Acurácia do Algoritmo de Recomendação
**Descrição:**  
&ensp;O sistema deve manter no mínimo 70% de acurácia no algoritmo de recomendação.

**Critério de Aceitação:**  
&ensp;O algoritmo de recomendação deve apresentar uma taxa de acurácia de pelo menos 70%, medida pela proporção de recomendações consideradas adequadas por especialistas em investimentos.

**Cenários de Teste:**  
- **Positivo:** O algoritmo de recomendação atinge pelo menos 70% de acurácia em um conjunto de teste.
- **Positivo Adicional:** O algoritmo mantém a acurácia mesmo com diferentes perfis de investidores.
- **Positivo Adicional:** As recomendações são consistentes com as melhores práticas do mercado financeiro.
- **Negativo:** A acurácia do algoritmo cai abaixo de 70% em determinados cenários.
- **Negativo Adicional:** O algoritmo gera recomendações inconsistentes para perfis similares.
- **Negativo Adicional:** As recomendações não se adaptam a mudanças nas condições de mercado.

**Pré-condição:**  
&ensp;Conjunto de dados de teste preparado com casos conhecidos.
&ensp;Especialistas em investimentos disponíveis para avaliar as recomendações.
&ensp;Métricas de avaliação definidas e implementadas.

**Procedimento:**  
1. Preparar conjunto de dados de teste com diferentes perfis de investidores e carteiras.
2. Executar o algoritmo de recomendação no conjunto de teste.
3. Submeter as recomendações geradas para avaliação por especialistas em investimentos.
4. Calcular a acurácia com base nas avaliações dos especialistas.
5. Testar o algoritmo com diferentes condições de mercado simuladas.
6. Verificar a consistência das recomendações para perfis similares.
7. Analisar casos de falha para identificar padrões e oportunidades de melhoria.

**Pós-condição:**  
&ensp;A acurácia do algoritmo é medida e documentada.
&ensp;Pontos de melhoria são identificados e priorizados.
&ensp;O algoritmo é ajustado conforme necessário para manter a acurácia mínima.

**Ferramentas:**  
&ensp;Jupyter Notebook, Scikit-learn, TensorFlow, Planilhas para avaliação de especialistas

**Resultado Esperado:**  
&ensp;O algoritmo de recomendação mantém acurácia de pelo menos 70% em diferentes cenários, gerando recomendações consistentes e alinhadas com as melhores práticas do mercado financeiro.

### RNF04 - Tempo de Resposta
**Descrição:**  
&ensp;O sistema deve garantir tempo de resposta inferior a 2 segundos para consultas padrão em condições favoráveis.

**Critério de Aceitação:**  
&ensp;As consultas padrão (visualização de dashboard, listagem de clientes, detalhes de cliente) devem ser completadas em menos de 2 segundos em condições favoráveis de rede e servidor.

**Cenários de Teste:**  
- **Positivo:** Todas as consultas padrão são completadas em menos de 2 segundos.
- **Positivo Adicional:** O tempo médio de resposta para consultas padrão é inferior a 1 segundo.
- **Positivo Adicional:** O sistema mantém tempos de resposta estáveis mesmo com aumento moderado de carga.
- **Negativo:** Algumas consultas padrão excedem o limite de 2 segundos.
- **Negativo Adicional:** O tempo de resposta degrada significativamente com o aumento do número de usuários.
- **Negativo Adicional:** Consultas complexas resultam em tempos de resposta inaceitáveis.

**Pré-condição:**  
&ensp;Sistema em ambiente de teste com configuração similar à produção.
&ensp;Ferramentas de medição de desempenho configuradas.
&ensp;Conjunto de consultas padrão definido para teste.

**Procedimento:**  
1. Identificar e documentar as consultas padrão a serem testadas.
2. Configurar ferramentas para medir o tempo de resposta de cada consulta.
3. Executar cada consulta padrão múltiplas vezes em condições favoráveis.
4. Calcular o tempo médio, mínimo e máximo de resposta para cada consulta.
5. Simular aumento gradual de carga e medir o impacto no tempo de resposta.
6. Identificar consultas que se aproximam ou excedem o limite de 2 segundos.
7. Analisar gargalos de desempenho e oportunidades de otimização.

**Pós-condição:**  
&ensp;Tempos de resposta são documentados para todas as consultas padrão.
&ensp;Gargalos de desempenho são identificados e priorizados para otimização.
&ensp;Plano de otimização é desenvolvido para consultas que se aproximam do limite.

**Ferramentas:**  
&ensp;JMeter, Gatling, Chrome DevTools, New Relic

**Resultado Esperado:**  
&ensp;Todas as consultas padrão são completadas em menos de 2 segundos em condições favoráveis, com tempo médio de resposta inferior a 1 segundo e estabilidade mesmo com aumento moderado de carga.

### RNF05 - Usuários Simultâneos
**Descrição:**  
&ensp;O sistema deve suportar ao menos 500 usuários simultâneos sem perda perceptível de desempenho.

**Critério de Aceitação:**  
&ensp;O sistema deve manter tempos de resposta aceitáveis (conforme RNF05) mesmo com 500 usuários realizando operações simultaneamente.

**Cenários de Teste:**  
- **Positivo:** O sistema mantém desempenho aceitável com 500 usuários simultâneos.
- **Positivo Adicional:** O sistema escala adequadamente com o aumento gradual de usuários.
- **Positivo Adicional:** Não há falhas ou erros mesmo com carga máxima.
- **Negativo:** O desempenho degrada significativamente antes de atingir 500 usuários.
- **Negativo Adicional:** Ocorrem erros ou falhas quando o sistema está sob carga máxima.
- **Negativo Adicional:** Alguns usuários experimentam tempos de resposta inaceitáveis durante picos de uso.

**Pré-condição:**  
&ensp;Ambiente de teste configurado para simular múltiplos usuários.
&ensp;Ferramentas de teste de carga configuradas.
&ensp;Cenários de uso típicos definidos para simulação.

**Procedimento:**  
1. Definir cenários de uso típicos para simulação (login, visualização de dashboard, consulta de clientes, etc.).
2. Configurar ferramentas de teste de carga para simular usuários simultâneos.
3. Iniciar com baixo número de usuários e aumentar gradualmente até 500.
4. Monitorar tempos de resposta, uso de recursos (CPU, memória, rede) e taxas de erro.
5. Manter carga máxima por período prolongado (30+ minutos) para verificar estabilidade.
6. Analisar logs e métricas para identificar gargalos e pontos de falha.
7. Verificar comportamento do sistema após redução da carga.

**Pós-condição:**  
&ensp;Capacidade do sistema é documentada com base nos resultados dos testes.
&ensp;Gargalos e limitações são identificados e priorizados para otimização.
&ensp;Plano de escalabilidade é desenvolvido para suportar crescimento futuro.

**Ferramentas:**  
&ensp;JMeter, Locust, K6, AWS Load Testing

**Resultado Esperado:**  
&ensp;O sistema mantém desempenho aceitável com 500 usuários simultâneos, sem erros ou falhas significativas, e com tempos de resposta dentro dos limites definidos no RNF05.

### RNF06 - Tempo de Upload
**Descrição:**  
&ensp;O sistema deve concluir o upload de arquivos CSV ou XLSX no formato padrão com 10 mil registros em até 15 segundos.

**Critério de Aceitação:**  
&ensp;O upload e processamento inicial de arquivos CSV ou XLSX contendo 10.000 registros no formato padrão deve ser concluído em no máximo 15 segundos em condições favoráveis de rede.

**Cenários de Teste:**  
- **Positivo:** Upload e processamento inicial de arquivo com 10.000 registros é concluído em menos de 15 segundos.
- **Positivo Adicional:** O sistema fornece feedback adequado sobre o progresso do upload.
- **Positivo Adicional:** Arquivos maiores são processados em tempo proporcional e previsível.
- **Negativo:** O tempo de upload e processamento excede 15 segundos para 10.000 registros.
- **Negativo Adicional:** O sistema falha ao processar arquivos grandes ou complexos.
- **Negativo Adicional:** Não há feedback adequado durante uploads longos.

**Pré-condição:**  
&ensp;Arquivos de teste (CSV e XLSX) preparados com 10.000 registros no formato padrão.
&ensp;Conexão de rede estável e de alta velocidade disponível.
&ensp;Funcionalidade de upload implementada e operacional.

**Procedimento:**  
1. Preparar arquivos de teste CSV e XLSX com 10.000 registros no formato padrão.
2. Configurar ferramentas para medir o tempo de upload e processamento.
3. Realizar uploads dos arquivos de teste em condições favoráveis de rede.
4. Medir o tempo desde o início do upload até a conclusão do processamento inicial.
5. Verificar se o sistema fornece feedback adequado durante o processo.
6. Testar com arquivos de diferentes tamanhos para verificar escalabilidade.
7. Analisar logs e métricas para identificar gargalos no processo.

**Pós-condição:**  
&ensp;Tempos de upload e processamento são documentados para diferentes tipos e tamanhos de arquivo.
&ensp;Gargalos são identificados e priorizados para otimização.
&ensp;Feedback ao usuário durante o processo é avaliado e melhorado se necessário.

**Ferramentas:**  
&ensp;Chrome DevTools, Postman, JMeter, Ferramentas de monitoramento de rede

**Resultado Esperado:**  
&ensp;O sistema conclui o upload e processamento inicial de arquivos CSV ou XLSX com 10.000 registros em menos de 15 segundos, fornecendo feedback adequado durante o processo e escalando de forma previsível para arquivos maiores.

### RNF07 - Tempo de Reanálise
**Descrição:**  
&ensp;O sistema deve realizar reanálise de recomendação em até 10 segundos.

**Critério de Aceitação:**  
&ensp;Quando uma reanálise de recomendação é solicitada pela equipe técnica, o sistema deve processar e exibir os resultados em no máximo 10 segundos.

**Cenários de Teste:**  
- **Positivo:** Verificar se o sistema realiza a reanálise dentro do limite de 10 segundos quando solicitado pela equipe técnica.
- **Positivo Adicional:** Verificar se o tempo de reanálise permanece consistente após múltiplas solicitações consecutivas.
- **Positivo Adicional:** Verificar se o tempo de reanálise é aceitável para carteiras de diferentes tamanhos e complexidades.
- **Negativo:** Verificar o comportamento do sistema quando o tempo de reanálise excede 10 segundos.
- **Negativo Adicional:** Verificar se o sistema fornece feedback adequado durante o processamento da reanálise.

**Pré-condição:**  
&ensp;Ambiente de teste configurado com acesso ao backend do sistema.
&ensp;Existência de carteiras de clientes cadastradas no sistema para teste.
&ensp;Ferramentas de medição de tempo configuradas.
&ensp;Acesso às APIs ou endpoints que permitem solicitar reanálise diretamente.

**Procedimento:**  
1. Acessar o ambiente de teste com credenciais da equipe técnica.
2. Selecionar uma carteira de cliente para reanálise.
3. Iniciar a medição de tempo.
4. Acionar o endpoint ou API de reanálise de recomendação.
5. Registrar o tempo decorrido até a exibição dos resultados.
6. Repetir o teste com diferentes carteiras (pequenas, médias e grandes).
7. Realizar múltiplas solicitações consecutivas para verificar consistência.
8. Documentar os resultados de cada teste.

**Pós-condição:**  
&ensp;Tempos de reanálise registrados para diferentes cenários.
&ensp;Comportamento do sistema documentado para casos de sucesso e falha.

**Ferramentas:**  
&ensp;Postman ou ferramenta similar para chamadas de API
&ensp;Cronômetro de precisão ou ferramentas de profiling
&ensp;Ferramentas de monitoramento de desempenho do servidor

**Resultado Esperado:**  
&ensp;O sistema realiza a reanálise de recomendação em menos de 10 segundos em todos os cenários testados.
&ensp;O tempo de resposta permanece consistente após múltiplas solicitações.
&ensp;O sistema fornece feedback adequado durante o processamento.
&ensp;Em caso de atraso, o sistema notifica apropriadamente.

### RNF08 - Modularidade do Código
**Descrição:**  
&ensp;O sistema deve ter código modular para facilitar manutenção e evolução.

**Critério de Aceitação:**  
&ensp;O código do sistema deve ser organizado em módulos coesos e com baixo acoplamento, seguindo princípios de design como SOLID, facilitando manutenção, testes e evolução.

**Cenários de Teste:**  
- **Positivo:** Módulos podem ser modificados independentemente sem afetar outras partes do sistema.
- **Positivo Adicional:** Novos recursos podem ser adicionados com mínimo impacto em código existente.
- **Positivo Adicional:** Testes automatizados podem ser executados isoladamente para cada módulo.
- **Negativo:** Modificações em um módulo causam efeitos colaterais em outras partes do sistema.
- **Negativo Adicional:** Alta complexidade ciclomática e acoplamento entre módulos.
- **Negativo Adicional:** Dificuldade em isolar componentes para testes.

**Pré-condição:**  
&ensp;Código-fonte completo disponível para análise.
&ensp;Ferramentas de análise estática de código configuradas.
&ensp;Documentação de arquitetura e design disponível.

**Procedimento:**  
1. Realizar análise estática do código para medir métricas de qualidade (complexidade, acoplamento, coesão).
2. Revisar a arquitetura do sistema para verificar separação de responsabilidades.
3. Selecionar módulos para modificação controlada e verificar impacto em outras partes do sistema.
4. Implementar novos recursos de teste e avaliar facilidade de integração.
5. Verificar cobertura e isolamento de testes automatizados.
6. Analisar histórico de bugs e correlacionar com problemas de modularidade.
7. Revisar documentação para verificar alinhamento com implementação real.

**Pós-condição:**  
&ensp;Métricas de qualidade de código são documentadas.
&ensp;Problemas de modularidade são identificados e priorizados para refatoração.
&ensp;Recomendações para melhorias na arquitetura são documentadas.

**Ferramentas:**  
&ensp;SonarQube, ESLint, JaCoCo, Estrutura para revisão de código

**Resultado Esperado:**  
&ensp;O código do sistema é modular, com alta coesão e baixo acoplamento, permitindo modificações independentes, fácil adição de novos recursos e testes isolados para cada módulo.

### RNF09 - Padrões de Codificação
**Descrição:**  
&ensp;O sistema deve seguir padrões de codificação e documentação.

**Critério de Aceitação:**  
&ensp;O código-fonte deve aderir a padrões de codificação estabelecidos para as linguagens utilizadas e incluir documentação adequada (comentários, documentação de API, etc.).

**Cenários de Teste:**  
- **Positivo:** O código segue consistentemente os padrões de codificação estabelecidos.
- **Positivo Adicional:** A documentação é clara, completa e atualizada.
- **Positivo Adicional:** Ferramentas automatizadas de verificação de estilo não reportam problemas significativos.
- **Negativo:** Partes significativas do código não seguem os padrões estabelecidos.
- **Negativo Adicional:** Documentação ausente, incompleta ou desatualizada.
- **Negativo Adicional:** Inconsistências de estilo entre diferentes partes do código.

**Pré-condição:**  
&ensp;Padrões de codificação e documentação definidos e documentados.
&ensp;Ferramentas de verificação de estilo configuradas.
&ensp;Código-fonte completo disponível para análise.

**Procedimento:**  
1. Executar verificação automatizada em todo o código-fonte.
2. Revisar manualmente amostras de código de diferentes partes do sistema.
3. Verificar completude e qualidade da documentação (comentários, documentação de API, etc.).
4. Verificar consistência de estilo entre diferentes partes do código.
5. Identificar áreas com maior incidência de problemas de estilo ou documentação.
6. Verificar se novos commits mantêm os padrões estabelecidos.

**Pós-condição:**  
&ensp;Relatório de conformidade com padrões de codificação é gerado.
&ensp;Problemas de estilo e documentação são identificados e priorizados para correção.
&ensp;Processo de revisão de código é ajustado para enfatizar conformidade com padrões.

**Ferramentas:**  
&ensp;ESLint, Prettier, JSDoc, Swagger, SonarQube

**Resultado Esperado:**  
&ensp;O código-fonte segue consistentemente os padrões de codificação estabelecidos, com documentação clara, completa e atualizada, e sem problemas significativos reportados por ferramentas automatizadas de verificação de estilo.

### RNF10 - Responsividade
**Descrição:**  
&ensp;O sistema deve ser responsivo em diversos dispositivos móveis.

**Critério de Aceitação:**  
&ensp;A interface do sistema deve se adaptar automaticamente a diferentes tamanhos de tela e orientações, mantendo usabilidade e funcionalidade em smartphones e tablets.

**Cenários de Teste:**  
- **Positivo:** A interface se adapta corretamente a diferentes tamanhos de tela.
- **Positivo Adicional:** Todas as funcionalidades permanecem acessíveis em dispositivos móveis.
- **Positivo Adicional:** A experiência do usuário é consistente entre dispositivos móveis.
- **Negativo:** Elementos da interface ficam cortados ou inacessíveis em telas menores.
- **Negativo Adicional:** Funcionalidades importantes não funcionam corretamente em dispositivos móveis.
- **Negativo Adicional:** A navegação é difícil ou confusa em telas menores.

**Pré-condição:**  
&ensp;Sistema implementado com design responsivo.
&ensp;Diversos dispositivos móveis ou emuladores disponíveis para teste.
&ensp;Lista de funcionalidades críticas definida para verificação.

**Procedimento:**  
1. Identificar tamanhos de tela e dispositivos representativos para teste.
2. Acessar o sistema em cada dispositivo ou emulador.
3. Verificar adaptação da interface em diferentes tamanhos de tela e orientações.
4. Testar todas as funcionalidades críticas em cada dispositivo.
5. Verificar usabilidade e facilidade de navegação em telas menores.
6. Identificar problemas específicos de dispositivos móveis (toque vs. clique, gestos, etc.).
7. Verificar desempenho e tempo de carregamento em dispositivos móveis.

**Pós-condição:**  
&ensp;Comportamento responsivo é documentado para diferentes dispositivos.
&ensp;Problemas de responsividade são identificados e priorizados para correção.
&ensp;Recomendações para melhorias na experiência móvel são documentadas.

**Ferramentas:**  
&ensp;Chrome DevTools (modo responsivo), BrowserStack, Dispositivos físicos para teste

**Resultado Esperado:**  
&ensp;A interface do sistema se adapta corretamente a diferentes tamanhos de tela e orientações, mantendo todas as funcionalidades acessíveis e usáveis em smartphones e tablets, com experiência consistente entre desktop e dispositivos móveis.

### RNF11 - Ambiente Docker
**Descrição:**  
&ensp;O sistema deve funcionar em ambiente Docker.

**Critério de Aceitação:**  
&ensp;O sistema deve ser containerizado usando Docker, com configuração adequada para desenvolvimento, teste e produção, permitindo implantação consistente em diferentes ambientes.

**Cenários de Teste:**  
- **Positivo:** O sistema é implantado e executado com sucesso em containers Docker.
- **Positivo Adicional:** A configuração Docker suporta diferentes ambientes (dev, teste, prod).
- **Positivo Adicional:** O sistema mantém desempenho adequado quando containerizado.
- **Negativo:** Falhas na implantação ou execução em containers Docker.
- **Negativo Adicional:** Problemas de desempenho significativos quando containerizado.
- **Negativo Adicional:** Dificuldades em configurar ou manter o ambiente Docker.

**Pré-condição:**  
&ensp;Docker instalado e configurado no ambiente de teste.
&ensp;Arquivos de configuração Docker (Dockerfile, docker-compose.yml) disponíveis.
&ensp;Sistema preparado para containerização.

**Procedimento:**  
1. Revisar arquivos de configuração Docker para verificar boas práticas.
2. Construir imagens Docker para todos os componentes do sistema.
3. Implantar o sistema completo usando Docker Compose ou Kubernetes.
4. Verificar se todos os componentes iniciam corretamente e se comunicam.
5. Executar testes funcionais básicos para verificar operação correta.
6. Comparar desempenho entre ambiente containerizado e não-containerizado.
7. Testar configurações para diferentes ambientes (dev, teste, prod).
8. Verificar logs e monitoramento em ambiente containerizado.

**Pós-condição:**  
&ensp;Sistema funciona corretamente em ambiente Docker.
&ensp;Configurações para diferentes ambientes são validadas.
&ensp;Problemas específicos de containerização são identificados e resolvidos.

**Ferramentas:**  
&ensp;Docker, Docker Compose, Kubernetes (opcional), Ferramentas de monitoramento de containers

**Resultado Esperado:**  
&ensp;O sistema é implantado e executado com sucesso em containers Docker, com configurações adequadas para diferentes ambientes, mantendo desempenho adequado e facilitando implantação consistente.

### RNF12 - Integridade de Dados
**Descrição:**  
&ensp;O sistema deve garantir a integridade dos dados armazenados e transmitidos.

**Critério de Aceitação:**  
&ensp;O sistema deve implementar mecanismos para garantir que os dados não sejam corrompidos durante armazenamento ou transmissão, incluindo validação, verificação de integridade e proteção contra modificações não autorizadas.

**Cenários de Teste:**  
- **Positivo:** Os dados mantêm integridade durante todo o ciclo de vida (criação, armazenamento, transmissão, recuperação).
- **Positivo Adicional:** Mecanismos de validação impedem a entrada de dados inválidos.
- **Positivo Adicional:** Transações de banco de dados mantêm consistência mesmo em caso de falhas.
- **Negativo:** Dados são corrompidos durante armazenamento ou transmissão.
- **Negativo Adicional:** Falhas no sistema resultam em inconsistência de dados.
- **Negativo Adicional:** Modificações não autorizadas de dados não são detectadas.

**Pré-condição:**  
&ensp;Sistema em ambiente de teste com dados representativos.
&ensp;Mecanismos de validação e verificação de integridade implementados.
&ensp;Ferramentas para simular falhas e corrupção de dados disponíveis.

**Procedimento:**  
1. Verificar implementação de validação de entrada em todos os formulários e APIs.
2. Testar transações de banco de dados para garantir atomicidade e consistência.
3. Simular falhas durante operações críticas (queda de conexão, reinicialização de servidor, etc.).
4. Verificar integridade dos dados após recuperação de falhas.
5. Testar mecanismos de detecção de modificações não autorizadas.
6. Verificar implementação de checksums ou outros mecanismos de verificação de integridade.
7. Analisar logs para identificar possíveis problemas de integridade de dados.

**Pós-condição:**  
&ensp;Integridade dos dados é verificada e documentada.
&ensp;Problemas potenciais são identificados e priorizados para correção.
&ensp;Mecanismos de proteção são validados e melhorados se necessário.

**Ferramentas:**  
&ensp;Ferramentas de teste de banco de dados, Ferramentas de interceptação de rede, Ferramentas de simulação de falhas

**Resultado Esperado:**  
&ensp;O sistema mantém a integridade dos dados durante todo o ciclo de vida, com mecanismos eficazes de validação, verificação de integridade e proteção contra modificações não autorizadas, mesmo em caso de falhas.

### RNF13 - Autenticação e Sessão
**Descrição:**  
&ensp;O sistema deve autenticar usuários por senha e ter política de expiração de sessão.

**Critério de Aceitação:**  
&ensp;O sistema deve implementar autenticação segura baseada em senha, com políticas de complexidade adequadas, e sessões que expiram automaticamente após período de inatividade.

**Cenários de Teste:**  
- **Positivo:** Usuários autenticados com credenciais válidas têm acesso às funcionalidades apropriadas.
- **Positivo Adicional:** Sessões expiram automaticamente após período de inatividade.
- **Positivo Adicional:** Políticas de complexidade de senha são aplicadas durante cadastro e alteração.
- **Negativo:** Tentativas de autenticação com credenciais inválidas são rejeitadas.
- **Negativo Adicional:** Acesso a funcionalidades protegidas sem autenticação é bloqueado.
- **Negativo Adicional:** Senhas que não atendem aos requisitos de complexidade são rejeitadas.

**Pré-condição:**  
&ensp;Sistema com funcionalidade de autenticação implementada.
&ensp;Políticas de senha e expiração de sessão configuradas.
&ensp;Usuários de teste com diferentes níveis de acesso disponíveis.

**Procedimento:**  
1. Testar autenticação com credenciais válidas e inválidas.
2. Verificar aplicação de políticas de complexidade de senha durante cadastro e alteração.
3. Testar acesso a funcionalidades protegidas com e sem autenticação.
4. Verificar expiração automática de sessão após período de inatividade.
5. Testar renovação de sessão durante atividade contínua.
6. Verificar comportamento após logout manual.
7. Testar autenticação simultânea em múltiplos dispositivos (se aplicável).

**Pós-condição:**  
&ensp;Mecanismos de autenticação e gestão de sessão funcionam corretamente.
&ensp;Políticas de senha e expiração de sessão são aplicadas consistentemente.
&ensp;Acesso a funcionalidades protegidas é controlado adequadamente.

**Ferramentas:**  
&ensp;Ferramentas de teste de segurança web, Ferramentas de interceptação de requisições, Ferramentas de automação de testes

**Resultado Esperado:**  
&ensp;O sistema implementa autenticação segura baseada em senha, com políticas de complexidade adequadas, e sessões que expiram automaticamente após período de inatividade, garantindo acesso controlado às funcionalidades protegidas.

### RNF14 - Bloqueio de Acesso
**Descrição:**  
&ensp;O sistema deve registrar tentativas de acesso inválidas e aplicar bloqueio progressivo.

**Critério de Aceitação:**  
&ensp;O sistema deve registrar todas as tentativas de acesso inválidas e implementar mecanismo de bloqueio progressivo após múltiplas tentativas falhas, para proteger contra ataques de força bruta.

**Cenários de Teste:**  
- **Positivo:** Tentativas de acesso inválidas são registradas corretamente.
- **Positivo Adicional:** Bloqueio temporário é aplicado após número definido de tentativas falhas.
- **Positivo Adicional:** Duração do bloqueio aumenta progressivamente com tentativas persistentes.
- **Negativo:** Usuário legítimo consegue acessar após bloqueio temporário expirar.
- **Negativo Adicional:** Tentativas de contornar o bloqueio são detectadas e tratadas.
- **Negativo Adicional:** Ataques automatizados de força bruta são efetivamente bloqueados.

**Pré-condição:**  
&ensp;Sistema com funcionalidade de autenticação e bloqueio implementada.
&ensp;Políticas de bloqueio progressivo configuradas.
&ensp;Ferramentas para simular múltiplas tentativas de acesso disponíveis.

**Procedimento:**  
1. Realizar múltiplas tentativas de login com credenciais inválidas para um mesmo usuário.
2. Verificar se as tentativas são registradas corretamente em logs.
3. Confirmar aplicação de bloqueio temporário após número definido de tentativas falhas.
4. Verificar se a duração do bloqueio aumenta com tentativas persistentes.
5. Testar acesso com credenciais válidas após expiração do bloqueio.
6. Simular ataques automatizados de força bruta e verificar eficácia do bloqueio.
7. Testar tentativas de contornar o bloqueio (diferentes IPs, user agents, etc.).

**Pós-condição:**  
&ensp;Mecanismo de bloqueio progressivo funciona corretamente.
&ensp;Tentativas de acesso inválidas são registradas adequadamente.
&ensp;Sistema está protegido contra ataques de força bruta.

**Ferramentas:**  
&ensp;Ferramentas de teste de segurança web, Scripts de automação para simulação de ataques, Analisadores de log

**Resultado Esperado:**  
&ensp;O sistema registra todas as tentativas de acesso inválidas e implementa mecanismo eficaz de bloqueio progressivo, protegendo contra ataques de força bruta enquanto permite acesso legítimo após expiração do bloqueio.

### RNF15 - Restrição de Acesso
**Descrição:**  
&ensp;O sistema deve restringir o acesso de cada assessor somente aos clientes sob sua gestão.

**Critério de Aceitação:**  
&ensp;O sistema deve implementar controle de acesso que garanta que cada assessor visualize e interaja apenas com os clientes formalmente associados a ele, sem possibilidade de acesso a dados de clientes de outros assessores.

**Cenários de Teste:**  
- **Positivo:** Assessor visualiza apenas clientes sob sua gestão.
- **Positivo Adicional:** Tentativas de acessar clientes de outros assessores são bloqueadas.
- **Positivo Adicional:** Logs registram tentativas de acesso não autorizado.
- **Negativo:** Assessor não consegue acessar clientes de outros assessores via URL direta.
- **Negativo Adicional:** Assessor não consegue acessar clientes de outros assessores via manipulação de parâmetros.
- **Negativo Adicional:** APIs e endpoints protegidos verificam autorização antes de retornar dados.

**Pré-condição:**  
&ensp;Sistema com múltiplos assessores e clientes cadastrados.
&ensp;Associações entre assessores e clientes definidas.
&ensp;Mecanismos de controle de acesso implementados.

**Procedimento:**  
1. Autenticar como diferentes assessores e verificar lista de clientes disponíveis.
2. Tentar acessar diretamente (via URL) detalhes de clientes não associados.
3. Tentar manipular parâmetros de requisição para acessar dados não autorizados.
4. Verificar se APIs e endpoints verificam autorização antes de retornar dados.
5. Testar funcionalidades de busca e filtro para garantir que retornem apenas clientes autorizados.
6. Verificar logs para confirmar registro de tentativas de acesso não autorizado.
7. Testar cenários de transferência de clientes entre assessores (se aplicável).

**Pós-condição:**  
&ensp;Controle de acesso funciona corretamente, restringindo visualização e interação.
&ensp;Tentativas de acesso não autorizado são bloqueadas e registradas.
&ensp;Dados de clientes permanecem protegidos contra acesso indevido.

**Ferramentas:**  
&ensp;Ferramentas de teste de segurança web, Ferramentas de interceptação de requisições, Analisadores de log

**Resultado Esperado:**  
&ensp;O sistema implementa controle de acesso eficaz que garante que cada assessor visualize e interaja apenas com os clientes sob sua gestão, bloqueando e registrando tentativas de acesso não autorizado.

### RNF16 - Facilidade de Aprendizado
**Descrição:**  
&ensp;O sistema deve permitir que o usuário aprenda a utilizá-lo em menos de 6 minutos.

**Critério de Aceitação:**  
&ensp;Novos usuários devem ser capazes de aprender as funcionalidades básicas do sistema (login, visualização de dashboard, consulta de clientes, aplicação de filtros) em menos de 6 minutos, sem necessidade de treinamento extensivo.

**Cenários de Teste:**  
- **Positivo:** Usuários sem experiência prévia aprendem funcionalidades básicas em menos de 6 minutos.
- **Positivo Adicional:** Interface intuitiva permite descoberta natural de funcionalidades.
- **Positivo Adicional:** Elementos de ajuda contextual facilitam o aprendizado.
- **Negativo:** Usuários levam mais de 6 minutos para aprender funcionalidades básicas.
- **Negativo Adicional:** Usuários cometem erros frequentes durante o aprendizado inicial.
- **Negativo Adicional:** Usuários precisam de suporte extensivo para realizar tarefas básicas.

**Pré-condição:**  
&ensp;Sistema completamente funcional em ambiente de teste.
&ensp;Grupo de usuários sem experiência prévia com o sistema disponível para teste.
&ensp;Lista de tarefas básicas definida para avaliação.

**Procedimento:**  
1. Selecionar grupo de usuários sem experiência prévia com o sistema.
2. Definir lista de tarefas básicas para avaliação (login, visualização de dashboard, consulta de clientes, aplicação de filtros).
3. Fornecer acesso ao sistema sem treinamento prévio ou com mínimas instruções.
4. Medir tempo necessário para aprender a realizar cada tarefa básica.
5. Observar e registrar dificuldades encontradas durante o aprendizado.
6. Coletar feedback dos usuários sobre facilidade de aprendizado.
7. Analisar resultados para identificar áreas de melhoria na usabilidade.

**Pós-condição:**  
&ensp;Tempos de aprendizado são documentados para diferentes funcionalidades.
&ensp;Dificuldades e pontos de confusão são identificados e priorizados para melhoria.
&ensp;Feedback dos usuários é analisado para orientar aprimoramentos na interface.

**Ferramentas:**  
&ensp;Ferramentas de gravação de sessão, Questionários de usabilidade, Cronômetro

**Resultado Esperado:**  
&ensp;Novos usuários aprendem as funcionalidades básicas do sistema em menos de 6 minutos, com interface intuitiva que permite descoberta natural de funcionalidades e elementos de ajuda contextual que facilitam o aprendizado.

### RNF17 - Mensagens de Erro
**Descrição:**  
&ensp;O sistema deve exibir mensagens de erro claras e orientativas.

**Critério de Aceitação:**  
&ensp;As mensagens de erro exibidas pelo sistema devem ser claras, em linguagem compreensível para o usuário, e fornecer orientações sobre como resolver o problema ou prosseguir.

**Cenários de Teste:**  
- **Positivo:** Mensagens de erro são claras e compreensíveis para usuários não técnicos.
- **Positivo Adicional:** Mensagens incluem orientações sobre como resolver o problema.
- **Positivo Adicional:** Erros críticos são destacados visualmente de forma apropriada.
- **Negativo:** Mensagens de erro técnicas ou crípticas são exibidas ao usuário.
- **Negativo Adicional:** Erros ocorrem sem feedback adequado ao usuário.
- **Negativo Adicional:** Mensagens de erro não fornecem orientação sobre como proceder.

**Pré-condição:**  
&ensp;Sistema em ambiente de teste com funcionalidades completas.
&ensp;Lista de cenários de erro definida para teste.
&ensp;Usuários de teste disponíveis para avaliação.

**Procedimento:**  
1. Identificar e documentar cenários comuns de erro (validação de formulários, falhas de autenticação, etc.).
2. Provocar cada cenário de erro de forma controlada.
3. Avaliar clareza e utilidade das mensagens de erro exibidas.
4. Verificar se as mensagens incluem orientações sobre como resolver o problema.
5. Testar compreensão das mensagens com usuários não técnicos.
6. Verificar destaque visual adequado para erros críticos.
7. Analisar logs para garantir que erros internos são tratados adequadamente.

**Pós-condição:**  
&ensp;Mensagens de erro são documentadas e avaliadas quanto à clareza e utilidade.
&ensp;Problemas com mensagens de erro são identificados e priorizados para melhoria.
&ensp;Recomendações para aprimoramento de mensagens são documentadas.

**Ferramentas:**  
&ensp;Ferramentas de captura de tela, Questionários de avaliação, Ferramentas de teste de interface

**Resultado Esperado:**  
&ensp;O sistema exibe mensagens de erro claras e compreensíveis, com orientações sobre como resolver problemas, e destaque visual apropriado para erros críticos, facilitando a recuperação de erros pelos usuários.

### RNF18 - Feedback Visual
**Descrição:**  
&ensp;O sistema deve conter feedback visual para auxiliar a identificação de usuários com carteiras incoerentes.

**Critério de Aceitação:**  
&ensp;O sistema deve implementar elementos visuais claros (cores, ícones, destaque) para facilitar a identificação rápida de clientes com carteiras desalinhadas ao perfil de risco.

**Cenários de Teste:**  
- **Positivo:** Clientes com carteiras incoerentes são facilmente identificáveis por elementos visuais.
- **Positivo Adicional:** Diferentes níveis de incoerência são representados visualmente de forma distinta.
- **Positivo Adicional:** Elementos visuais são consistentes em todas as visualizações (lista, detalhes, dashboard).
- **Negativo:** Elementos visuais são ambíguos ou difíceis de interpretar.
- **Negativo Adicional:** Feedback visual não é perceptível para usuários com deficiências visuais.
- **Negativo Adicional:** Inconsistência de elementos visuais entre diferentes partes do sistema.

**Pré-condição:**  
&ensp;Sistema com dados de clientes incluindo casos de carteiras coerentes e incoerentes.
&ensp;Elementos visuais de feedback implementados.
&ensp;Diferentes visualizações (lista, detalhes, dashboard) disponíveis para teste.

**Procedimento:**  
1. Acessar diferentes visualizações que mostram status de coerência de carteiras (lista, detalhes, dashboard).
2. Verificar se clientes com carteiras incoerentes são facilmente identificáveis.
3. Avaliar se diferentes níveis de incoerência são representados visualmente de forma distinta.
4. Verificar consistência dos elementos visuais entre diferentes visualizações.
5. Testar percepção dos elementos visuais em diferentes condições (tamanhos de tela, brilho, etc.).
6. Avaliar acessibilidade dos elementos visuais para usuários com deficiências visuais.
7. Coletar feedback de usuários sobre clareza e utilidade dos elementos visuais.

**Pós-condição:**  
&ensp;Eficácia dos elementos visuais é avaliada e documentada.
&ensp;Problemas de clareza ou consistência são identificados e priorizados para melhoria.
&ensp;Recomendações para aprimoramento do feedback visual são documentadas.

**Ferramentas:**  
&ensp;Ferramentas de teste de acessibilidade, Ferramentas de captura de tela, Questionários de avaliação

**Resultado Esperado:**  
&ensp;O sistema implementa elementos visuais claros e consistentes que facilitam a identificação rápida de clientes com carteiras desalinhadas, com diferentes níveis de incoerência representados visualmente de forma distinta e acessível a todos os usuários.

&ensp;Nota: Os requisitos não funcionais foram atualizados para refletir as necessidades reais do projeto. A lista foi ajustada para incluir apenas os 18 RNFs presentes no planejamento de testes, removendo o RNF03 (Registro de Logs) e o RNF12 (Agnóstico de Provedor). O RNF02 (Disponibilidade) foi simplificado para permitir testes em um único dia. Veja a [seção 3.2 Requisitos Não Funcionais (RNFs)](#32-requisitos-não-funcionais-rnfs) para mais detalhes.

## 9.2.2 Resultados

_conteúdo_

## 9.3 Testes de Usabilidade

Os testes de usabilidade verificam a interface e experiência do usuário através de sessões com participantes da faculdade, incluindo funcionários e alunos, e possivelmente pessoas relacionadas ao mercado financeiro, representando diferentes perfis e necessidades. Estas sessões permitem identificar pontos de dificuldade e oportunidades de melhoria na interação, garantindo uma experiência intuitiva e eficiente para os usuários finais.

## 9.3.1 Planejamento

### ******* Objetivo dos Testes de Usabilidade

&ensp;O objetivo é avaliar como usuários interagem com o sistema de recomendação, verificando:

- **Facilidade de uso:** Intuitividade na navegação e compreensão dos recursos.
- **Eficiência:** Tempo e número de etapas necessárias para executar tarefas-chave.
- **Satisfação:** Percepção geral sobre a experiência de uso (layout, clareza das mensagens, feedback de erros).
- **Dificuldades comuns:** Pontos em que o assessor encontra obstáculos ou confusão ao completar as tarefas.
- **Relevância das recomendações:** Percepção sobre a utilidade e precisão das recomendações de investimento geradas pelo sistema.

### ******* Planejamento dos Testes

#### *******.1 Participantes

&ensp;Os testes serão realizados com **pelo menos 5 usuários externos ao grupo**. Esses participantes serão selecionados entre a comunidade acadêmica (sem necessidade de conhecimento prévio em finanças) e, quando possível, profissionais do mercado financeiro.

&ensp;Como o sistema possui apenas um tipo de usuário (o assessor de investimentos), não há necessidade de representação de diferentes perfis durante os testes. O foco será avaliar a usabilidade geral do sistema, que foi desenvolvido especificamente para auxiliar assessores na gestão de carteiras de clientes e na geração de recomendações de investimentos alinhadas ao perfil de cada cliente.

#### *******.2 Equipe de Condução

- **Facilitador (Membro A):** Conduz a explicação inicial ao participante, apresenta as tarefas, cronometra o tempo de execução das atividades, realiza a entrevista final e intervém apenas se necessário para evitar que o teste fique bloqueado.
- **Anotador (Membro B):** Observa o comportamento do participante e preenche a tabela de resultados, registrando comentários, dificuldades e demais informações relevantes.

#### *******.3 Ambiente e Período

- **Local:** Presencialmente na Inteli ou em qualquer outro local adequado, dependendo da disponibilidade dos participantes.
- **Recursos:**
  - **Dispositivos:** Smartphones ou tablets para acesso ao sistema mobile.
  - **Cronômetro:** Para medir a duração das tarefas.
  - **Tabela de Resultados:** Uma planilha onde serão registrados os dados de cada tarefa (nome, enunciado, tempo, dificuldades encontradas, etc.).
  - **Entrevista:** Para aprofundar o feedback qualitativo sobre a experiência do participante ao término dos testes.
  - **Tabela SUS (System Usability Scale):** Para coleta de feedback quantitativo sobre a usabilidade, aplicado após a conclusão das tarefas.
- **Tempo Estimado:** Aproximadamente 15 minutos por participante, dependendo da complexidade das tarefas, e incluindo as etapas de coleta de feedback.

#### *******.4 Perguntas para a Entrevista

- Se fosse possível melhorar um aspecto do sistema, qual seria e por quê?
- Qual funcionalidade do sistema você considera mais útil para o trabalho diário de um assessor de investimentos, baseado no seu conhecimento, e por quê?
- As recomendações de investimento geradas pelo sistema parecem relevantes e precisas para o perfil dos clientes? Por quê?
- Se você fosse um assessor, você se sentiria confiante em utilizar as recomendações deste sistema para orientar seus clientes em decisões reais de investimento? Por quê?
- A interface do sistema facilita a identificação de carteiras desalinhadas ao perfil do cliente? Como isso poderia ser melhorado?
- Quão fácil foi entender as informações apresentadas nos gráficos e visualizações de dados?

#### *******.5 Tabela SUS

&ensp;Este questionário, baseado no método **System Usability Scale (SUS)**, visa avaliar a usabilidade do sistema em diferentes dimensões, como facilidade de uso, clareza da interface, desempenho e confiança do usuário. Durante o preenchimento, os participantes deverão considerar sua experiência como assessor de investimentos utilizando o sistema.

&ensp;Para cada uma das **dez afirmações** abaixo, deve-se atribuir um nível de concordância de **1 (Discordo Totalmente)** a **5 (Concordo Totalmente)**. Nas afirmações marcadas como *(inverso)*, uma pontuação mais alta representa um ponto negativo do sistema, devendo ser invertida ao calcular eventuais escores de usabilidade.

1. Eu acho que usaria esse aplicativo diariamente para verificar e/ou ajustar o alinhamento entre o perfil de risco dos clientes e suas carteiras de investimentos.
2. Eu acho o sistema desnecessariamente simples e sinto falta de mais informações em relação aos clientes e suas carteiras. *(inverso)*
3. Eu achei o sistema fácil de usar pela simplicidade das telas.				
4. Eu acho que precisaria de ajuda de uma pessoa com conhecimentos técnicos para usar o sistema. *(inverso)*
5. Eu acho que as várias funcionalidade do sistema estão muito bem integradas e completamente funcionais.			
6. Eu acho que o sistema apresenta muita inconsistência na navegação ou nos resultados apresentados. *(inverso)*
7. Eu imagino que a maioria dos assessores irão aprender como usar esse sistema rapidamente.
8. Eu achei o sistema confuso de usar, as informações e o fluxo de telas não se encaixam na rotina diária de um assessor do BTG Pactual. *(inverso)*
9. Eu me senti confiante ao buscar informações dos clientes no sistema, pois os dados estavam bem estruturados, completos e fáceis de localizar.
10. Senti que precisei assimilar conceitos que vão além do conhecimento habitual de um assessor para operar o sistema. *(inverso)*

#### *******.6 Roteiro de Execução dos Testes

**Briefing Inicial**  
- **Facilitador:**
  - Explica ao participante que o objetivo é avaliar o sistema, não a pessoa.
  - Informa sobre o formato do teste, a duração aproximada, como os dados serão anotados e contextualiza o sistema.
  - Ressalta que a participação é voluntária e pode ser interrompida a qualquer momento.
  - Explica o contexto do sistema: "Este é um sistema de recomendação de investimentos para assessores financeiros, que ajuda a identificar carteiras desalinhadas e sugerir ajustes baseados no perfil do cliente."

**Execução dos Cenários de Teste**  
- **Facilitador:**
  - Fornece ao participante o Enunciado do Cenário de Teste (conforme definido na tabela de resultados).
  - Cronometra o tempo de execução de cada tarefa.
  - Só intervém se o usuário estiver completamente bloqueado.
- **Participante:**
  - Executa a tarefa, procurando finalizar sem assistência direta.
- **Anotador:**
  - Preenche a tabela de resultados para cada tarefa, registrando:
    - Nome do Cenário de Teste e o Enunciado que o usuário está executando;
    - Nome do Usuário/Participante;
    - Resultados por Etapas (tempo, dificuldades, dúvidas levantadas);
    - Resultado Geral da Tarefa (se concluiu com sucesso, se precisou de ajuda, etc.).

**Entrevista Final**  
- **Facilitador:**
  - Conduz uma entrevista qualitativa utilizando perguntas direcionadas para aprofundar o feedback do participante.
- **Participante:**
  - Responde de forma aberta e detalhada, fornecendo informações e justificativas que ilustrem sua experiência com o sistema.
- **Anotador:**
  - Registra os insights-chave, observações e comentários que complementem os dados quantitativos obtidos na tabela de resultados.

**Questionário SUS**  
- **Facilitador:**
  - Orienta o participante a preencher o questionário SUS para avaliar a experiência.
- **Participante:**
  - Responde ao questionário, atribuindo notas para cada afirmação.

**Encerramento**  
- **Anotador:**
  - Verifica se todos os campos da tabela de resultados estão devidamente preenchidos.
- **Facilitador:**
  - Agradece a participação e esclarece eventuais dúvidas sobre o sistema ou o projeto.

### ******* Cenários de Teste

&ensp;Os cenários de teste foram elaborados para avaliar as principais funcionalidades do sistema utilizadas por assessores de investimentos.

1. **Login e Navegação Inicial**  
   - **Objetivo:** Verificar a facilidade de acesso ao sistema e a clareza da navegação inicial.  
   - **Critérios de avaliação:**
     - *Eficácia:* O usuário consegue fazer login e identificar as principais seções do sistema.
     - *Eficiência:* O processo é concluído em menos de 1 minuto.
     - *Satisfação:* A interface inicial é percebida como clara e organizada.
   - **Comando:** "Faça login no sistema usando as credenciais fornecidas e explore a tela inicial para identificar as principais funcionalidades disponíveis para um assessor."

2. **Visualização de Lista de Clientes**  
   - **Objetivo:** Avaliar a clareza na apresentação da lista de clientes e a facilidade de identificação de informações relevantes.  
   - **Critérios de avaliação:**
     - *Eficácia:* O usuário consegue visualizar a lista completa de clientes e identificar informações-chave.
     - *Eficiência:* A visualização e interpretação são concluídas em menos de 1 minuto.
     - *Satisfação:* As informações são apresentadas de forma clara e organizada.
   - **Comando:** "Acesse a lista de clientes e identifique quais possuem carteiras desalinhadas ao seu perfil de risco."

3. **Visualização de Dados de Cliente Específico**  
   - **Objetivo:** Verificar a facilidade de acesso e clareza na apresentação dos dados detalhados de um cliente.  
   - **Critérios de avaliação:**
     - *Eficácia:* O usuário consegue localizar e interpretar corretamente os dados do cliente.
     - *Eficiência:* A visualização e interpretação são concluídas em menos de 2 minutos.
     - *Satisfação:* As informações são apresentadas de forma clara e compreensível.
   - **Comando:** "Acesse os detalhes do cliente 'João Silva' e identifique seu perfil de investidor, valor total investido e principais ativos em sua carteira."

4. **Identificação de Carteiras Desalinhadas**  
   - **Objetivo:** Verificar se o assessor consegue identificar facilmente clientes com carteiras desalinhadas ao seu perfil de risco.  
   - **Critérios de avaliação:**
     - *Eficácia:* O usuário identifica corretamente os clientes com carteiras desalinhadas.
     - *Eficiência:* A identificação é realizada em menos de 2 minutos.
     - *Satisfação:* O feedback visual é percebido como claro e útil.
   - **Comando:** "Identifique quais clientes possuem carteiras desalinhadas ao seu perfil de risco e explique como o sistema indica essa informação."

5. **Análise de Recomendações**  
   - **Objetivo:** Avaliar a clareza e utilidade das recomendações de investimento geradas pelo sistema.  
   - **Critérios de avaliação:**
     - *Eficácia:* O usuário consegue encontrar facilmente as recomendações na interface.
     - *Eficiência:* A análise é concluída em menos de 3 minutos.
     - *Satisfação:* As recomendações são percebidas como relevantes e bem fundamentadas.
   - **Comando:** "Analise as recomendações de investimento para o cliente 'Maria Oliveira' e explique o que o sistema está sugerindo e por quê."

6. **Navegação Entre Perfis de Clientes**  
   - **Objetivo:** Avaliar a facilidade de navegação entre diferentes perfis de clientes.  
   - **Critérios de avaliação:**
     - *Eficácia:* O usuário consegue navegar facilmente entre diferentes perfis de clientes.
     - *Eficiência:* A navegação entre perfis é concluída em menos de 30 segundos.
     - *Satisfação:* A transição entre perfis é percebida como fluida e intuitiva.
   - **Comando:** "Após visualizar os detalhes do cliente 'Maria Oliveira', navegue para o perfil do cliente 'Paulo Santos' e identifique suas informações principais."

7. **Filtragem e Busca de Clientes**  
   - **Objetivo:** Verificar a eficiência das funcionalidades de filtragem e busca de clientes.  
   - **Critérios de avaliação:**
     - *Eficácia:* O usuário consegue filtrar e encontrar clientes específicos.
     - *Eficiência:* A busca é concluída em menos de 30 segundos.
     - *Satisfação:* As funcionalidades de filtragem são percebidas como úteis e intuitivas.
   - **Comando:** "Utilize as funcionalidades de filtragem para encontrar todos os clientes com perfil 'conservador' e valor investido acima de R$ 100.000."

8. **Interpretação de Gráficos e Visualizações**  
   - **Objetivo:** Avaliar a clareza e utilidade das visualizações de dados para análise de carteiras.  
   - **Critérios de avaliação:**
     - *Eficácia:* O usuário interpreta corretamente os gráficos e visualizações.
     - *Eficiência:* A interpretação é realizada em menos de 2 minutos.
     - *Satisfação:* As visualizações são percebidas como informativas e bem projetadas.
   - **Comando:** "Observe os gráficos da carteira do cliente 'Paulo Santos' e explique o que eles mostram sobre a distribuição de ativos e o desempenho da carteira."

### 9.3.1.4 Critérios de Avaliação

- **Eficácia:** Mede o percentual de assessores que completam as tarefas corretamente, sem cometer erros.
- **Eficiência:** Avalia o tempo médio gasto para realizar cada tarefa, refletindo a rapidez na execução sem comprometer a precisão.
- **Satisfação:** Determinada pela percepção subjetiva dos assessores sobre a usabilidade do sistema, medida por meio do questionário SUS e comentários qualitativos.
- **Taxa de Erro:** Quantifica o número de tentativas incorretas realizadas antes da conclusão de uma tarefa, indicando dificuldades na execução.
- **Taxa de Sucesso:** Indica a proporção de cenários de teste concluídos sem a necessidade de intervenção do Facilitador.
- **Relevância das Recomendações:** Avalia a percepção dos assessores sobre a utilidade e precisão das recomendações de investimento geradas pelo sistema.

### ******* Relatório de Testes de Usabilidade

&ensp;Após a conclusão de todos os testes e o devido preenchimento da tabela de resultados, será elaborado um relatório consolidado que incluirá:

- **Resumo dos Participantes:** Perfil geral dos usuários que testaram o sistema, incluindo sua experiência prévia com sistemas financeiros e conhecimento sobre investimentos.
- **Análise de Resultados:** Interpretação dos dados da tabela, destacando tendências, padrões de dificuldades e aspectos positivos identificados.
- **Principais Problemas Encontrados:** Relação dos problemas críticos ou recorrentes que demandam correção ou melhoria.
- **Avaliação das Recomendações:** Análise específica sobre a percepção dos usuários quanto à qualidade e utilidade das recomendações de investimento.
- **Sugestões de Melhoria:** Recomendações objetivas para aprimorar a usabilidade, embasadas nas observações coletadas.
- **Conclusão:** Síntese de como os testes contribuem para a evolução do projeto e sugestões de próximas etapas de testes, se necessário.

&ensp;Esse relatório proporcionará uma visão abrangente do desempenho do sistema em termos de usabilidade para assessores de investimentos, indicando ajustes ou refinamentos necessários antes da finalização do projeto. O foco será garantir que o sistema atenda eficientemente às necessidades dos assessores em seu trabalho diário de análise de carteiras e recomendação de investimentos.

## 9.3.2 Resultados

_conteúdo_

## 9.4 Testes de APIs Externas

&emsp; Esta seção descreve o planejamento dos testes voltados à validação das integrações com APIs externas utilizadas no projeto. O sistema se comunica com dois serviços externos:

1. **Supabase Auth** – responsável pelo mecanismo de autenticação e vinculação de usuários via UID.
2. **RandomUser API** – utilizada para gerar dados fictícios (nome, email e telefone) de clientes, em função da ausência desses dados nos registros reais fornecidos.

&emsp; Os testes foram estruturados para verificar o comportamento do sistema em cenários positivos (esperados) e negativos (falhas), a fim de assegurar a fidedignidade da comunicação com serviços externos, o tratamento adequado de erros e a consistência dos dados obtidos.

## 9.4.1 Planejamento

### Supabase Auth – Testes da API de Autenticação

**Objetivo:** Verificar o correto funcionamento da autenticação via Supabase Auth, incluindo o vínculo do identificador único do usuário (`uid`) ao respectivo `advisor_id` no banco relacional.

**Pré-condições:**

* O backend do sistema deve estar ativo e corretamente integrado ao Supabase.
* O endpoint de autenticação deve estar funcional.

**Cenários de Teste:**

| ID   | Cenário                           | Descrição                                            | Tipo     | Resultado Esperado                                          |
| ---- | --------------------------------- | ---------------------------------------------------- | -------- | ----------------------------------------------------------- |
| SA01 | Autenticação com UID válido       | Enviar um `uid` válido via endpoint de autenticação  | Positivo | `advisor_id` retornado e vinculado corretamente             |
| SA02 | Autenticação com UID já vinculado | Enviar um `uid` já existente no sistema              | Positivo | Retorna o mesmo `advisor_id` sem duplicações                |
| SA03 | UID inválido (string malformada)  | Enviar uma string vazia ou fora do padrão como `uid` | Negativo | Resposta 400 com mensagem de erro clara                     |
| SA04 | UID não autorizado                | Enviar um `uid` inexistente ou revogado no Supabase  | Negativo | Resposta 401 ou equivalente indicando falha de autenticação |
| SA05 | Supabase fora do ar               | Simular indisponibilidade do serviço Supabase        | Negativo | Retorno 503 com tratamento de erro apropriado               |

**Instruções de Execução:**

* Utilizar ferramentas como Postman ou scripts automatizados com Jest/Supertest.
* Simular indisponibilidade de serviço com ferramentas como `nock` ou `Mock Service Worker`.
* Verificar respostas HTTP, logs e persistência no banco de dados.

---

### RandomUser API – Testes de Geração de Dados Fictícios

**Objetivo:** Validar a integração com a API RandomUser, assegurando que os dados gerados estejam no formato correto e que falhas sejam tratadas adequadamente.

**Pré-condições:**

* A funcionalidade de geração de dados deve estar habilitada no backend.
* O sistema deve estar conectado à internet para acesso à API.

**Cenários de Teste:**

| ID   | Cenário                             | Descrição                                                 | Tipo     | Resultado Esperado                                               |
| ---- | ----------------------------------- | --------------------------------------------------------- | -------- | ---------------------------------------------------------------- |
| RU01 | Requisição bem-sucedida             | Realizar uma chamada padrão à RandomUser API              | Positivo | Retorna objeto com `email`, `phone` e `name` válidos             |
| RU02 | Erro interno na API                 | Simular resposta 500 da API                               | Negativo | Backend lida com erro e responde com status controlado (ex: 502) |
| RU03 | Timeout na resposta                 | Simular lentidão extrema na API                           | Negativo | Backend aborta após tempo limite e retorna status 504            |
| RU04 | Excesso de requisições (rate limit) | Simular erro 429 por exceder limite de chamadas           | Negativo | Implementa retry com backoff ou fallback local                   |
| RU05 | Validação de estrutura de dados     | Verificar se os dados retornados seguem o schema esperado | Positivo | Dados validados antes de uso pelo frontend ou persistência       |

**Instruções de Execução:**

* Realizar chamadas reais e simuladas à API com ferramentas como Postman ou mocks.
* Validar estrutura dos dados com JSON Schema ou lógica de verificação.
* Implementar testes de fallback em caso de falha com dados simulados locais.
* Avaliar resposta do sistema em diferentes cenários de carga e disponibilidade.

---

### Observações Complementares:

* Todas as chamadas às APIs externas devem ser encapsuladas com timeout, tratamento de exceções e logs apropriados.
* A integração com a RandomUser API será otimizada com um mecanismo de cache (VHS module) para evitar requisições repetidas.
* O tratamento de falhas externas deve garantir que a instabilidade de serviços terceiros não afete a operação principal do sistema.

## 9.4.2 Resultados

_conteúdo_

## 9.5 Estratégia de Testes para Cenários de Falha

### 9.5.1 Testes de Falha de Rede
- **Objetivo**: Verificar comportamento do sistema quando APIs externas estão indisponíveis
- **Cenários Testados**:
  - Timeout de conexão (> 30 segundos)
  - Perda de conectividade intermitente
  - Latência alta (> 5 segundos)
  - Falha de DNS

- **Implementação**:
  ```csharp
  // Exemplo de teste com mock para simular timeout
  [Test]
  public async Task ClassificationService_ShouldHandleTimeout_WhenApiIsUnresponsive()
  {
      // Arrange
      var mockHttpClient = new Mock<HttpClient>();
      mockHttpClient.Setup(x => x.PostAsync(It.IsAny<string>(), It.IsAny<HttpContent>()))
                   .ThrowsAsync(new TaskCanceledException("Request timeout"));

      var service = new ClassificationService(mockHttpClient.Object);

      // Act & Assert
      var result = await service.ClassifyPortfolioAsync(portfolioData);
      Assert.IsTrue(result.IsFailure);
      Assert.Contains("timeout", result.Error.ToLower());
  }
  ```

### 9.5.2 Testes de Timeout de APIs
- **Configuração de Timeouts**:
  - API de Classificação: 30 segundos
  - API de Recomendação: 45 segundos
  - Banco de Dados: 15 segundos

- **Estratégias de Teste**:
  - Usar ferramentas como Chaos Mesh para simular latência
  - Implementar circuit breaker pattern
  - Testar retry policies com backoff exponencial

### 9.5.3 Testes de Limite de Memória
- **Cenários**:
  - Processamento de grandes volumes de dados (> 10.000 clientes)
  - Memory leaks em operações de longa duração
  - Garbage collection sob pressão

- **Ferramentas**:
  - **dotMemory**: Profiling de memória em .NET
  - **K6**: Testes de carga para identificar vazamentos
  - **Kubernetes**: Limits e requests de memória

- **Como Executar**:
  ```bash
  # Teste de carga com K6
  k6 run --vus 100 --duration 30m load-test.js

  # Monitoramento de memória
  kubectl top pods --sort-by=memory

  # Análise de memory dump
  dotnet-dump collect -p <process-id>
  ```

### 9.5.4 Chaos Engineering
- **Ferramentas**: Chaos Mesh para Kubernetes
- **Experimentos**:
  - **Pod Failure**: Simular falha de pods aleatoriamente
  - **Network Chaos**: Introduzir latência e perda de pacotes
  - **IO Chaos**: Simular falhas de disco e I/O lento

- **Configuração de Experimento**:
  ```yaml
  apiVersion: chaos-mesh.org/v1alpha1
  kind: PodChaos
  metadata:
    name: pati-pod-failure
  spec:
    action: pod-failure
    mode: one
    selector:
      namespaces:
        - pati-production
      labelSelectors:
        app: pati-api
    duration: "30s"
    scheduler:
      cron: "0 */6 * * *"  # A cada 6 horas
  ```

- **Como Executar**:
  ```bash
  # Aplicar experimento de chaos
  kubectl apply -f chaos-mesh-pod-failure.yaml

  # Monitorar resultados
  kubectl get podchaos
  kubectl describe podchaos pati-pod-failure

  # Limpar experimento
  kubectl delete podchaos pati-pod-failure
  ```

### 9.5.5 Testes de Resiliência
- **Circuit Breaker**: Implementar padrão para evitar cascata de falhas
- **Bulkhead**: Isolar recursos críticos
- **Timeout e Retry**: Configurar políticas adequadas
- **Graceful Degradation**: Funcionalidade reduzida em caso de falhas

### 9.5.6 Métricas de Monitoramento
- **SLA Targets**:
  - Disponibilidade: 99.9%
  - Tempo de resposta: < 500ms (95th percentile)
  - Taxa de erro: < 1%

- **Alertas Configurados**:
  - CPU > 80% por 5 minutos
  - Memória > 85% por 3 minutos
  - Taxa de erro > 5% por 2 minutos
  - Tempo de resposta > 1s por 1 minuto

# 10. Procedimentos de Implantação

Esta seção apresenta os procedimentos necessários para a implantação e configuração completa da infraestrutura de banco de dados da aplicação. São abordados desde os requisitos técnicos até as regras de negócio implementadas diretamente no banco de dados, garantindo que a solução possa ser replicada em diferentes ambientes de desenvolvimento, teste e produção.

## 10.1 Implantação e Configuração do Banco de Dados

Esta seção tem o objetivo de esclarecer as informações essenciais para configuração e implantação dos bancos de dados da aplicação.

### 10.1.1. Visão Geral

A solução utiliza duas instâncias de bancos de dados:

* **PostgreSQL**: Um banco de dados relacional (SQL) para persistência de dados estruturados.
* **MongoDB**: Um banco de dados não relacional (NoSQL) para dados semi estruturados, como as recomendações de investimentos geradas pelo modelo de machine learning.

Ambos os bancos de dados são conteinerizados usando Docker Compose, o que facilita a implantação e o gerenciamento em diferentes ambientes.

### 10.1.2. Requisitos

Para configurar e executar os bancos de dados, você precisará ter instalado:

* **Docker Engine**: Versão 20.10.0 ou superior (ou Docker Desktop na versão LTS).
* **Docker Compose**: Versão 1.29.0 ou superior (ou Docker Compose V2, que vem integrado ao Docker Desktop).

### 10.1.3. Estrutura do Banco de Dados PostgreSQL

O modelo físico do banco de dados PostgreSQL é definido no script `01_create_database.sql`, enquanto, os dados de população do banco de dados para testes e desenvolvimento estão definidos no script `02_initial_data.sql`.

Os scripts podem ser encontrados no diretório `src/database`.

#### ********. Detalhes dos Scripts SQL

**01_create_database.sql**
Este script implementa:
- **Estrutura das tabelas**: ADVISOR, CLIENT, INVESTMENT, CLIENT_INVESTMENT, compliance_history, ACTIVITY_LOG
- **Regras de negócio automatizadas**: Triggers para validação de compatibilidade de investimentos
- **Sistema de compliance**: Detecção automática de divergências entre perfis de risco
- **Cálculo automático**: Perfil de risco da carteira baseado na composição dos investimentos
- **Auditoria**: Sistema de log automático para mudanças importantes
- **Otimização**: Índices para melhorar performance das consultas frequentes

**02_initial_data.sql**
Popula o banco com:
- 3 assessores de exemplo
- 5 clientes com diferentes perfis de risco (incluindo casos de não conformidade)
- 20 investimentos de diferentes categorias (RF Privado, Debêntures, Ações Brasil/Internacional)
- Carteiras diversificadas para demonstrar o funcionamento das regras de negócio

### 10.1.4. Implantação e Configuração

Esta seção detalha as etapas para implantar e configurar os bancos de dados usando Docker Compose.

#### Variáveis de Ambiente

As credenciais e configurações sensíveis para os bancos de dados são gerenciadas através do arquivo `.env` localizado no diretório `src/database/`.

Um exemplo de arquivo `.env` pode ser:

```bash
POSTGRES_USER=myuser
POSTGRES_PASSWORD=mypassword
POSTGRES_DB=mydatabase
MONGO_INITDB_ROOT_USERNAME=mongoadmin
MONGO_INITDB_ROOT_PASSWORD=mongopassword
```

**Importante:** Como informações sensíveis, as variáveis de ambiente em ambiente de produção não devem ser versionadas de forma pública.

#### Executando os Bancos de Dados com Docker Compose

Para levantar as instâncias do PostgreSQL e MongoDB, navegue até o diretório raiz do projeto (em que o `docker-compose.yml` está localizado) e execute o seguinte comando:

```bash
docker-compose up -d db mongo
```

`docker-compose up`: Inicia os serviços definidos no docker-compose.yml.

`-d`: Executa os contêineres em modo detached (em segundo plano).

`db mongo`: Especifica quais serviços devem ser iniciados. Neste caso, apenas os serviços de banco de dados.

Os scripts `01_create_database.sql` e `02_initial_data.sql` serão executados automaticamente na primeira vez que o contêiner PostgreSQL for inicializado, devido ao **volume mapping** no docker-compose.yml para `/docker-entrypoint-initdb.d/`.

Para parar os contêineres, execute:

```bash
docker-compose down db mongo
```

Para remover os contêineres, redes e volumes associados aos serviços db e mongo:

```bash
docker-compose down -v db mongo
```

**Atenção:** O comando docker-compose down -v removerá o volume pgdata, o que significa que todos os dados do seu banco de dados PostgreSQL serão perdidos. Use com cautela.

#### Execução Manual dos Scripts

Caso precise executar os scripts manualmente no PostgreSQL:

```sql
-- 1. Conectar ao banco
\c mydatabase

-- 2. Executar script de criação
\i /docker-entrypoint-initdb.d/01_create_database.sql

-- 3. Executar script de dados iniciais  
\i /docker-entrypoint-initdb.d/02_initial_data.sql

-- 4. Verificar se tudo foi criado corretamente
\dt  -- Listar tabelas
SELECT * FROM vw_non_compliant_clients;  -- Testar view de compliance
```

#### Acessando os Bancos de Dados
**PostgreSQL:** Você pode acessar o PostgreSQL a partir da sua máquina host na porta 5432.

Exemplo de conexão usando psql (certifique-se de ter o psql instalado):

```bash
psql -h localhost -p 5432 -U myuser -d mydatabase
```

Substitua **myuser** e **mydatabase** pelos valores definidos no seu arquivo `.env`. A senha será solicitada.

<br/>

**MongoDB:** O MongoDB estará acessível na sua máquina host na porta 27017.

Exemplo de conexão usando o mongosh (MongoDB Shell):

```bash
mongosh "mongodb://localhost:27017" --username mongoadmin --password mongopassword
```

Substitua **mongoadmin** e **mongopassword** pelos valores definidos no seu arquivo `.env`.

Dessa forma, são descritos os detalhes essenciais para configuração e implantação das instâncias de banco de dados usadas na aplicação.

### 10.1.5. Regras de Negócio Implementadas

O banco de dados implementa automaticamente as seguintes regras de negócio:

#### Compatibilidade de Investimentos (CVM 175)
- **Conservador**: Apenas investimentos de risco "Baixo"
- **Moderado**: Investimentos de risco "Baixo" e "Médio"  
- **Arrojado/Sofisticado**: Qualquer nível de risco
- **Validação automática**: Impede inserção de investimentos incompatíveis com o perfil do cliente

#### Cálculo Automático do Perfil da Carteira
- **Arrojado**: Mais de 30% em investimentos de alto risco
- **Moderado**: Menos de 30% em alto risco, mas mais de 30% em médio+alto risco
- **Conservador**: Menos de 30% em médio+alto risco
- **Atualização automática**: Recalcula o perfil sempre que há mudanças nos investimentos (inserção, atualização ou exclusão)

#### Sistema de Compliance Automático
- **Detecção de divergências**: Compara automaticamente o perfil declarado (formulário) com o perfil real da carteira
- **Marcação automática**: Define compliance = TRUE quando há divergência entre perfis
- **Histórico completo**: Registra todas as mudanças de status de compliance na tabela `compliance_history`

#### Sistema de Auditoria e Logs
- **Log automático de compliance**: Registra mudanças de status na tabela `ACTIVITY_LOG`
- **Função de log manual**: Permite registrar atividades específicas via `log_activity()`
- **Timestamps automáticos**: Atualiza `updated_at` automaticamente em todas as alterações

#### Integridade e Validações
- **Constraints de data**: Garante que `updated_at >= created_at`
- **Validação de quantidade**: Impede quantidades negativas nos investimentos
- **Referências seguras**: 
  - Impede exclusão de assessores com clientes (`RESTRICT`)
  - Impede exclusão de investimentos em uso (`RESTRICT`) 
  - Remove automaticamente investimentos do cliente quando cliente é excluído (`CASCADE`)

#### Views e Consultas Especializadas
- **View de não conformidade**: `vw_non_compliant_clients` lista clientes em situação irregular
- **Dados normalizados**: Procedure para normalizar valores de investimento para análises
- **Índices otimizados**: Aceleram consultas por assessor, data, tipo de investimento e valores

## 10.2 Implantação do Protótipo para uso por equipe de desenvolvimento

_conteúdo_

# Referências

_conteúdo_
