using Microsoft.EntityFrameworkCore;
using Pati.ClassificationService.Application.Repositories;
using Pati.ClassificationService.Domain;
using Pati.ClassificationService.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pati.ClassificationService.Infrastructure.Repositories
{
    public class ClientRepository : IClientRepository
    {
        private readonly ClassificationDbContext _context;

        public ClientRepository(ClassificationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<Client?> GetByIdAsync(int clientId)
        {
            return await _context.Clients
                .Include(c => c.Investments)
                    .ThenInclude(ci => ci.Investment)
                .Include(c => c.Recommendations)
                .FirstOrDefaultAsync(c => c.ClientId == clientId);
        }

        public Client? GetById(int clientId)
        {
            return _context.Clients
                .Include(c => c.Investments)
                    .ThenInclude(ci => ci.Investment)
                .Include(c => c.Recommendations)
                .FirstOrDefault(c => c.ClientId == clientId);
        }

        public async Task<IEnumerable<Client>> GetAllAsync()
        {
            return await _context.Clients
                .Include(c => c.Investments)
                    .ThenInclude(ci => ci.Investment)
                .Include(c => c.Recommendations)
                .ToListAsync();
        }

        public async Task<IEnumerable<Client>> GetByAdvisorIdAsync(int advisorId)
        {
            return await _context.Clients
                .Include(c => c.Investments)
                    .ThenInclude(ci => ci.Investment)
                .Include(c => c.Recommendations)
                .Where(c => c.AdvisorId == advisorId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Client>> GetNonCompliantClientsAsync()
        {
            return await _context.Clients
                .Include(c => c.Investments)
                    .ThenInclude(ci => ci.Investment)
                .Include(c => c.Recommendations)
                .Where(c => c.NonCompliance)
                .ToListAsync();
        }

        public async Task AddAsync(Client client)
        {
            if (client == null)
                throw new ArgumentNullException(nameof(client));

            await _context.Clients.AddAsync(client);
        }

        public async Task UpdateAsync(Client client)
        {
            if (client == null)
                throw new ArgumentNullException(nameof(client));

            _context.Clients.Update(client);
            await Task.CompletedTask;
        }

        public async Task DeleteAsync(int clientId)
        {
            var client = await _context.Clients.FindAsync(clientId);
            if (client != null)
            {
                _context.Clients.Remove(client);
            }
        }

        public async Task<bool> ExistsAsync(int clientId)
        {
            return await _context.Clients.AnyAsync(c => c.ClientId == clientId);
        }
    }
}
