namespace api_data_presentation.Models.QueryParameters
{
    public class InvestmentQueryParams
    {
        public string? InvestmentType { get; set; }
        public string? InvestmentRisk { get; set; }
        public string? OrderBy { get; set; }
        public int? Offset { get; set; }
        public int? Limit { get; set; }

        public bool IsValidOrderBy()
        {
            if (string.IsNullOrEmpty(OrderBy))
                return true;

            var validOrderValues = new[]
            {
                "amount_asc",
                "amount_desc",
                "date_asc",
                "date_desc"
            };

            return validOrderValues.Contains(OrderBy.ToLower());
        }

        public string GetOrderByClause()
        {
            return OrderBy?.ToLower() switch
            {
                "amount_asc" => "ci.invested_amount ASC",
                "amount_desc" => "ci.invested_amount DESC",
                "date_asc" => "ci.investment_date ASC",
                "date_desc" => "ci.investment_date DESC",
                _ => "ci.investment_date DESC"
            };
        }
    }
}