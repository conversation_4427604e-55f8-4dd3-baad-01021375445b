namespace Pati.ClassificationService.Domain
{
    /// <summary>
    /// Enum que representa os perfis de risco disponíveis baseado nas regras CVM 175
    /// </summary>
    public enum RiskProfile
    {
        /// <summary>
        /// Perfil conservador - menor tolerância ao risco
        /// </summary>
        Conservador = 1,

        /// <summary>
        /// Perfil moderado - tolerância média ao risco
        /// </summary>
        Moderado = 2,

        /// <summary>
        /// Perfil arrojado - alta tolerância ao risco
        /// </summary>
        Arrojado = 3,

        /// <summary>
        /// Perfil sofisticado - máxima tolerância ao risco
        /// </summary>
        Sofisticado = 4
    }

    /// <summary>
    /// Extensões para o enum RiskProfile
    /// </summary>
    public static class RiskProfileExtensions
    {
        /// <summary>
        /// Converte string para enum RiskProfile
        /// </summary>
        public static RiskProfile ToRiskProfile(this string riskProfileString)
        {
            return riskProfileString?.ToLower() switch
            {
                "conservador" => RiskProfile.Conservador,
                "moderado" => RiskProfile.Moderado,
                "arrojado" => RiskProfile.Arrojado,
                "sofisticado" => RiskProfile.Sofisticado,
                _ => RiskProfile.Moderado // Default
            };
        }

        /// <summary>
        /// Converte enum RiskProfile para string
        /// </summary>
        public static string ToDisplayString(this RiskProfile riskProfile)
        {
            return riskProfile switch
            {
                RiskProfile.Conservador => "Conservador",
                RiskProfile.Moderado => "Moderado",
                RiskProfile.Arrojado => "Arrojado",
                RiskProfile.Sofisticado => "Sofisticado",
                _ => "Moderado"
            };
        }

        /// <summary>
        /// Verifica se um perfil de risco é compatível com outro
        /// </summary>
        public static bool IsCompatibleWith(this RiskProfile clientProfile, RiskProfile assetProfile)
        {
            // Um cliente pode investir em ativos de risco igual ou menor ao seu perfil
            return (int)assetProfile <= (int)clientProfile;
        }

        /// <summary>
        /// Obtém todos os perfis de risco permitidos para um perfil específico
        /// </summary>
        public static RiskProfile[] GetAllowedProfiles(this RiskProfile clientProfile)
        {
            return clientProfile switch
            {
                RiskProfile.Conservador => new[] { RiskProfile.Conservador },
                RiskProfile.Moderado => new[] { RiskProfile.Conservador, RiskProfile.Moderado },
                RiskProfile.Arrojado => new[] { RiskProfile.Conservador, RiskProfile.Moderado, RiskProfile.Arrojado },
                RiskProfile.Sofisticado => new[] { RiskProfile.Conservador, RiskProfile.Moderado, RiskProfile.Sofisticado },
                _ => new[] { RiskProfile.Conservador }
            };
        }
    }
}
