using Microsoft.EntityFrameworkCore;
using Pati.ClassificationService.Application.Repositories;
using Pati.ClassificationService.Domain;
using Pati.ClassificationService.Infrastructure.Data;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pati.ClassificationService.Infrastructure.Repositories
{
    public class ClassificationRuleRepository : IClassificationRuleRepository
    {
        private readonly ClassificationDbContext _context;

        public ClassificationRuleRepository(ClassificationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<IEnumerable<ClassificationRule>> GetAllRulesAsync()
        {
            return await _context.ClassificationRules
                .Where(r => r.IsActive)
                .OrderBy(r => r.RiskProfile)
                .ToListAsync();
        }

        public async Task<IEnumerable<ClassificationRule>> GetRulesByProfileAsync(string riskProfile)
        {
            return await _context.ClassificationRules
                .Where(r => r.IsActive && r.RiskProfile.ToLower() == riskProfile.ToLower())
                .ToListAsync();
        }

        public async Task AddRuleAsync(ClassificationRule rule)
        {
            if (rule == null)
                throw new ArgumentNullException(nameof(rule));

            await _context.ClassificationRules.AddAsync(rule);
        }

        public async Task UpdateRuleAsync(ClassificationRule rule)
        {
            if (rule == null)
                throw new ArgumentNullException(nameof(rule));

            _context.ClassificationRules.Update(rule);
            await Task.CompletedTask;
        }

        public async Task DeleteRuleAsync(int ruleId)
        {
            var rule = await _context.ClassificationRules.FindAsync(ruleId);
            if (rule != null)
            {
                // Soft delete - apenas desativa a regra
                rule.Deactivate();
                _context.ClassificationRules.Update(rule);
            }
        }
    }
}
