namespace api_data_presentation.Models.QueryParameters
{
    public class ClientListQueryParams
    {
        public string? RiskProfileWallet { get; set; }
        public bool? Compliance { get; set; }
        public string? RiskProfileForm { get; set; }
        public string? OrderBy { get; set; }
        public int? Offset { get; set; }
        public int? Limit { get; set; }

        public bool IsValidOrderBy()
        {
            if (string.IsNullOrEmpty(OrderBy))
                return true;

            var validOrderValues = new[]
            {
                "name_asc",
                "name_desc",
                "total_invested_asc",
                "total_invested_desc"
            };

            return validOrderValues.Contains(OrderBy.ToLower());
        }

        public string GetOrderByClause()
        {
            return OrderBy?.ToLower() switch
            {
                "name_asc" => "c.name ASC",
                "name_desc" => "c.name DESC",
                "total_invested_asc" => "total_invested ASC",
                "total_invested_desc" => "total_invested DESC",
                _ => "c.name ASC" 
            };
        }
    }
}