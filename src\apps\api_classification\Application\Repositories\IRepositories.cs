using Pati.ClassificationService.Domain;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pati.ClassificationService.Application.Repositories
{
    public interface IClientRepository
    {
        Task<Client?> GetByIdAsync(int clientId);
        Client? GetById(int clientId); // Método síncrono para compatibilidade
        Task<IEnumerable<Client>> GetAllAsync();
        Task<IEnumerable<Client>> GetByAdvisorIdAsync(int advisorId);
        Task<IEnumerable<Client>> GetNonCompliantClientsAsync();
        Task AddAsync(Client client);
        Task UpdateAsync(Client client);
        Task DeleteAsync(int clientId);
        Task<bool> ExistsAsync(int clientId);
    }

    public interface IAdvisorRepository
    {
        Task<Advisor?> GetByIdAsync(int advisorId);
        Task<IEnumerable<Advisor>> GetAllAsync();
        Task AddAsync(Advisor advisor);
        Task UpdateAsync(Advisor advisor);
        Task DeleteAsync(int advisorId);
        Task<bool> ExistsAsync(int advisorId);
    }

    public interface IInvestmentRepository
    {
        Task<Investment?> GetByIdAsync(int investmentId);
        Task<IEnumerable<Investment>> GetAllAsync();
        Task<IEnumerable<Investment>> GetByTypeAsync(string type);
        Task<IEnumerable<Investment>> GetByRiskAsync(string risk);
        Task AddAsync(Investment investment);
        Task UpdateAsync(Investment investment);
        Task DeleteAsync(int investmentId);
        Task<bool> ExistsAsync(int investmentId);
    }

    public interface IClientRecommendationRepository
    {
        Task<ClientRecommendation?> GetByIdAsync(int recommendationId);
        Task<IEnumerable<ClientRecommendation>> GetByClientIdAsync(int clientId);
        Task AddAsync(ClientRecommendation recommendation);
        Task UpdateAsync(ClientRecommendation recommendation);
        Task DeleteAsync(int recommendationId);
    }

    public interface IClassificationRuleRepository
    {
        Task<IEnumerable<ClassificationRule>> GetAllRulesAsync();
        Task<IEnumerable<ClassificationRule>> GetRulesByProfileAsync(string riskProfile);
        Task AddRuleAsync(ClassificationRule rule);
        Task UpdateRuleAsync(ClassificationRule rule);
        Task DeleteRuleAsync(int ruleId);
    }

    public interface IUnitOfWork
    {
        IClientRepository Clients { get; }
        IInvestmentRepository Investments { get; }
        IAdvisorRepository Advisors { get; }
        IClientRecommendationRepository ClientRecommendations { get; }
        IClassificationRuleRepository ClassificationRules { get; }
        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
