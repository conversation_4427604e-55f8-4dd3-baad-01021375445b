﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by SpecFlow (https://www.specflow.org/).
//      SpecFlow Version:3.9.0.0
//      SpecFlow Generator Version:3.9.0.0
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
namespace IntegrationTests
{
    using TechTalk.SpecFlow;
    using System;
    using System.Linq;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("TechTalk.SpecFlow", "3.9.0.0")]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public partial class GeracaoDeRecomendacaoBaseadaEmDadosDoBancoFeature : object, Xunit.IClassFixture<GeracaoDeRecomendacaoBaseadaEmDadosDoBancoFeature.FixtureData>, System.IDisposable
    {
        
        private static TechTalk.SpecFlow.ITestRunner testRunner;
        
        private static string[] featureTags = ((string[])(null));
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "RecommendationService.feature"
#line hidden
        
        public GeracaoDeRecomendacaoBaseadaEmDadosDoBancoFeature(GeracaoDeRecomendacaoBaseadaEmDadosDoBancoFeature.FixtureData fixtureData, IntegrationTests_XUnitAssemblyFixture assemblyFixture, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
            this.TestInitialize();
        }
        
        public static void FeatureSetup()
        {
            testRunner = TechTalk.SpecFlow.TestRunnerManager.GetTestRunner();
            TechTalk.SpecFlow.FeatureInfo featureInfo = new TechTalk.SpecFlow.FeatureInfo(new System.Globalization.CultureInfo("en-US"), "", "Geração de recomendação baseada em dados do banco", @"  Como sistema de recomendação,
  Quero gerar recomendações para um usuário com base nos dados armazenados no banco,
  Para garantir que a recomendação seja precisa e os dados sejam recuperados corretamente.

 Cenário: Gerar recomendação para cliente inconforme existente no banco
    Given o banco de dados está limpo e contém um cliente inconforme com CPF ""12345678901"" e investimentos válidos
    When eu solicito a geração de recomendação para o cliente com CPF ""12345678901""
    Then a recomendação deve ser retornada com sucesso e conter sugestões baseadas nos dados do banco
    And nenhuma falha de conexão com o banco deve ocorrer", ProgrammingLanguage.CSharp, featureTags);
            testRunner.OnFeatureStart(featureInfo);
        }
        
        public static void FeatureTearDown()
        {
            testRunner.OnFeatureEnd();
            testRunner = null;
        }
        
        public void TestInitialize()
        {
        }
        
        public void TestTearDown()
        {
            testRunner.OnScenarioEnd();
        }
        
        public void ScenarioInitialize(TechTalk.SpecFlow.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public void ScenarioStart()
        {
            testRunner.OnScenarioStart();
        }
        
        public void ScenarioCleanup()
        {
            testRunner.CollectScenarioErrors();
        }
        
        void System.IDisposable.Dispose()
        {
            this.TestTearDown();
        }
        
        [System.CodeDom.Compiler.GeneratedCodeAttribute("TechTalk.SpecFlow", "3.9.0.0")]
        [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : System.IDisposable
        {
            
            public FixtureData()
            {
                GeracaoDeRecomendacaoBaseadaEmDadosDoBancoFeature.FeatureSetup();
            }
            
            void System.IDisposable.Dispose()
            {
                GeracaoDeRecomendacaoBaseadaEmDadosDoBancoFeature.FeatureTearDown();
            }
        }
    }
}
#pragma warning restore
#endregion
