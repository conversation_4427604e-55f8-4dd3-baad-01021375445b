using Pati.ClassificationService.Application.Services;
using Pati.ClassificationService.DTOs;
using Pati.ClassificationService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Pati.ClassificationService.Infrastructure.Services
{
    /// <summary>
    /// Implementação do serviço de classificação de carteiras
    /// Implementa as regras CVM 175 e integração com modelo SVD
    /// </summary>
    public class ClassificationService : IClassificationService
    {
        private readonly ClassificationDbContext _context;
        private readonly ISVDModelService _svdModelService;
        private readonly IRecommendationServiceClient _recommendationClient;
        private readonly ILogger<ClassificationService> _logger;

        public ClassificationService(
            ClassificationDbContext context,
            ISVDModelService svdModelService,
            IRecommendationServiceClient recommendationClient,
            ILogger<ClassificationService> logger)
        {
            _context = context;
            _svdModelService = svdModelService;
            _recommendationClient = recommendationClient;
            _logger = logger;
        }

        /// <summary>
        /// Classifica uma carteira individual identificando inconformidades
        /// </summary>
        public async Task<PortfolioOutputDto> ClassifyPortfolioAsync(PortfolioInputDto portfolioInput)
        {
            _logger.LogInformation("Starting portfolio classification for account {AccountId}, portfolio {PortfolioId}",
                portfolioInput.AccountId, portfolioInput.PortfolioId);

            try
            {
                // 1. Buscar cliente no banco
                var client = await _context.Clients.FindAsync(portfolioInput.AccountId);
                if (client == null)
                {
                    throw new ArgumentException($"Client {portfolioInput.AccountId} not found");
                }

                // 2. Análise SVD simulada
                var svdConfidence = 0.729m;
                var predictedProfile = "Moderado";

                // 3. Detectar inconformidades
                var inconsistencies = new List<InconsistencyDto>();
                foreach (var asset in portfolioInput.Assets)
                {
                    if (!IsRiskAllowedForProfile(portfolioInput.PortfolioProfile, asset.InvestmentProfile))
                    {
                        inconsistencies.Add(new InconsistencyDto
                        {
                            AssetId = asset.AssetId,
                            Name = asset.Name,
                            Description = $"Ativo {asset.InvestmentProfile} incompatível com perfil {portfolioInput.PortfolioProfile}",
                            Severity = "high"
                        });
                    }
                }

                // 4. Calcular alocação
                var totalValue = portfolioInput.Assets.Sum(a => a.Value);
                var fixedIncomeValue = portfolioInput.Assets
                    .Where(a => a.IncomeType == "Renda Fixa")
                    .Sum(a => a.Value);
                
                var fixedIncomeRatio = totalValue > 0 ? fixedIncomeValue / totalValue : 1.0m;

                // 5. Determinar compliance
                var hasHighSeverityIssues = inconsistencies.Any(i => i.Severity == "high");
                var isCompliant = !hasHighSeverityIssues;

                // 6. Atualizar cliente no banco
                await UpdateClientAsync(portfolioInput.AccountId, predictedProfile, isCompliant);

                // 7. Criar resultado
                var result = new PortfolioOutputDto
                {
                    AccountId = portfolioInput.AccountId,
                    PortfolioId = portfolioInput.PortfolioId,
                    PortfolioProfile = predictedProfile,
                    Inconsistencies = inconsistencies,
                    IncomeAllocation = new IncomeAllocationDto
                    {
                        FixedIncome = fixedIncomeRatio,
                        VariableIncome = 1.0m - fixedIncomeRatio
                    }
                };

                // 8. Chamar serviço de recomendação
                try
                {
                    await _recommendationClient.SendPortfolioAnalysisAsync(result);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to call recommendation service for client {ClientId}", portfolioInput.AccountId);
                }

                _logger.LogInformation("Portfolio classification completed for account {AccountId}. Found {Count} inconsistencies",
                    portfolioInput.AccountId, inconsistencies.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error classifying portfolio for account {AccountId}", portfolioInput.AccountId);
                throw;
            }
        }

        /// <summary>
        /// Classifica todas as carteiras do sistema em lote
        /// </summary>
        public async Task<object> ClassifyAllPortfoliosAsync()
        {
            _logger.LogInformation("Starting batch portfolio classification");

            var startTime = DateTime.UtcNow;
            var processedCount = 0;
            var totalInconsistencies = 0;

            try
            {
                // Buscar todos os clientes
                var clients = await _context.Clients.ToListAsync();

                foreach (var client in clients)
                {
                    try
                    {
                        // Buscar investimentos do cliente
                        var clientInvestments = await _context.ClientInvestments
                            .Where(ci => ci.ClientId == client.ClientId)
                            .ToListAsync();

                        // Criar portfolio simulado
                        var assets = clientInvestments.Select(ci => new AssetInputDto
                        {
                            AssetId = $"asset_{ci.InvestmentId}",
                            Name = $"Investment {ci.InvestmentId}",
                            Type = "bond",
                            IncomeType = "Renda Fixa",
                            InvestmentProfile = "Moderado",
                            Quantity = ci.Quantity,
                            Value = ci.InvestedAmount
                        }).ToList();

                        // Se não há investimentos, criar um ativo fictício
                        if (!assets.Any())
                        {
                            assets.Add(new AssetInputDto
                            {
                                AssetId = $"default_asset_{client.ClientId}",
                                Name = "Carteira Vazia",
                                Type = "cash",
                                IncomeType = "Renda Fixa",
                                InvestmentProfile = "Conservador",
                                Quantity = 1,
                                Value = 1000.00m
                            });
                        }

                        var portfolioInput = new PortfolioInputDto
                        {
                            AccountId = client.ClientId,
                            PortfolioId = $"portfolio_{client.ClientId}",
                            PortfolioProfile = client.RiskProfileForm ?? "Moderado",
                            Assets = assets
                        };

                        // Processar carteira
                        var result = await ClassifyPortfolioAsync(portfolioInput);
                        totalInconsistencies += result.Inconsistencies.Count;
                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing client {ClientId} in batch classification", client.ClientId);
                    }
                }

                var duration = DateTime.UtcNow - startTime;

                _logger.LogInformation("Batch portfolio classification completed for {Count} clients", processedCount);

                return new
                {
                    message = "Batch classification completed successfully",
                    timestamp = DateTime.UtcNow,
                    processedClients = processedCount,
                    totalInconsistencies = totalInconsistencies,
                    executionTimeMs = duration.TotalMilliseconds
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in batch portfolio classification");
                throw;
            }
        }

        /// <summary>
        /// Verifica se um risco é permitido para um perfil específico
        /// </summary>
        public bool IsRiskAllowedForProfile(string profile, string risk)
        {
            var riskRules = new Dictionary<string, string[]>
            {
                { "Conservador", new[] { "Conservador" } },
                { "Moderado", new[] { "Conservador", "Moderado" } },
                { "Arrojado", new[] { "Conservador", "Moderado", "Arrojado" } },
                { "Sofisticado", new[] { "Conservador", "Moderado", "Arrojado", "Sofisticado" } }
            };

            return riskRules.ContainsKey(profile) && riskRules[profile].Contains(risk);
        }

        /// <summary>
        /// Obtém as proporções ideais de Renda Fixa e Renda Variável para um perfil
        /// </summary>
        public (decimal FixedIncome, decimal VariableIncome) GetIdealProportions(string profile)
        {
            var idealProportions = new Dictionary<string, (decimal FixedIncome, decimal VariableIncome)>
            {
                { "Conservador", (0.9m, 0.1m) },
                { "Moderado", (0.6m, 0.4m) },
                { "Arrojado", (0.3m, 0.7m) },
                { "Sofisticado", (0.2m, 0.8m) }
            };

            return idealProportions.GetValueOrDefault(profile, (0.6m, 0.4m));
        }

        private async Task UpdateClientAsync(int clientId, string riskProfile, bool isCompliant)
        {
            try
            {
                var client = await _context.Clients.FindAsync(clientId);
                if (client == null) return;

                var previousCompliance = !client.NonCompliance;

                client.RiskProfileWallet = riskProfile;
                client.NonCompliance = !isCompliant;

                if (previousCompliance != isCompliant)
                {
                    var history = new Domain.ComplianceHistory(clientId, previousCompliance, isCompliant);
                    _context.ComplianceHistory.Add(history);
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating client {ClientId}", clientId);
            }
        }
    }
}
