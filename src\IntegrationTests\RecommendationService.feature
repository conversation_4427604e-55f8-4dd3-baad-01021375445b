Feature: Geração de recomendação baseada em dados do banco

  Como sistema de recomendação,
  Quero gerar recomendações para um usuário com base nos dados armazenados no banco,
  Para garantir que a recomendação seja precisa e os dados sejam recuperados corretamente.

 Cenário: Gerar recomendação para cliente inconforme existente no banco
    Given o banco de dados está limpo e contém um cliente inconforme com CPF "12345678901" e investimentos válidos
    When eu solicito a geração de recomendação para o cliente com CPF "12345678901"
    Then a recomendação deve ser retornada com sucesso e conter sugestões baseadas nos dados do banco
    And nenhuma falha de conexão com o banco deve ocorrer