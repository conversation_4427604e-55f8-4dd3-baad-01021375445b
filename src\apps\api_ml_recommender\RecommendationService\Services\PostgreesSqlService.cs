using ApiMlRecommender.DTOs;
using Microsoft.Extensions.Configuration;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ApiMlRecommender.Services
{
    public class PostgreSqlService
    {
        private readonly string _connectionString;

        public PostgreSqlService(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("PostgreSql") 
                ?? throw new ArgumentNullException("PostgreSql connection string not found.");
        }

        public async Task<List<RecomendacaoRequestDto>> GetCarteirasInconformesAsync(string? clientId = null)
        {
            var result = new List<RecomendacaoRequestDto>();

            using var conn = new NpgsqlConnection(_connectionString);
            await conn.OpenAsync();

            var query = @"
                SELECT 
                    c.client_id AS Cpf,
                    c.name AS Nome,
                    c.risk_profile_form AS PerfilRisco,
                    'inconforme' AS ComplianceStatus,
                    ci.investment_id AS Codigo,
                    i.name AS NomeAtivo,
                    i.asset_type AS TipoAtivo,
                    i.risk_level AS RiskProfile,
                    ci.current_value AS ValorAtual
                FROM vw_non_compliant_clients vnc
                JOIN client c ON vnc.client_id = c.client_id
                JOIN client_investment ci ON c.client_id = ci.client_id
                JOIN investment i ON ci.investment_id = i.investment_id
                WHERE @clientId IS NULL OR c.client_id = @clientId";

            using var cmd = new NpgsqlCommand(query, conn);
            cmd.Parameters.AddWithValue("clientId", string.IsNullOrEmpty(clientId) ? DBNull.Value : clientId);

            using var reader = await cmd.ExecuteReaderAsync();

            var carteiras = new Dictionary<string, RecomendacaoRequestDto>();
            while (await reader.ReadAsync())
            {
                var cpf = reader.GetString(0);
                if (!carteiras.TryGetValue(cpf, out var dto))
                {
                    dto = new RecomendacaoRequestDto
                    {
                        Cpf = cpf,
                        Nome = reader.GetString(1),
                        PerfilRisco = reader.GetString(2),
                        ComplianceStatus = reader.GetString(3),
                        Investimentos = new List<InvestimentoDto>()
                    };
                    carteiras[cpf] = dto;
                    result.Add(dto);
                }

                dto.Investimentos.Add(new InvestimentoDto
                {
                    Codigo = reader.GetString(4),
                    Nome = reader.GetString(5),
                    TipoAtivo = reader.GetString(6),
                    NivelRisco = CalcularNivelRisco(reader.GetString(7)),
                    ValorAtual = reader.GetDecimal(8),
                    PercentualCarteira = 0
                });
            }

            foreach (var dto in result)
            {
                CalcularPercentuaisCarteira(dto.Investimentos);
            }

            return result;
        }

        private int CalcularNivelRisco(string risco)
        {
            return risco.ToUpper() switch
            {
                "CONSERVADOR" => 1,
                "MODERADO" => 3,
                "ARROJADO" => 4,
                "SOFISTICADO" => 5,
                _ => 2
            };
        }

        private void CalcularPercentuaisCarteira(List<InvestimentoDto> investimentos)
        {
            var valorTotal = investimentos.Sum(i => i.ValorAtual);
            if (valorTotal > 0)
            {
                foreach (var investimento in investimentos)
                {
                    investimento.PercentualCarteira = Math.Round((investimento.ValorAtual / valorTotal) * 100, 2);
                }
            }
        }

        public async Task<bool> CheckConnectionAsync()
        {
            try
            {
                using var conn = new NpgsqlConnection(_connectionString);
                await conn.OpenAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}