using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using Pati.ClassificationService.Application.Services;
using Pati.ClassificationService.DTOs;
using Pati.ClassificationService.Domain;
using Pati.ClassificationService.Infrastructure.Data;
using Pati.ClassificationService.Infrastructure.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System;
using System.Net.Http;

namespace Pati.ClassificationService.Tests
{
    /// <summary>
    /// Testes unitários para o ClassificationService refatorado
    /// </summary>
    public class ClassificationServiceTests
    {
        private readonly Mock<ILogger<Application.Services.ClassificationService>> _mockLogger;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVDModelService> _mockSvdService;
        private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly ClassificationDbContext _context;
        private readonly Application.Services.ClassificationService _classificationService;

        public ClassificationServiceTests()
        {
            _mockLogger = new Mock<ILogger<Application.Services.ClassificationService>>();
            _mockMapper = new Mock<IMapper>();
            _mockSvdService = new Mock<ISVDModelService>();
            _mockHttpClientFactory = new Mock<IHttpClientFactory>();
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup in-memory database
            var options = new DbContextOptionsBuilder<ClassificationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
            _context = new ClassificationDbContext(options);

            _classificationService = new Application.Services.ClassificationService(
                _mockLogger.Object,
                _mockMapper.Object,
                _mockSvdService.Object,
                _mockHttpClientFactory.Object,
                _context,
                _mockConfiguration.Object
            );

            SeedTestData();
        }

        [Fact]
        public async Task ClassifyPortfolioAsync_ValidInput_ReturnsPortfolioOutput()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            var portfolioData = new PortfolioData { ClientId = 1, PortfolioProfile = "Moderado" };
            var prediction = new RiskProfilePrediction { RiskProfile = "Moderado", Score = new[] { 0.8f } };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync(prediction);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(portfolioInput.AccountId, result.AccountId);
            Assert.Equal(portfolioInput.PortfolioId, result.PortfolioId);
        }

        [Fact]
        public async Task ClassifyPortfolioAsync_ClientNotFound_ThrowsArgumentException()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            portfolioInput.AccountId = 999; // Non-existent client

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => 
                _classificationService.ClassifyPortfolioAsync(portfolioInput));
        }

        [Fact]
        public async Task ClassifyPortfolioAsync_UsesPostgreSQLFunctions()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            var portfolioData = new PortfolioData { ClientId = 1, PortfolioProfile = "Moderado" };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync((RiskProfilePrediction?)null);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);
            // Verify that PostgreSQL functions would be called (mocked in real implementation)
        }

        [Fact]
        public async Task ClassifyPortfolioAsync_CallsRecommendationService()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            var portfolioData = new PortfolioData { ClientId = 1, PortfolioProfile = "Moderado" };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync((RiskProfilePrediction?)null);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            _mockHttpClientFactory.Verify(f => f.CreateClient("RecommendationService"), Times.Once);
        }

        [Fact]
        public async Task ClassifyAllPortfoliosAsync_ProcessesAllClients()
        {
            // Arrange
            var portfolioData = new PortfolioData { ClientId = 1, PortfolioProfile = "Moderado" };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync((RiskProfilePrediction?)null);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            await _classificationService.ClassifyAllPortfoliosAsync();

            // Assert
            // Verify that all clients in the database were processed
            var clientCount = await _context.Clients.CountAsync();
            Assert.True(clientCount > 0);
        }

        [Fact]
        public void CalculateCurrentProportions_ValidAssets_ReturnsCorrectProportions()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("ASSET1", "Asset 1", "bond", "Renda Fixa", "Conservador", 10, 1000),
                new Asset("ASSET2", "Asset 2", "stock", "Renda Variável", "Arrojado", 5, 500)
            };

            // Act
            var result = _classificationService.CalculateCurrentProportions(assets);

            // Assert
            Assert.Equal(0.67m, result.FixedIncome, 2);
            Assert.Equal(0.33m, result.VariableIncome, 2);
        }

        [Fact]
        public async Task IdentifyInconsistenciesAsync_WithSVDModel_ReturnsInconsistencies()
        {
            // Arrange
            var portfolio = CreateValidPortfolio();
            var expectedSvdInconsistencies = new List<Inconsistency>
            {
                new Inconsistency("ASSET1", "Asset 1", "SVD detected issue", "medium")
            };

            _mockSvdService.Setup(s => s.IsModelLoaded).Returns(true);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(expectedSvdInconsistencies);

            // Act
            var result = await _classificationService.IdentifyInconsistenciesAsync(portfolio);

            // Assert
            Assert.NotNull(result);
            Assert.Contains(result, i => i.AssetId == "ASSET1");
        }

        [Fact]
        public async Task ValidatePortfolioComplianceAsync_CompliantPortfolio_ReturnsTrue()
        {
            // Arrange
            var portfolio = CreateValidPortfolio();
            _mockSvdService.Setup(s => s.IsModelLoaded).Returns(true);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            // Act
            var result = await _classificationService.ValidatePortfolioComplianceAsync(portfolio);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task ValidatePortfolioComplianceAsync_NonCompliantPortfolio_ReturnsFalse()
        {
            // Arrange
            var portfolio = CreateValidPortfolio();
            var highSeverityInconsistency = new Inconsistency("ASSET1", "Asset 1", "High severity issue", "high");

            _mockSvdService.Setup(s => s.IsModelLoaded).Returns(true);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency> { highSeverityInconsistency });

            // Act
            var result = await _classificationService.ValidatePortfolioComplianceAsync(portfolio);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetPortfolioStatusAsync_ValidPortfolioId_ReturnsStatus()
        {
            // Arrange
            var portfolioId = "portfolio_123";

            // Act
            var result = await _classificationService.GetPortfolioStatusAsync(portfolioId);

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetPortfolioDetailsAsync_ValidPortfolioId_ReturnsDetails()
        {
            // Arrange
            var portfolioId = "portfolio_123";

            // Act
            var result = await _classificationService.GetPortfolioDetailsAsync(portfolioId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(portfolioId, result.PortfolioId);
        }

        [Fact]
        public async Task GetNonCompliantClientsAsync_ReturnsClientList()
        {
            // Act
            var result = await _classificationService.GetNonCompliantClientsAsync();

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetDashboardDataAsync_ReturnsDashboardData()
        {
            // Act
            var result = await _classificationService.GetDashboardDataAsync();

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public void CalculateCurrentProportions_EmptyAssets_ReturnsZero()
        {
            // Arrange
            var assets = new List<Asset>();

            // Act
            var result = _classificationService.CalculateCurrentProportions(assets);

            // Assert
            Assert.Equal(0m, result.FixedIncome);
            Assert.Equal(0m, result.VariableIncome);
        }

        [Fact]
        public void CalculateCurrentProportions_OnlyFixedIncome_ReturnsCorrectRatio()
        {
            // Arrange
            var assets = new List<Asset>
            {
                new Asset("ASSET1", "Asset 1", "bond", "Renda Fixa", "Conservador", 10, 1000),
                new Asset("ASSET2", "Asset 2", "bond", "Renda Fixa", "Conservador", 5, 500)
            };

            // Act
            var result = _classificationService.CalculateCurrentProportions(assets);

            // Assert
            Assert.Equal(1.0m, result.FixedIncome);
            Assert.Equal(0.0m, result.VariableIncome);
        }

        [Fact]
        public async Task ClassifyPortfolioAsync_WithSVDPrediction_UsesPredictedProfile()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            portfolioInput.AccountId = 27; // Cliente conhecido nos dados simulados

            var portfolioData = new PortfolioData { ClientId = 27, PortfolioProfile = "Moderado" };
            var svdPrediction = new RiskProfilePrediction
            {
                RiskProfile = "Conservador",
                Score = new[] { 0.85f },
                Confidence = 0.85f
            };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync(svdPrediction);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Conservador", result.PortfolioProfile); // Should use SVD prediction
            Assert.Equal(27, result.AccountId);
            Assert.Equal("Cliente 27", result.Name); // Should use simulated data
            Assert.Equal("<EMAIL>", result.Email);
        }

        [Fact]
        public async Task ClassifyPortfolioAsync_WithLowSVDConfidence_UsesFallback()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            portfolioInput.AccountId = 55;

            var portfolioData = new PortfolioData { ClientId = 55, PortfolioProfile = "Moderado" };
            var lowConfidencePrediction = new RiskProfilePrediction
            {
                RiskProfile = "Arrojado",
                Score = new[] { 0.3f },
                Confidence = 0.3f // Baixa confiança
            };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync(lowConfidencePrediction);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Moderado", result.PortfolioProfile); // Should use simulated form profile
            Assert.Equal("Cliente 55", result.Name);
        }

        [Fact]
        public async Task ClassifyPortfolioAsync_WithSVDInconsistencies_ReturnsDetailedAnalysis()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            portfolioInput.AccountId = 27;
            portfolioInput.Assets.Add(new AssetInputDto
            {
                AssetId = "RISKY_ASSET",
                Name = "Ativo Arriscado",
                Type = "stock",
                IncomeType = "Renda Variável",
                InvestmentProfile = "Arrojado",
                Quantity = 100,
                Value = 50000
            });

            var portfolioData = new PortfolioData { ClientId = 27, PortfolioProfile = "Conservador" };
            var svdPrediction = new RiskProfilePrediction
            {
                RiskProfile = "Conservador",
                Score = new[] { 0.8f },
                Confidence = 0.8f
            };

            var svdInconsistencies = new List<Inconsistency>
            {
                new Inconsistency("RISKY_ASSET", "Ativo Arriscado",
                    "Modelo SVD indica baixa adequação do ativo para cliente conservador (score: 0.15)", "high")
            };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync(svdPrediction);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(svdInconsistencies);

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Conservador", result.PortfolioProfile);
            Assert.NotEmpty(result.Inconsistencies);
            Assert.Contains(result.Inconsistencies, i => i.AssetId == "RISKY_ASSET" && i.Severity == "high");
        }

        [Fact]
        public void CalculateCurrentProportions_WithMixedAssets_ReturnsCorrectSVDBasedProportions()
        {
            // Arrange - Simular dados baseados no notebook SVD
            var assets = new List<Asset>
            {
                new Asset("TESOURO001", "Tesouro Selic", "bond", "Renda Fixa", "Conservador", 100, 15000),
                new Asset("PETR4001", "Petrobras PN", "stock", "Renda Variável", "Arrojado", 50, 10000),
                new Asset("UNCLASSIFIED", "Ativo Não Classificado", "fund", "", "Moderado", 25, 5000) // Sem incomeType
            };

            // Act
            var proportions = _classificationService.CalculateCurrentProportions(assets);

            // Assert
            Assert.Equal(0.6667m, Math.Round(proportions.FixedIncome, 4)); // 20000 / 30000
            Assert.Equal(0.3333m, Math.Round(proportions.VariableIncome, 4)); // 10000 / 30000

            // Verificar que a soma é exatamente 1.0 (normalização do notebook)
            var total = proportions.FixedIncome + proportions.VariableIncome;
            Assert.True(Math.Abs(total - 1.0m) < 0.001m);
        }

        /// <summary>
        /// Testa se o modelo SVD é carregado e executado corretamente
        /// Valida integração direta com arquivo .pkl
        /// </summary>
        [Fact]
        public async Task ClassifyPortfolioAsync_SVDModelLoaded_UsesDirectPklIntegration()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            portfolioInput.AccountId = 27; // Cliente conhecido do notebook SVD

            var portfolioData = new PortfolioData
            {
                ClientId = 27,
                PortfolioProfile = "Moderado",
                TotalValue = 50000f,
                FixedIncomeRatio = 0.8f,
                VariableIncomeRatio = 0.2f,
                RiskScore = 1.5f
            };

            var svdPrediction = new RiskProfilePrediction
            {
                RiskProfile = "Conservador",
                Score = new[] { 0.85f },
                Confidence = 0.85f
            };

            // Setup SVD service para simular carregamento do modelo .pkl
            _mockSvdService.Setup(s => s.IsModelLoaded).Returns(true);
            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync(svdPrediction);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Conservador", result.PortfolioProfile); // Deve usar predição SVD
            Assert.Equal(27, result.AccountId);

            // Verificar que o modelo SVD foi usado
            _mockSvdService.Verify(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()), Times.Once);
            _mockSvdService.Verify(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()), Times.Once);
        }

        /// <summary>
        /// Testa normalização MinMaxScaler baseada no notebook
        /// Valida que valores são normalizados entre 0 e 1 com máximo de 100,000
        /// </summary>
        [Fact]
        public async Task ClassifyPortfolioAsync_SVDNormalization_UsesMinMaxScaler()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            portfolioInput.AccountId = 89; // Cliente arrojado do notebook
            portfolioInput.Assets = new List<AssetInputDto>
            {
                new AssetInputDto
                {
                    AssetId = "PETR4_001",
                    Name = "Petrobras PN",
                    Type = "stock",
                    IncomeType = "Renda Variável",
                    InvestmentProfile = "Arrojado",
                    Quantity = 100,
                    Value = 80000 // Valor alto para testar normalização
                }
            };

            var portfolioData = new PortfolioData
            {
                ClientId = 89,
                PortfolioProfile = "Arrojado",
                TotalValue = 80000f, // Será normalizado para 0.8 (80000/100000)
                FixedIncomeRatio = 0.0f,
                VariableIncomeRatio = 1.0f,
                RiskScore = 3.0f
            };

            var svdPrediction = new RiskProfilePrediction
            {
                RiskProfile = "Arrojado",
                Score = new[] { 0.95f },
                Confidence = 0.95f
            };

            _mockSvdService.Setup(s => s.IsModelLoaded).Returns(true);
            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync(svdPrediction);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Arrojado", result.PortfolioProfile);
            Assert.Equal(89, result.AccountId);

            // Verificar que a normalização foi aplicada corretamente
            _mockSvdService.Verify(s => s.ConvertToPortfolioData(It.Is<Portfolio>(p =>
                p.Assets.Sum(a => a.Value) == 80000)), Times.Once);
        }

        /// <summary>
        /// Testa simulação de dados do cliente baseada no accountId
        /// Valida dados dos clientes de teste (27, 44, 55, 89, 103)
        /// </summary>
        [Theory]
        [InlineData(27, "Cliente 27", "<EMAIL>", "Conservador")]
        [InlineData(44, "Cliente 44", "<EMAIL>", "Sofisticado")]
        [InlineData(55, "Cliente 55", "<EMAIL>", "Moderado")]
        [InlineData(89, "Cliente 89", "<EMAIL>", "Arrojado")]
        [InlineData(103, "Cliente 103", "<EMAIL>", "Conservador")]
        public async Task ClassifyPortfolioAsync_SimulatedClientData_ReturnsCorrectData(
            int accountId, string expectedName, string expectedEmail, string expectedProfile)
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            portfolioInput.AccountId = accountId;

            var portfolioData = new PortfolioData { ClientId = accountId, PortfolioProfile = expectedProfile };
            var svdPrediction = new RiskProfilePrediction
            {
                RiskProfile = expectedProfile,
                Score = new[] { 0.8f },
                Confidence = 0.8f
            };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync(svdPrediction);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(accountId, result.AccountId);
            Assert.Equal(expectedName, result.Name);
            Assert.Equal(expectedEmail, result.Email);
            Assert.Equal(expectedProfile, result.PortfolioProfile);
        }

        /// <summary>
        /// Testa que inconsistências específicas do SVD são detectadas
        /// Valida mensagens baseadas nas previsões do modelo
        /// </summary>
        [Fact]
        public async Task ClassifyPortfolioAsync_SVDInconsistencies_ReturnsSpecificMessages()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            portfolioInput.AccountId = 27; // Cliente conservador
            portfolioInput.Assets.Add(new AssetInputDto
            {
                AssetId = "PETR4_001",
                Name = "Petrobras PN",
                Type = "stock",
                IncomeType = "Renda Variável",
                InvestmentProfile = "Arrojado",
                Quantity = 100,
                Value = 25000 // 50% da carteira em ativo arriscado
            });

            var portfolioData = new PortfolioData { ClientId = 27, PortfolioProfile = "Conservador" };
            var svdPrediction = new RiskProfilePrediction
            {
                RiskProfile = "Conservador",
                Score = new[] { 0.85f },
                Confidence = 0.85f
            };

            // SVD detecta incompatibilidade específica
            var svdInconsistencies = new List<Inconsistency>
            {
                new Inconsistency("PETR4_001", "Petrobras PN",
                    "Modelo SVD indica baixa adequação do ativo Petrobras PN para o cliente (score: 0.15)", "high")
            };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync(svdPrediction);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(svdInconsistencies);

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result.Inconsistencies);

            var svdInconsistency = result.Inconsistencies.FirstOrDefault(i => i.AssetId == "PETR4_001");
            Assert.NotNull(svdInconsistency);
            Assert.Contains("Modelo SVD indica", svdInconsistency.Description);
            Assert.Contains("score: 0.15", svdInconsistency.Description);
            Assert.Equal("high", svdInconsistency.Severity);
        }

        /// <summary>
        /// Testa cálculo correto de incomeAllocation baseado no SVD
        /// Valida que proporções refletem os valores dos ativos
        /// </summary>
        [Fact]
        public async Task ClassifyPortfolioAsync_IncomeAllocation_ReflectsSVDPredictions()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            portfolioInput.AccountId = 27;
            portfolioInput.Assets = new List<AssetInputDto>
            {
                new AssetInputDto
                {
                    AssetId = "TESOURO_SELIC_001",
                    Name = "Tesouro Selic",
                    Type = "bond",
                    IncomeType = "Renda Fixa",
                    InvestmentProfile = "Conservador",
                    Quantity = 100,
                    Value = 40000 // 80% da carteira
                },
                new AssetInputDto
                {
                    AssetId = "IGTI11_001",
                    Name = "IGTI11",
                    Type = "fund",
                    IncomeType = "Renda Variável",
                    InvestmentProfile = "Arrojado",
                    Quantity = 50,
                    Value = 10000 // 20% da carteira
                }
            };

            var portfolioData = new PortfolioData { ClientId = 27, PortfolioProfile = "Conservador" };
            var svdPrediction = new RiskProfilePrediction
            {
                RiskProfile = "Conservador",
                Score = new[] { 0.85f },
                Confidence = 0.85f
            };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync(svdPrediction);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.IncomeAllocation);

            // Verificar proporções corretas (80% Renda Fixa, 20% Renda Variável)
            Assert.Equal(0.8m, result.IncomeAllocation.FixedIncome);
            Assert.Equal(0.2m, result.IncomeAllocation.VariableIncome);

            // Verificar que a soma é 1.0 (normalização do notebook)
            var total = result.IncomeAllocation.FixedIncome + result.IncomeAllocation.VariableIncome;
            Assert.Equal(1.0m, total);
        }

        /// <summary>
        /// Testa chamada obrigatória ao api_ml_recommender
        /// Valida que o endpoint é chamado após cada classificação
        /// </summary>
        [Fact]
        public async Task ClassifyPortfolioAsync_CallsRecommendationService_Mandatory()
        {
            // Arrange
            var portfolioInput = CreateValidPortfolioInput();
            portfolioInput.AccountId = 27;

            var portfolioData = new PortfolioData { ClientId = 27, PortfolioProfile = "Conservador" };
            var svdPrediction = new RiskProfilePrediction
            {
                RiskProfile = "Conservador",
                Score = new[] { 0.85f },
                Confidence = 0.85f
            };

            _mockSvdService.Setup(s => s.ConvertToPortfolioData(It.IsAny<Portfolio>()))
                          .Returns(portfolioData);
            _mockSvdService.Setup(s => s.PredictRiskProfileAsync(It.IsAny<PortfolioData>()))
                          .ReturnsAsync(svdPrediction);
            _mockSvdService.Setup(s => s.AnalyzePortfolioAsync(It.IsAny<Portfolio>()))
                          .ReturnsAsync(new List<Inconsistency>());

            var mockHttpClient = new Mock<HttpClient>();
            _mockHttpClientFactory.Setup(f => f.CreateClient("RecommendationService"))
                                 .Returns(mockHttpClient.Object);

            // Act
            var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

            // Assert
            Assert.NotNull(result);

            // Verificar que o RecommendationService foi chamado
            _mockHttpClientFactory.Verify(f => f.CreateClient("RecommendationService"), Times.Once);
        }

        private void SeedTestData()
        {
            var client = new Client
            {
                ClientId = 1,
                Name = "Test Client",
                Email = "<EMAIL>",
                RiskProfileForm = "Moderado",
                AdvisorId = 1,
                NonCompliance = false // false significa que está em compliance
            };

            var investment = new Investment
            {
                InvestmentId = 1,
                Name = "Test Investment",
                Type = "bond",
                Risk = "Conservador",
                Price = 100m
            };

            var clientInvestment = new ClientInvestment
            {
                ClientId = 1,
                InvestmentId = 1,
                Quantity = 10,
                InvestedAmount = 1000m,
                InvestmentDate = DateTime.UtcNow,
                Investment = investment
            };

            _context.Clients.Add(client);
            _context.Investments.Add(investment);
            _context.ClientInvestments.Add(clientInvestment);
            _context.SaveChanges();
        }

        private PortfolioInputDto CreateValidPortfolioInput()
        {
            return new PortfolioInputDto
            {
                AccountId = 1,
                PortfolioId = "portfolio_1",
                PortfolioProfile = "Moderado",
                Assets = new List<AssetInputDto>
                {
                    new AssetInputDto
                    {
                        AssetId = "ASSET1",
                        Name = "Asset 1",
                        Type = "bond",
                        IncomeType = "Renda Fixa",
                        InvestmentProfile = "Conservador",
                        Quantity = 10,
                        Value = 1000
                    }
                }
            };
        }

        private Portfolio CreateValidPortfolio()
        {
            var assets = new List<Asset>
            {
                new Asset("ASSET1", "Asset 1", "bond", "Renda Fixa", "Conservador", 10, 1000)
            };

            return new Portfolio(1, "portfolio_1", "Moderado", assets);
        }
    }
}
