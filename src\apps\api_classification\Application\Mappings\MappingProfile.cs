using AutoMapper;
using Pati.ClassificationService.Domain;
using Pati.ClassificationService.DTOs;

namespace Pati.ClassificationService.Application.Mappings
{
    /// <summary>
    /// Perfil de mapeamento AutoMapper para conversão entre DTOs e entidades de domínio
    /// </summary>
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Mapeamento de Asset
            CreateMap<AssetInputDto, Asset>()
                .ConstructUsing(src => new Asset(
                    src.AssetId,
                    src.Name,
                    src.Type,
                    src.IncomeType,
                    src.InvestmentProfile,
                    src.Quantity,
                    src.Value
                ));

            CreateMap<Asset, AssetOutputDto>()
                .ForMember(dest => dest.AssetId, opt => opt.MapFrom(src => src.AssetId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type))
                .ForMember(dest => dest.IncomeType, opt => opt.MapFrom(src => src.IncomeType))
                .ForMember(dest => dest.InvestmentProfile, opt => opt.MapFrom(src => src.InvestmentProfile))
                .ForMember(dest => dest.Quantity, opt => opt.MapFrom(src => src.Quantity))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value));

            // Mapeamento de Portfolio
            CreateMap<PortfolioInputDto, Portfolio>()
                .ConstructUsing((src, context) =>
                {
                    var assets = context.Mapper.Map<List<Asset>>(src.Assets);
                    return new Portfolio(
                        src.AccountId,
                        src.PortfolioId,
                        src.PortfolioProfile,
                        assets
                    );
                });

            CreateMap<Portfolio, PortfolioOutputDto>()
                .ForMember(dest => dest.AccountId, opt => opt.MapFrom(src => src.AccountId))
                .ForMember(dest => dest.PortfolioId, opt => opt.MapFrom(src => src.PortfolioId))
                .ForMember(dest => dest.PortfolioProfile, opt => opt.MapFrom(src => src.PortfolioProfile))
                .ForMember(dest => dest.Assets, opt => opt.MapFrom(src => src.Assets))
                .ForMember(dest => dest.Inconsistencies, opt => opt.Ignore()) // Será mapeado separadamente
                .ForMember(dest => dest.IncomeAllocation, opt => opt.Ignore()); // Será calculado separadamente

            // Mapeamento de Inconsistency
            CreateMap<Inconsistency, InconsistencyDto>()
                .ForMember(dest => dest.AssetId, opt => opt.MapFrom(src => src.AssetId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.Severity, opt => opt.MapFrom(src => src.Severity));

            // Mapeamento de IncomeAllocation
            CreateMap<(decimal FixedIncome, decimal VariableIncome), IncomeAllocationDto>()
                .ForMember(dest => dest.FixedIncome, opt => opt.MapFrom(src => src.FixedIncome))
                .ForMember(dest => dest.VariableIncome, opt => opt.MapFrom(src => src.VariableIncome));
        }

    }
}
