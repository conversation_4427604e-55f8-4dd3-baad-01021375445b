name: Validação de nome de branch

on:
  pull_request:
    branches:
      - "*"

jobs:
  check-branch-name:
    runs-on: ubuntu-latest

    steps:
      - name: Verificar nome da branch
        run: |
          BRANCH_NAME="${{ github.head_ref }}"
          echo "Nome da branch: $BRANCH_NAME"

          PATTERN="^(feature|bugfix|release|hotfix|docs|test)/[^[:space:]]+$|^develop$"

          if [[ ! "$BRANCH_NAME" =~ $PATTERN ]]; then
            echo "🚨 Nome de branch inválido: '$BRANCH_NAME'."
            echo "👉 O nome deve ser:"
            echo "   - 'feature/<nome>'"
            echo "   - 'bugfix/<nome>'"
            echo "   - 'release/<nome>'"
            echo "   - 'hotfix/<nome>'"
            echo "   - 'docs/<nome>'"
            echo "   - 'test/<nome>'"
            echo "   - ou apenas 'develop'"
            exit 1
          else
            echo "✅ Nome da branch válido."
          fi
