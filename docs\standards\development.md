<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="../assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>

# Nome do Projeto: Pati (Plataforma de Adequação de Tipo de Investidor)

## Nome do Grupo: MMs (Meninas Malvadas)

## Integrantes:

<div align="center">
<table>
  <tr>
    <td align="center">
      <a href="https://www.linkedin.com/in/larissa-temoteo/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQE-yMLLUD04Qg/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1730056625827?e=1750896000&v=beta&t=Vxukmk-nWK9EjbZGD4zQ0IIi5se0JwECJLmyqZ-2mrg" width="100px;" alt="Foto de Larissa Temoteo" style="border-radius:50%"/>
        <br />
        <b>Larissa Temoteo</b>
      </a>
      <br />
      <a href="https://github.com/larissatemoteo">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/larissa-temoteo/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/lucas-nunes-matheus/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHN4SR2WsAIdA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1710163486566?e=1750896000&v=beta&t=o-9q_kscwkEexlcm92Cobx197j0MsiztrpiTQgiJ9Kg" width="100px;" alt="Foto de Lucas Matheus Nunes" style="border-radius:50%"/>
        <br />
        <b>Lucas Matheus Nunes</b>
      </a>
      <br />
      <a href="https://github.com/lucas-nunes-matheus">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/lucas-nunes-matheus/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/rafael-furtado-b30715265/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQGfsjSmMmtAsw/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1709258479259?e=1750896000&v=beta&t=oIehlqkG2dGqrV8ya_JukCvuBgTEs-q7i32Oen49fdQ" width="100px;" alt="Foto de Rafael Furtado" style="border-radius:50%"/>
        <br />
        <b>Rafael Furtado</b>
      </a>
      <br />
      <a href="https://github.com/Rafaelfurtadovs">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/rafael-furtado-b30715265/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
  <tr>
    <td align="center">
      <a href="https://www.linkedin.com/in/ryan-botelho-gartlan/">
        <img src="https://media.licdn.com/dms/image/v2/D5603AQGy5KTEKUM2pA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1728396516687?e=1750896000&v=beta&t=LjSCsFve87n2F4J7v-LzbwHHytG4SJnTxTigdBhVlUU" width="100px;" alt="Foto de Ryan Gartlan" style="border-radius:50%"/>
        <br />
        <b>Ryan Gartlan</b>
      </a>
      <br />
      <a href="https://github.com/ryanbotgar">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/ryan-botelho-gartlan/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/tainacortez/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHV4IOZvu7n3A/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1713277689597?e=1750896000&v=beta&t=fgvCFEght43z8dmT0PhJgj1Lg5VtXoCjZie0pNPujxA" width="100px;" alt="Foto de Tainá Cortez" style="border-radius:50%"/>
        <br />
        <b>Tainá Cortez</b>
      </a>
      <br />
      <a href="https://github.com/taicortezz">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/tainacortez/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/thiagogomesalmeida/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHh3rHCD36uKA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1711828725384?e=1750896000&v=beta&t=Kggq5QNqIQ66GqL7_dT37fq5YO3NQAGBwX9BF0Fq8oU" width="100px;" alt="Foto de Thiago Gomes" style="border-radius:50%"/>
        <br />
        <b>Thiago Gomes</b>
      </a>
      <br />
      <a href="https://github.com/thiagomes07">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/thiagogomesalmeida/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
  <tr>
    <td colspan="3" align="center">
      <a href="https://www.linkedin.com/in/viniciussavian/">
        <img src="https://media.licdn.com/dms/image/v2/D4E03AQFsD6PLB2Du0w/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1710417108475?e=1750896000&v=beta&t=Qbzx-PMJMrTRJlEtE_NYRwVfMfOyY7Nf-duVxKugTmk" width="100px;" alt="Foto de Vinicius Savian" style="border-radius:50%"/>
        <br />
        <b>Vinicius Savian</b>
      </a>
      <br />
      <a href="https://github.com/ViniciusSavian">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/viniciussavian/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
</table>
</div>

<br/>

# Sumário

- [1. Introdução](#1-introdução)
- [2. Padrões de Codificação](#2-padrões-de-codificação)
  - [2.1 Naming Conventions](#21-naming-conventions)
  - [2.2 Uso de Ferramentas para Padronização de Código](#22-uso-de-ferramentas-para-padronização-de-código)
  - [2.3 Comentários e Documentação](#23-comentários-e-documentação)
- [3. Padrões de Projeto](#3-padrões-de-projeto)
  - [3.1 Design Patterns](#31-design-patterns)
  - [3.2 Arquitetura de Software](#32-arquitetura-de-software)
- [4. Práticas de Desenvolvimento](#4-práticas-de-desenvolvimento)
  - [4.1 Testes Automatizados](#41-testes-automatizados)
  - [4.2 Controle de Qualidade](#42-controle-de-qualidade)
- [5. Ferramentas e Tecnologias](#5-ferramentas-e-tecnologias)
- [6. Infraestrutura como Código](#6-ferramentas-e-tecnologias)
- [7. Gestão de Arquivos Sensíveis](#7-ferramentas-e-tecnologias)
- [8. Registro de Variáveis de Ambiente](#8-ferramentas-e-tecnologias)

# 1. Introdução

&emsp; Este documento tem como objetivo estabelecer um conjunto de padrões e diretrizes para o desenvolvimento de software, garantindo a qualidade, a manutenibilidade e a consistência do código. A adesão a esses padrões melhora a legibilidade do código, facilita a colaboração entre os desenvolvedores e reduz o tempo de depuração e manutenção.

&emsp; A definição de convenções permite que o time trabalhe de maneira organizada e produtiva, minimizando ambiguidades e promovendo boas práticas. Este documento também enfatiza o uso de ferramentas automatizadas para garantir conformidade com os padrões de código.

# 2. Padrões de Codificação

## 2.1 Naming Conventions

- Variáveis e Funções: Utilize camelCase e nomes em inglês

  - Exemplos:
    - `let userName = "John";`
    - `function calculateTotal() { ... }`

- Classes e Interfaces: Utilize PascalCase e nomes em inglês

  - Exemplos:
    - `class UserProfile { ... }`
    - `interface PaymentGateway { ... }`

- Constantes: Utilize UPPER_CASE com underscores e nomes em inglês
  - Exemplo:
    - `const MAX_USERS = 100;`

## 2.2 Uso de Ferramentas para Padronização de Código

&emsp; Embora a padronização de escrita de código seja fundamental, é altamente recomendável utilizar ferramentas automáticas para garantir a conformidade com boas práticas.

&emsp; A análise estática de código e a formatação automática, através de ferramentas como ESLint e Prettier (para JavaScript/TypeScript) ou equivalentes para outras linguagens, reduzem a necessidade de revisão manual e evitam erros comuns. Essas ferramentas podem ser configuradas para aplicar automaticamente os padrões mais atualizados do ECMAScript (ou padrões equivalentes da linguagem utilizada). Dessa forma, a revisão de código pode se concentrar em aspectos lógicos e estruturais, ao invés de formatação.

&emsp; Recomenda-se que os desenvolvedores habilitem essas ferramentas em seus editores de código para garantir um ambiente de desenvolvimento consistente e eficiente.

## 2.3 Comentários e Documentação

- Comentários de Linha: Recomenda-se que sejam escritos em inglês, mas não é obrigatório. Devem ser usados para explicar lógica complexa.

  - Exemplo: `// Calculates the total with discount`

- Comentários de Bloco: Utilize JSDoc para documentar funções e classes.

  - Exemplo:
    ```
    /**
    * Calculates the total amount of an order.
    * @param {number} price - Price per item.
    * @param {number} quantity - Quantity of items.
    * @returns {number} Total calculated.
    */
    function calculateTotal(price, quantity) {
        return price * quantity;
    }
    ```

- Documentação da API:
  - Utilizar SWAGGER para documentação de APIs a partir dos comentários do código.
  - Configurar uma rota, como `/api-docs`, para que a documentação seja acessível aos desenvolvedores e stakeholders.

# 3. Padrões de Projeto

## 3.1 Design Patterns

### Padrão Repository
- **Descrição**: Separa a lógica de acesso a dados da lógica de negócio, fornecendo uma interface uniforme para acessar dados.
- **Exemplo**:
  ```csharp
  public interface IClientRepository
  {
      Task<Client> GetByIdAsync(int id);
      Task<IEnumerable<Client>> GetAllAsync();
      Task AddAsync(Client client);
      Task UpdateAsync(Client client);
      Task DeleteAsync(int id);
  }

  public class ClientRepository : IClientRepository
  {
      private readonly DbContext _context;

      public ClientRepository(DbContext context) => _context = context;

      public async Task<Client> GetByIdAsync(int id) =>
          await _context.Clients.FindAsync(id);

      public async Task<IEnumerable<Client>> GetAllAsync() =>
          await _context.Clients.ToListAsync();
  }
  ```

### Padrão Unit of Work
- **Descrição**: Mantém uma lista de objetos afetados por uma transação de negócio e coordena a escrita de mudanças.
- **Exemplo**:
  ```csharp
  public interface IUnitOfWork : IDisposable
  {
      IClientRepository Clients { get; }
      IInvestmentRepository Investments { get; }
      Task<int> SaveChangesAsync();
  }

  public class UnitOfWork : IUnitOfWork
  {
      private readonly DbContext _context;

      public UnitOfWork(DbContext context)
      {
          _context = context;
          Clients = new ClientRepository(_context);
          Investments = new InvestmentRepository(_context);
      }

      public IClientRepository Clients { get; }
      public IInvestmentRepository Investments { get; }

      public async Task<int> SaveChangesAsync() =>
          await _context.SaveChangesAsync();
  }
  ```

### Injeção de Dependência
- **Descrição**: Técnica para alcançar Inversão de Controle entre classes e suas dependências.
- **Exemplo**:
  ```csharp
  // Configuração no Program.cs
  builder.Services.AddScoped<IClientRepository, ClientRepository>();
  builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
  builder.Services.AddScoped<IClientService, ClientService>();

  // Uso no Controller
  [ApiController]
  [Route("api/[controller]")]
  public class ClientsController : ControllerBase
  {
      private readonly IClientService _clientService;

      public ClientsController(IClientService clientService)
      {
          _clientService = clientService;
      }

      [HttpGet("{id}")]
      public async Task<ActionResult<Client>> GetClient(int id)
      {
          var client = await _clientService.GetByIdAsync(id);
          return client == null ? NotFound() : Ok(client);
      }
  }
  ```

## 3.2 Arquitetura de Software

### Clean Architecture
- **Descrição**: Utilize Clean Architecture para separar responsabilidades em camadas bem definidas.
- **Estrutura de Camadas**:
  - **Domain**: Entidades de negócio e regras de domínio
  - **Application**: Casos de uso e lógica de aplicação
  - **Infrastructure**: Acesso a dados, APIs externas, frameworks
  - **Presentation**: Controllers, DTOs, validações de entrada

- **Exemplo de Estrutura**:
  ```
  src/
  ├── Domain/
  │   ├── Entities/
  │   ├── Interfaces/
  │   └── ValueObjects/
  ├── Application/
  │   ├── Services/
  │   ├── DTOs/
  │   └── Interfaces/
  ├── Infrastructure/
  │   ├── Data/
  │   ├── Repositories/
  │   └── ExternalServices/
  └── Presentation/
      ├── Controllers/
      ├── Models/
      └── Validators/
  ```

- **Arquitetura de Três Camadas**: Separe a lógica de negócios (Camada de Negócios), a interface do usuário (Camada de Apresentação) e o acesso aos dados (Camada de Dados).
  - Exemplo: Em uma aplicação .NET, utilize serviços para lógica de negócios, controllers para a interface e repositórios para gerenciar o acesso aos dados.

## 3.3 Estrutura de Diretórios

### Estrutura Recomendada (Clean Architecture)
```
src/
├── services/
│   └── nome-do-servico/
│       ├── src/
│       │   ├── Controllers/          # Camada de Apresentação
│       │   ├── Services/             # Camada de Aplicação
│       │   ├── Interfaces/           # Contratos e abstrações
│       │   ├── Models/               # Entidades e DTOs
│       │   ├── Repositories/         # Camada de Dados
│       │   └── Infrastructure/       # Configurações e dependências externas
│       ├── tests/
│       │   ├── UnitTests/
│       │   ├── IntegrationTests/
│       │   └── EndToEndTests/
│       └── docs/
├── database/
│   ├── migrations/
│   ├── scripts/
│   └── docs/
└── frontend/
    ├── src/
    │   ├── components/
    │   ├── screens/
    │   ├── services/
    │   └── tests/
    └── docs/
```

### Interfaces de Serviços (Obrigatório)
Todos os serviços devem implementar interfaces para facilitar testes e manutenibilidade:

```csharp
// Exemplo: Interface de Serviço
namespace Services.Interfaces
{
    public interface IClientService
    {
        Task<Client> GetClientByIdAsync(int id);
        Task<IEnumerable<Client>> GetAllClientsAsync();
        Task<Client> AddClientAsync(Client client);
        Task<Client> UpdateClientAsync(Client client);
        Task<bool> DeleteClientAsync(int id);
        Task<bool> ValidateClientComplianceAsync(int clientId);
    }
}

// Implementação do Serviço
namespace Services.Implementation
{
    public class ClientService : IClientService
    {
        private readonly IClientRepository _clientRepository;
        private readonly ILogger<ClientService> _logger;

        public ClientService(IClientRepository clientRepository, ILogger<ClientService> logger)
        {
            _clientRepository = clientRepository;
            _logger = logger;
        }

        public async Task<Client> GetClientByIdAsync(int id)
        {
            _logger.LogInformation("Retrieving client with ID: {ClientId}", id);
            return await _clientRepository.GetByIdAsync(id);
        }

        // Outras implementações...
    }
}
```

### Estratégias para Evitar Duplicação de Código

#### 1. Classes Base Abstratas
```csharp
public abstract class BaseService<T> where T : class
{
    protected readonly IRepository<T> _repository;
    protected readonly ILogger _logger;

    protected BaseService(IRepository<T> repository, ILogger logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public virtual async Task<T> GetByIdAsync(int id)
    {
        _logger.LogInformation("Retrieving entity with ID: {Id}", id);
        return await _repository.GetByIdAsync(id);
    }
}
```

#### 2. Métodos de Extensão
```csharp
public static class ValidationExtensions
{
    public static bool IsValidEmail(this string email)
    {
        return !string.IsNullOrEmpty(email) &&
               new EmailAddressAttribute().IsValid(email);
    }

    public static bool IsValidRiskProfile(this string profile)
    {
        var validProfiles = new[] { "Conservador", "Moderado", "Arrojado" };
        return validProfiles.Contains(profile);
    }
}
```

#### 3. Helpers e Utilitários
```csharp
public static class DateTimeHelper
{
    public static DateTime ToUtc(this DateTime dateTime)
    {
        return dateTime.Kind == DateTimeKind.Utc ? dateTime : dateTime.ToUniversalTime();
    }

    public static bool IsBusinessDay(this DateTime date)
    {
        return date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday;
    }
}
```

# 4. Práticas de Desenvolvimento

## 4.1 Testes Automatizados

- Testes Unitários: Utilize Jest para criar testes unitários
- Testes de Integração e End-to-End: Utilizar Cypress

## 4.2 Controle de Qualidade

- Revisão de Código: Todos os commits devem ser revisados por pelo menos um outro desenvolvedor.
- Sugestão: Utilizar ESLint para análise estática de código.

## 4.3 Tratamento de Erros

### Validação de Entrada com FluentValidation
```csharp
public class ClientValidator : AbstractValidator<Client>
{
    public ClientValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email é obrigatório")
            .EmailAddress().WithMessage("Email deve ter formato válido");

        RuleFor(x => x.RiskProfileForm)
            .NotEmpty().WithMessage("Perfil de risco é obrigatório")
            .Must(BeValidRiskProfile).WithMessage("Perfil de risco deve ser 'Conservador', 'Moderado' ou 'Arrojado'");
    }

    private bool BeValidRiskProfile(string profile)
    {
        var validProfiles = new[] { "Conservador", "Moderado", "Arrojado" };
        return validProfiles.Contains(profile);
    }
}
```

### Tratamento de Exceções nos Controllers
```csharp
[ApiController]
[Route("api/[controller]")]
public class ClientsController : ControllerBase
{
    private readonly IClientService _clientService;

    public ClientsController(IClientService clientService)
    {
        _clientService = clientService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateClient([FromBody] Client client)
    {
        try
        {
            var validator = new ClientValidator();
            var validationResult = await validator.ValidateAsync(client);

            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.Errors);
            }

            var createdClient = await _clientService.CreateClientAsync(client);
            return CreatedAtAction(nameof(GetClient), new { id = createdClient.Id }, createdClient);
        }
        catch (ValidationException ex)
        {
            return BadRequest(new { message = "Dados inválidos", errors = ex.Errors });
        }
        catch (DuplicateEmailException ex)
        {
            return Conflict(new { message = "Email já cadastrado", email = ex.Email });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro interno ao criar cliente");
            return StatusCode(500, new { message = "Erro interno do servidor" });
        }
    }
}
```

## 4.4 Logging Estruturado

### Configuração do Serilog
```csharp
// Program.cs
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
    .Enrich.FromLogContext()
    .Enrich.WithProperty("Application", "PATI")
    .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
    .WriteTo.File("logs/pati-.log", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();
```

### Padrões de Logging
```csharp
public class ClientService : IClientService
{
    private readonly ILogger<ClientService> _logger;

    public async Task<Client> GetClientByIdAsync(int id)
    {
        using (_logger.BeginScope("GetClientById {ClientId}", id))
        {
            _logger.LogInformation("Iniciando busca do cliente");

            try
            {
                var client = await _clientRepository.GetByIdAsync(id);

                if (client == null)
                {
                    _logger.LogWarning("Cliente não encontrado");
                    return null;
                }

                _logger.LogInformation("Cliente encontrado com sucesso");
                return client;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar cliente");
                throw;
            }
        }
    }
}
```

### Níveis de Log Recomendados
- **Debug**: Informações detalhadas para desenvolvimento
- **Information**: Fluxo geral da aplicação, início/fim de operações
- **Warning**: Situações inesperadas que não impedem o funcionamento
- **Error**: Erros que impedem a operação mas não quebram a aplicação
- **Critical**: Erros que podem quebrar a aplicação

## 4.3 Padrões de Gitflow

### Conventional Commits
- **Descrição**: Padrão para mensagens de commit que facilita a geração automática de changelogs e versionamento semântico.
- **Estrutura**: `<tipo>: <descrição>`

### Tipos de Commit Permitidos
- **feat**: Nova funcionalidade para o usuário
- **fix**: Correção de bug
- **docs**: Mudanças na documentação
- **style**: Formatação, ponto e vírgula ausente, etc (sem mudança de código)
- **refactor**: Refatoração de código (sem adicionar funcionalidade ou corrigir bugs)
- **test**: Adição ou correção de testes
- **chore**: Mudanças em ferramentas, configurações, dependências

### Exemplos de Mensagens de Commit
```bash
feat: adiciona endpoint para classificação de carteira
fix: corrige validação de perfil de risco do cliente
docs: atualiza documentação da API de recomendações
style: formata código seguindo padrões do ESLint
refactor: reorganiza estrutura de pastas do projeto
test: adiciona testes unitários para serviço de classificação
chore: atualiza dependências do projeto
```

### Boas Práticas para Commits
- Mantenha a primeira linha com no máximo 50 caracteres
- Use o imperativo na descrição ("adiciona" ao invés de "adicionado")
- Seja específico e descritivo sobre a mudança
- Referencie issues quando aplicável: `feat: adiciona login (closes #123)`

### Nomenclatura de Branches
- **Feature**: `feature/nome-da-funcionalidade`
  - Exemplo: `feature/authentication-system`
  - Exemplo: `feature/client-dashboard`
- **Bugfix**: `fix/nome-do-bug`
  - Exemplo: `fix/login-validation-error`
  - Exemplo: `fix/database-connection-timeout`
- **Documentação**: `docs/nome-da-documentacao`
  - Exemplo: `docs/api-documentation`
  - Exemplo: `docs/installation-guide`
- **Hotfix**: `hotfix/nome-da-correcao-critica`
  - Exemplo: `hotfix/security-vulnerability`

### Estrutura de Pull Requests
Todos os Pull Requests devem incluir:

#### Template de PR
```markdown
## O que foi alterado
- Descrição clara das mudanças implementadas
- Lista de funcionalidades adicionadas/modificadas

## Por que foi alterado
- Justificativa técnica ou de negócio
- Referência a issues relacionadas (#123)

## Como testar
- Passos para reproduzir e validar as mudanças
- Comandos específicos para execução de testes
- Cenários de teste recomendados

## Screenshots (se aplicável)
- Capturas de tela para mudanças visuais
- Antes e depois para comparação

## Checklist
- [ ] Código testado localmente
- [ ] Testes unitários passando
- [ ] Documentação atualizada
- [ ] Sem conflitos de merge
```

# 5. Ferramentas e Tecnologias

- Backend: Utilize .NET com C#.
- Frontend: Utilize React Native com TypeScript.

# 6. Infraestrutura como Código
<!-- TODO -->
- Uso obrigatório de Docker para padronizar ambientes de desenvolvimento e produção.
- Incluir na raiz do repositório:
  - Dockerfile(s) para build de aplicações e serviços.
  - .dockerignore para excluir arquivos desnecessários do contexto de build.
  - docker-compose.yml para orquestrar múltiplos serviços/containers localmente.
- Cada serviço/contêiner deve ter seu próprio Dockerfile customizável, se necessário.
- O Docker Compose deve funcionar para rodar toda a stack do sistema localmente com um único comando (docker-compose up).

# 7. Monitoramento e Observabilidade

## 7.1 Ferramentas de Monitoramento
- **Prometheus**: Coleta de métricas de aplicação e infraestrutura
- **Grafana**: Visualização de métricas e criação de dashboards
- **ELK Stack (Elasticsearch, Logstash, Kibana)**: Centralização e análise de logs

## 7.2 Métricas Monitoradas
- **Performance**: Latência de APIs, tempo de resposta, throughput
- **Recursos**: Uso de CPU, memória, disco e rede
- **Aplicação**: Taxa de erros, número de requisições, tempo de processamento
- **Negócio**: Número de classificações realizadas, recomendações geradas

## 7.3 Alertas e Notificações
- **Critérios de Alerta**:
  - Tempo de resposta > 500ms
  - Taxa de erro > 5%
  - Uso de CPU > 80%
  - Uso de memória > 85%
- **Canais de Notificação**: Email, Slack, PagerDuty

# 8. Estratégias de Deploy Blue-Green

## 8.1 Conceito
- **Blue Environment**: Ambiente de produção atual
- **Green Environment**: Novo ambiente com a versão atualizada
- **Switch**: Redirecionamento de tráfego do Blue para Green após validação

## 8.2 Processo de Deploy
1. **Preparação**: Deploy da nova versão no ambiente Green
2. **Validação**: Testes de smoke e health checks no Green
3. **Switch**: Redirecionamento gradual do tráfego
4. **Monitoramento**: Acompanhamento de métricas pós-deploy
5. **Rollback**: Retorno ao Blue em caso de problemas

## 8.3 Ferramentas
- **Kubernetes**: Deployments e Services para gerenciar ambientes
- **Load Balancer**: Nginx ou AWS ALB para controle de tráfego
- **CI/CD**: Azure DevOps ou GitHub Actions para automação

# 9. Disaster Recovery

## 9.1 Estratégias de Backup
- **Banco de Dados**: Backup automático diário com retenção de 30 dias
- **Código**: Versionamento no Git com múltiplos repositórios remotos
- **Configurações**: Backup de variáveis de ambiente e configurações

## 9.2 Plano de Recuperação
- **RTO (Recovery Time Objective)**: 4 horas
- **RPO (Recovery Point Objective)**: 1 hora
- **Procedimentos**:
  1. Identificação do incidente
  2. Ativação do plano de contingência
  3. Restauração de dados e serviços
  4. Validação da recuperação
  5. Comunicação aos stakeholders

## 9.3 Testes de Disaster Recovery
- **Frequência**: Trimestral
- **Escopo**: Simulação de falhas críticas
- **Documentação**: Registro de resultados e melhorias

# 10. Registro de Variáveis de Ambiente

- Obrigatório manter um arquivo .env.example documentando todas as variáveis de ambiente exigidas pela aplicação.
- O arquivo deve listar todas as keys (sem valores reais/sensíveis) e breve explicação de cada variável.
- Todos os membros devem usar esse exemplo para criar seus próprios arquivos .env locais.
- Exemplo:
  ```bash
  # Configurações do Banco de Dados
  DB_HOST=localhost # Endereço do banco de dados
  DB_PORT=5432 # Porta do PostgreSQL
  DB_NAME=pati_db # Nome do banco de dados
  DB_USER=postgres # Usuário do banco
  DB_PASS= # Senha do banco (NUNCA commitada)

  # Configurações da Aplicação
  SECRET_KEY= # Chave secreta do sistema
  JWT_SECRET= # Chave para assinatura de tokens JWT
  API_PORT=3000 # Porta da API

  # Configurações de Monitoramento
  PROMETHEUS_ENDPOINT= # Endpoint do Prometheus
  LOG_LEVEL=info # Nível de log (debug, info, warn, error)
  ```