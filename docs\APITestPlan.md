<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="./assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>

# Planejamento de Testes de APIs - Projeto PATI

## Sumário
- [Visão Geral](#visão-geral)
- [APIs Testadas](#apis-testadas)
- [Cenários de Teste](#cenários-de-teste)
- [Cenários de Erro](#cenários-de-erro)
- [Ferramentas](#ferramentas)
- [Execução](#execução)

## Visão Geral

Este documento descreve o planejamento de testes para as APIs externas utilizadas no projeto PATI, incluindo cenários de sucesso, falha e tratamento de erros específicos.

## APIs Testadas

### 1. Supabase Auth
- **Endpoint**: `https://seu-projeto.supabase.co/auth/v1`
- **Propósito**: Autenticação de usuários
- **Métodos**: POST /signup, POST /signin, POST /signout

### 2. RandomUser API
- **Endpoint**: `https://randomuser.me/api/`
- **Propósito**: Geração de dados fictícios de clientes
- **Métodos**: GET com parâmetros de filtro

## Cenários de Teste

### Supabase Auth - Cenários de Sucesso

#### Teste 1: Login Válido
```json
{
  "endpoint": "/auth/v1/token?grant_type=password",
  "method": "POST",
  "payload": {
    "email": "<EMAIL>",
    "password": "senha123"
  },
  "expected_status": 200,
  "expected_response": {
    "access_token": "string",
    "token_type": "bearer",
    "expires_in": 3600
  }
}
```

#### Teste 2: Cadastro Válido
```json
{
  "endpoint": "/auth/v1/signup",
  "method": "POST",
  "payload": {
    "email": "<EMAIL>",
    "password": "senha123"
  },
  "expected_status": 200,
  "expected_response": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    }
  }
}
```

### RandomUser API - Cenários de Sucesso

#### Teste 3: Busca de Usuário Único
```json
{
  "endpoint": "/api/?results=1&nat=br",
  "method": "GET",
  "expected_status": 200,
  "expected_response": {
    "results": [
      {
        "name": {
          "first": "string",
          "last": "string"
        },
        "email": "string",
        "phone": "string"
      }
    ]
  }
}
```

## Cenários de Erro

### Supabase Auth - Cenários de Erro

#### Erro 1: Token Inválido
- **Cenário**: Tentativa de acesso com token expirado ou inválido
- **Endpoint**: Qualquer endpoint protegido
- **Headers**: `Authorization: Bearer token_invalido`
- **Status Esperado**: `401 Unauthorized`
- **Resposta Esperada**:
```json
{
  "error": "invalid_token",
  "error_description": "The access token provided is expired, revoked, malformed, or invalid"
}
```

#### Erro 2: Credenciais Inválidas
- **Cenário**: Login com email/senha incorretos
- **Endpoint**: `/auth/v1/token?grant_type=password`
- **Payload**:
```json
{
  "email": "<EMAIL>",
  "password": "senha_errada"
}
```
- **Status Esperado**: `400 Bad Request`
- **Resposta Esperada**:
```json
{
  "error": "invalid_grant",
  "error_description": "Invalid login credentials"
}
```

#### Erro 3: Email Já Cadastrado
- **Cenário**: Tentativa de cadastro com email existente
- **Endpoint**: `/auth/v1/signup`
- **Status Esperado**: `422 Unprocessable Entity`
- **Resposta Esperada**:
```json
{
  "error": "signup_disabled",
  "error_description": "User already registered"
}
```

### RandomUser API - Cenários de Erro

#### Erro 4: Parâmetro Inválido
- **Cenário**: Requisição com parâmetros inválidos
- **Endpoint**: `/api/?results=abc&nat=invalid`
- **Status Esperado**: `400 Bad Request`
- **Resposta Esperada**:
```json
{
  "error": "Invalid parameters provided"
}
```

#### Erro 5: Limite de Rate Excedido
- **Cenário**: Muitas requisições em pouco tempo
- **Status Esperado**: `429 Too Many Requests`
- **Headers Esperados**: `Retry-After: 60`

#### Erro 6: Serviço Indisponível
- **Cenário**: API temporariamente fora do ar
- **Status Esperado**: `503 Service Unavailable`
- **Tratamento**: Implementar retry com backoff exponencial

## Ferramentas

### Ferramentas de Teste
- **Postman**: Testes manuais e coleções automatizadas
- **Newman**: Execução de coleções via linha de comando
- **Jest**: Testes unitários para funções de integração
- **VCR.js**: Gravação e replay de requisições HTTP

### Configuração de Ambiente
```bash
# Variáveis de ambiente para testes
SUPABASE_URL=https://seu-projeto.supabase.co
SUPABASE_ANON_KEY=sua_chave_anonima
RANDOMUSER_BASE_URL=https://randomuser.me/api
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=senha_teste_123
```

## Execução

### Testes Manuais
```bash
# Executar coleção do Postman
newman run PATI_API_Tests.postman_collection.json -e test_environment.json

# Gerar relatório HTML
newman run PATI_API_Tests.postman_collection.json -e test_environment.json -r html
```

### Testes Automatizados
```bash
# Testes de integração com Jest
npm test -- --testPathPattern=integration

# Testes com VCR cassettes
npm test -- --testPathPattern=vcr
```

### Monitoramento Contínuo
- **Frequência**: Execução a cada commit e deploy
- **Alertas**: Notificação em caso de falha nos testes críticos
- **Métricas**: Taxa de sucesso, tempo de resposta, disponibilidade

## Critérios de Aceitação

### Performance
- Tempo de resposta < 2 segundos para 95% das requisições
- Taxa de sucesso > 99% para APIs críticas

### Confiabilidade
- Implementação de retry para falhas temporárias
- Fallback para dados mockados em caso de indisponibilidade
- Logs detalhados para troubleshooting

### Segurança
- Validação de tokens em todas as requisições protegidas
- Não exposição de credenciais em logs
- Implementação de rate limiting
