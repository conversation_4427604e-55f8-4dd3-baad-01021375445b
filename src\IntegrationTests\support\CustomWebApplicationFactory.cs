using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;

public class CustomWebApplicationFactory : WebApplicationFactory<Program>
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, config) =>
        {
            // Aqui você pode sobrescrever variáveis de ambiente se quiser
            // config.AddEnvironmentVariables();
        });
        // Não precisa mexer em DbContext!
    }
}