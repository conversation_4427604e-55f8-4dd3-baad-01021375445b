namespace Pati.ClassificationService.Infrastructure.Models
{
    /// <summary>
    /// Opções de configuração para o modelo SVD
    /// </summary>
    public class SVDModelOptions
    {
        public const string SectionName = "SVDModel";

        /// <summary>
        /// Caminho para o arquivo do modelo SVD treinado
        /// </summary>
        public string ModelPath { get; set; } = "/app/models/modelo_svd_treinado.pkl";

        /// <summary>
        /// Threshold mínimo de adequação
        /// </summary>
        public double MinSuitabilityThreshold { get; set; } = 0.5;

        /// <summary>
        /// Número padrão de recomendações
        /// </summary>
        public int DefaultRecommendationCount { get; set; } = 5;

        /// <summary>
        /// Habilita cache do modelo
        /// </summary>
        public bool EnableModelCaching { get; set; } = true;
    }
}
