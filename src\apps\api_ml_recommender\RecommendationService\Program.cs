using ApiMlRecommender.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Configuration.AddEnvironmentVariables();

// Configuração do Swagger para documentação da API
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { 
        Title = "API ML Recommender", 
        Version = "v1",
        Description = "API para recomendações de investimentos baseada em Machine Learning"
    });
});

// Registra os serviços personalizados
builder.Services.AddSingleton<MongoDbService>();
builder.Services.AddSingleton<PostgreSqlService>();
builder.Services.AddScoped<IRecomendacaoService, RecomendacaoService>();

// Configuração de CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "API ML Recommender V1");
    c.RoutePrefix = string.Empty;
});

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthorization();
app.MapControllers();

// Endpoint de health check
app.MapGet("/health", () => new { 
    Status = "Healthy", 
    Timestamp = DateTime.Now,
    Service = "API ML Recommender"
});

app.Run();

// Necessário para testes de integração com WebApplicationFactory
public partial class Program { }