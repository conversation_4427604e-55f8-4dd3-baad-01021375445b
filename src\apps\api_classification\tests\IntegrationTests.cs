using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Pati.ClassificationService.DTOs;
using Pati.ClassificationService.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Xunit;

namespace Pati.ClassificationService.Tests
{
    /// <summary>
    /// Testes de integração para o api_classification
    /// </summary>
    public class IntegrationTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public IntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // Remove the existing DbContext registration
                    var descriptor = services.BuildServiceProvider()
                        .GetService<DbContextOptions<ClassificationDbContext>>();
                    if (descriptor != null)
                    {
                        services.Remove(new ServiceDescriptor(typeof(DbContextOptions<ClassificationDbContext>), descriptor));
                    }

                    // Add in-memory database for testing
                    services.AddDbContext<ClassificationDbContext>(options =>
                    {
                        options.UseInMemoryDatabase("TestDb");
                    });

                    // Build the service provider
                    var sp = services.BuildServiceProvider();

                    // Create a scope to obtain a reference to the database context
                    using var scope = sp.CreateScope();
                    var scopedServices = scope.ServiceProvider;
                    var db = scopedServices.GetRequiredService<ClassificationDbContext>();
                    var logger = scopedServices.GetRequiredService<ILogger<IntegrationTests>>();

                    // Ensure the database is created
                    db.Database.EnsureCreated();

                    try
                    {
                        // Seed the database with test data
                        SeedDatabase(db);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "An error occurred seeding the database with test messages. Error: {Message}", ex.Message);
                    }
                });
            });

            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task ClassifyPortfolio_ValidInput_ReturnsSuccess()
        {
            // Arrange
            var portfolioInput = new PortfolioInputDto
            {
                AccountId = 1,
                PortfolioId = "test-portfolio-integration",
                PortfolioProfile = "Moderado",
                Assets = new List<AssetInputDto>
                {
                    new AssetInputDto
                    {
                        AssetId = "ASSET_INT_001",
                        Name = "Integration Test Asset",
                        Type = "bond",
                        IncomeType = "Renda Fixa",
                        InvestmentProfile = "Conservador",
                        Quantity = 100,
                        Value = 10000
                    }
                }
            };

            var json = JsonSerializer.Serialize(portfolioInput, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/internal/v1/portfolios/classify", content);

            // Assert
            Assert.True(response.IsSuccessStatusCode);
            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<PortfolioOutputDto>(responseContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            Assert.NotNull(result);
            Assert.Equal(portfolioInput.AccountId, result.AccountId);
            Assert.Equal(portfolioInput.PortfolioId, result.PortfolioId);
        }

        [Fact]
        public async Task ClassifyAllPortfolios_ReturnsSuccess()
        {
            // Act
            var response = await _client.PostAsync("/internal/v1/portfolios/classify-all", null);

            // Assert
            Assert.True(response.IsSuccessStatusCode);
            var responseContent = await response.Content.ReadAsStringAsync();
            Assert.Contains("Batch classification completed successfully", responseContent);
        }

        [Fact]
        public async Task GetPortfolioStatus_ValidId_ReturnsStatus()
        {
            // Act
            var response = await _client.GetAsync("/internal/v1/portfolios/test-portfolio/status");

            // Assert
            Assert.True(response.IsSuccessStatusCode);
            var responseContent = await response.Content.ReadAsStringAsync();
            Assert.NotEmpty(responseContent);
        }

        [Fact]
        public async Task GetPortfolioDetails_ValidId_ReturnsDetails()
        {
            // Act
            var response = await _client.GetAsync("/internal/v1/portfolios/test-portfolio/details");

            // Assert
            Assert.True(response.IsSuccessStatusCode);
            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<PortfolioOutputDto>(responseContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            Assert.NotNull(result);
            Assert.Equal("test-portfolio", result.PortfolioId);
        }

        [Fact]
        public async Task GetNonCompliantClients_ReturnsClientList()
        {
            // Act
            var response = await _client.GetAsync("/internal/v1/portfolios/non-compliant-clients");

            // Assert
            Assert.True(response.IsSuccessStatusCode);
            var responseContent = await response.Content.ReadAsStringAsync();
            Assert.NotEmpty(responseContent);
        }

        private static void SeedDatabase(ClassificationDbContext context)
        {
            // Add test data
            var advisor = new Domain.Advisor(1, "advisor_test", "Test Advisor", "<EMAIL>", "123456789");
            context.Advisors.Add(advisor);

            var client = new Domain.Client
            {
                ClientId = 1,
                Name = "Integration Test Client",
                Email = "<EMAIL>",
                PhoneNumber = "123456789",
                RiskProfileForm = "Moderado",
                RiskProfileWallet = "Moderado",
                AdvisorId = 1,
                NonCompliance = false // false significa que está em compliance
            };
            context.Clients.Add(client);

            var investment = new Domain.Investment
            {
                InvestmentId = 1,
                Name = "Test Bond",
                Type = "bond",
                Risk = "Conservador",
                Price = 100m,
                LastPriceUpdate = DateTime.UtcNow
            };
            context.Investments.Add(investment);

            var clientInvestment = new Domain.ClientInvestment
            {
                ClientId = 1,
                InvestmentId = 1,
                Quantity = 10,
                InvestedAmount = 1000m,
                InvestmentDate = DateTime.UtcNow
            };
            context.ClientInvestments.Add(clientInvestment);

            context.SaveChanges();
        }
    }
}
